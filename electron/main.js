const {
  app,
  BrowserWindow,
  Menu,
  screen,
  dialog,
  ipcMain,
  globalShortcut,
} = require("electron");
const path = require("path");
const fs = require("fs");
const https = require("https");
const { autoUpdater } = require("electron-updater");
const { exec } = require("child_process");
const os = require("os");
const iconv = require("iconv-lite");
const packageJson = require(path.join(app.getAppPath(), "package.json"));
const axios = require("axios");

autoUpdater.autoDownload = false;

autoUpdater.autoInstallOnAppQuit = false;
const log = require("electron-log");
const cmdPath = app.isPackaged
  ? path.join(app.getPath("exe"), "../teap")
  : "./teap";
const cmdBpaPath = app.isPackaged
  ? path.join(app.getPath("exe"), "../teap/tscan")
  : "./teap/tscan";
log.transports.file.maxSize = 5 * 1024 * 1024;
log.transports.file.level = "info";
log.transports.file.format =
  "[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}]{scope} {text}";
app.commandLine.appendSwitch("high-dpi-support", 1);
app.commandLine.appendSwitch("force-device-scale-factor", 1);
let mainWindow, loadingWindow;
let WritePermission = false;
const NODE_ENV = packageJson.electron_ENV;
let port = 20426;
let webConfig = {};
let updateType = "auto_update";
let filepath;
if (app.isPackaged) {
} else {
  Object.defineProperty(app, "isPackaged", {
    get() {
      return true;
    },
  });
  autoUpdater.updateConfigPath = path.join(__dirname, "../app-update.yml");
  log.transports.file.resolvePathFn = () => path.join(__dirname, "../teap.log");
}
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit();
} else {
  if (process.argv && process.argv.length > 1) {
    filepath =
      process.argv[process.argv.length - 1] == "."
        ? undefined
        : process.argv[process.argv.length - 1];
  }
  app.on("second-instance", (event, commandLine, workingDirectory) => {
    log.info("second-instance", event, commandLine, workingDirectory);
    if (commandLine[commandLine.length - 1].includes(".")) {
      mainWindow.webContents.send(
        "open_file_from_electron",
        commandLine[commandLine.length - 1]
      );
    }
    if (mainWindow.isMinimized()) mainWindow.restore();
    mainWindow.focus();
  });
}

autoUpdater.on("error", (err) => {
  log.error("error", err);
  mainWindow.webContents.send("update_error");
});

autoUpdater.on("checking-for-update", (res) => {
  log.info("checking-for-update", res);
});

autoUpdater.on("update-not-available", (res) => {
  log.info("update-not-available", res);
  mainWindow.webContents.send("update_not_find", res, updateType);
});

autoUpdater.on("update-available", (res) => {
  log.info("found new version", res);
  if (updateType == "handle_update") {
    autoUpdater.downloadUpdate();
  } else if (updateType == "auto_update") {
    mainWindow.webContents.send("update_find", res);
  } else {
    mainWindow.webContents.send("update_find", res, updateType);
  }
});

autoUpdater.on("download-progress", (progress) => {
  log.info("下载进度", progress);
  mainWindow.setProgressBar(progress.percent / 100);
  mainWindow.webContents.send("upadate_progress", progress);
});

autoUpdater.on("update-downloaded", (res) => {
  log.info("update-downloaded", res);
  mainWindow.setProgressBar(-1);
  mainWindow.webContents.send("upadate_finish");
});

function checkWritePermission(dirPath) {
  return new Promise((resolve) => {
    const tempFile = path.join(dirPath, `tempfile-${Date.now()}.tmp`);

    fs.writeFile(tempFile, "test", (err) => {
      if (fs.existsSync(tempFile)) {
        fs.unlinkSync(tempFile);
      }

      if (err) {
        resolve(false);
      } else {
        resolve(true);
      }
    });
  });
}
const checkPortAvailable = (val) => {
  const port = val;
  return new Promise((resolve, reject) => {
    require("child_process").exec(
      `netstat -ano |findstr ${port}`,
      (error, stdout, stderr) => {
        if (error) {
          resolve(1);
          return;
        }
        if (stdout || stderr) {
          resolve(0);
          return;
        }
        resolve(1);
      }
    );
  });
};
const findPort = () => {
  return new Promise(async (resolve, reject) => {
    let testPort = 20000;
    while ((await checkPortAvailable(testPort)) == 0) {
      testPort++;
    }
    resolve(testPort);
  });
};
const waitPortStart = (port) => {
  return new Promise((resolve, reject) => {
    async function loop(resolve) {
      if ((await checkPortAvailable(port)) == 0) {
        resolve(1);
      } else {
        setTimeout(() => {
          loop(resolve);
        }, 1000);
      }
    }
    loop(resolve);
  });
};
function transferLog(str) {
  const regex = /teap_critical_error >>([\s\S]*?)<< teap_critical_error/g;
  const match = regex.exec(str);
  if (match) {
    return match[1];
  } else {
    return false;
  }
}
function startServerWithSpawn() {
  log.info("启动后台服务");
  const cmdStr = "teap.exe";
  runStartServerExec(cmdStr);
  function runStartServerExec(cmdStr) {
    const startServerProcess = require("child_process").spawn(cmdStr, [port], {
      cwd: cmdPath,
    });

    startServerProcess.stdout.on("data", function (data) {
      if (transferLog(iconv.decode(data, "gbk"))) {
        showErrorDialog(transferLog(iconv.decode(data, "gbk")));
      }
      log.info("stdout:" + iconv.decode(data, "gbk"));
    });
    startServerProcess.on("error", (err) => {
      log.error(err);
      showErrorDialog("程序启动错误！");
    });

    startServerProcess.stderr.on("data", function (data) {
      log.error("stderr:" + iconv.decode(data, "gbk"));
    });

    startServerProcess.on("close", function (code) {
      log.info("out code:" + code);
    });
  }
}
function closeServerWithSpawn() {
  log.info("关闭后台服务");
  const cmdStr = "teap_stop.bat";
  runCloseServerExec(cmdStr);
  function runCloseServerExec(cmdStr) {
    const closeServerProcess = require("child_process").spawn(cmdStr, {
      cwd: cmdPath,
      shell: true,
    });
    closeServerProcess.stdout.on("data", function (data) {
      log.info("closeServerWithSpawn stdout:" + iconv.decode(data, "gbk"));
    });
    closeServerProcess.stderr.on("data", function (data) {
      log.error("closeServerWithSpawn stderr:" + iconv.decode(data, "gbk"));
    });
    closeServerProcess.on("close", function (code) {
      log.info("closeServerWithSpawn out code:" + code);
    });
  }
}
const showErrorDialog = (message) => {
  const options = {
    type: "error",
    noLink: true,
    title: "启动出错",
    message: message || "后端服务启动错误",
    defaultId: 0,
    buttons: ["确定"],
  };
  dialog.showMessageBox(options).then((res) => {
    if (res.response === 0) {
      loadingWindow.close();
    }
  });
};
const showLoading = () => {
  let width, height, loadingHtml;
  if (
    packageJson.productName == "TEAPZJ" ||
    packageJson.productName == "TEAP"
  ) {
    width = 965;
    height = 903;
    loadingHtml = `loadingStart-teap`;
  } else {
    width = 948;
    height = 651;
    loadingHtml = `loadingStart-prsas`;
  }
  return new Promise((resolve, reject) => {
    loadingWindow = new BrowserWindow({
      show: false,
      width,
      height,
      frame: false,
      transparent: true,
      webPreferences: {
        preload: path.join(__dirname, "loadingStart.js"),
        nodeIntegration: true,
      },
    });
    fs.readdir(cmdPath, function (err, files) {
      if (err) {
      }
      loadingWindow.webContents.send("files", files);
    });
    loadingWindow.webContents.send(
      "version",
      app.getVersion(),
      os.arch(),
      packageJson.date
    );
    if (NODE_ENV === "development") {
      loadingWindow.loadFile(
        path.join(__dirname, `../public/${loadingHtml}.html`)
      );
    } else {
      loadingWindow.loadFile(`dist/${loadingHtml}.html`);
    }
    loadingWindow.once("ready-to-show", () => {
      loadingWindow.show();
      resolve();
    });
  });
};
function createWindow() {
  Menu.setApplicationMenu(null);
  const { width, height } = screen.getPrimaryDisplay().workAreaSize;

  mainWindow = new BrowserWindow({
    width: parseInt(width * 0.85),
    height: parseInt(width * 0.85 * (9 / 16)),
    icon: path.join(__dirname, "../dist/teap.ico"),
    frame: false,
    show: false,

    webPreferences: {
      preload: path.join(__dirname, "preload.js"),
      nodeIntegration: true,
    },
  });
  if (NODE_ENV === "development") {
    mainWindow.loadURL("http://localhost:3000");
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile("dist/index.html");
  }
  mainWindow.webContents.send("port", port, app.getVersion(), webConfig);
  mainWindow.once("ready-to-show", () => {
    loadingWindow.close();
    mainWindow.show();
  });
  mainWindow.webContents.on("did-finish-load", () => {
    setTimeout(() => {
      mainWindow.webContents.send("open_file_from_electron", filepath);
      autoUpdater.checkForUpdatesAndNotify();
    }, 500);
  });

  mainWindow.on("will-resize", resizeWindow);
  function resizeWindow() {
    mainWindow.setAspectRatio(16 / 9);
  }
  globalShortcut.register("Ctrl+F12", function () {
    mainWindow.webContents.openDevTools();
  });
  globalShortcut.register("Ctrl+F5", function () {
    updateType = "auto_update";
    mainWindow.reload();
  });
  mainWindow.webContents.on("render-process-gone", (event, details) => {
    log.error("render-process-gone:", details);
  });
  mainWindow.webContents.on(
    "did-fail-load",
    (event, errorCode, errorDescription) => {
      log.error("did-fail-load errorCode:", errorCode);
      log.error("did-fail-load errorDescription:", errorDescription);
    }
  );
}
const testUpdate = () => {
  https
    .get("https://store.teap.online/pro/common/prsas/latest.yml", (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        console.log(data);
      });
    })
    .on("error", (err) => {
      console.error("HTTP 请求出错:", err);
    });
};
const getWebConfig = async () => {
  try {
    webConfig = (await axios.post(`http://127.0.0.1:${port}/backend/teap_api/`))
      .data;
  } catch (error) {
    log.error("请求失败:", error);
  }
};
ipcMain.handle("save_file", async (event, file_path) => {
  try {
    const data = await new Promise((resolve, reject) => {
      fs.readFile(file_path, async (err, data) => {
        if (err) {
          log.error("error save_file", err);
          reject(err);
        } else {
          resolve(data);
        }
      });
    });
    const { canceled, filePath } = await dialog.showSaveDialog({
      title: "Save File",
      defaultPath: file_path,
    });
    if (!canceled) {
      const isSave = await new Promise((resolve, reject) => {
        fs.writeFile(filePath, data, (err) => {
          if (err) {
            log.error("fs.writeFile", err);
            resolve(0);
          } else {
            resolve(1);
          }
        });
      });
      if (isSave) {
        log.info("File saved successfully:", filePath);
        return {
          filePath: filePath,
          fileName: path.basename(filePath),
        };
      } else {
        return undefined;
      }
    } else {
      return undefined;
    }
  } catch (error) {
    mainWindow.webContents.send("electron_error", error);
    log.error("save_file", error);
  }
});
ipcMain.on("install_update_later", (event, val) => {
  const path =
    `${app.getPath("home")}\\AppData\\Local\\PRSAS_Updater\\pending\\` + val;
  if (fs.existsSync(path)) {
    exec(path, (error, stdout, stderr) => {
      if (error) {
        log.error(`exec error: ${error}`);
      }
      log.info(`stdout: ${stdout}`);
      log.error(`stderr: ${stderr}`);
    });
    setTimeout(() => {
      app.quit();
    }, 1800);
  } else {
    mainWindow.webContents.send("install_error");
  }
});
ipcMain.on("bpaScan", (event, id) => {
  const cmdStr = "tscan.exe";
  runBpaExec(cmdStr);
  function runBpaExec(cmdStr) {
    const startBpaProcess =
      id === undefined
        ? require("child_process").spawn(cmdStr, [], { cwd: cmdBpaPath })
        : require("child_process").spawn(cmdStr, [port, id], {
            cwd: cmdBpaPath,
          });
    // 启动成功的输出
    startBpaProcess.stdout.on("data", function (data) {
      log.info("stdout:" + iconv.decode(data, "gbk"));
      if (id === undefined) {
        mainWindow.webContents.send("bpaScan_ready");
      }
    });
    startBpaProcess.on("error", (err) => {
      log.error(err);
      showErrorDialog("tscan启动错误！");
    });
    // 发生错误的输出
    startBpaProcess.stderr.on("data", function (data) {
      log.error("stderr:" + iconv.decode(data, "gbk"));
    });
    // 退出后的输出
    startBpaProcess.on("close", function (code) {
      log.info("out code:" + code);
    });
  }
});
ipcMain.on("will_download", (event) => {
  mainWindow.webContents.session.once(
    "will-download",
    (event, item, webContents) => {
      item.on("updated", (event, state) => {
        if (state === "interrupted") {
          log.info("下载已经中断");
        } else if (state === "progressing") {
          log.info(
            `下载中: ${item.getReceivedBytes()} , 进度: ${
              item.getReceivedBytes() / item.getTotalBytes()
            }`
          );
        }
      });
      item.on("done", (event, state) => {
        if (state === "completed") {
          log.info(`下载完成: ${item.getSavePath()}`);
          mainWindow.webContents.send("download_finished", {
            filePath: item.getSavePath(),
            fileName: path.basename(item.getSavePath()),
          });
        } else {
          log.info(`下载失败: ${state}`);
        }
      });
    }
  );
});
ipcMain.on("check_update", (event, val) => {
  updateType = val;
  autoUpdater.checkForUpdates();
});
ipcMain.on("install_update", () => {
  autoUpdater.quitAndInstall();
});
ipcMain.on("download_update", () => {
  autoUpdater.downloadUpdate();
});
ipcMain.handle("openFolder", async (event, data) => {
  const { canceled, filePaths } = await dialog.showOpenDialog({
    properties: ["openDirectory"],
  });
  if (!canceled) {
    return filePaths[0];
  }
});
ipcMain.handle("getPosition", () => {
  return mainWindow.getPosition();
});
ipcMain.on("move-window", (event, { x, y, width, height }) => {
  mainWindow.setPosition(x, y);
  // mainWindow.setBounds({ x, y, width, height })
});
ipcMain.on("normal-window", () => {
  // mainWindow.setFullScreen(false)
  if (mainWindow.isMaximized()) {
    mainWindow.unmaximize();
  }
});
ipcMain.on("maximize-window", () => {
  mainWindow.maximize();
  // mainWindow.setFullScreen(true)
});
ipcMain.on("close-window", () => {
  mainWindow.close();
});
ipcMain.on("reduce-window", () => {
  mainWindow.minimize();
});
ipcMain.on("addRecentDocument", (event, file_path) => {
  app.addRecentDocument(file_path);
});
app.whenReady().then(async () => {
  if (NODE_ENV == "product") {
    WritePermission = await checkWritePermission(
      path.dirname(app.getPath("exe"))
    );
    if (WritePermission && app.isPackaged) {
      let date = new Date();
      date =
        date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
      log.transports.file.resolvePathFn = () =>
        path.join(app.getPath("exe"), "../appLogs/" + date + ".log");
    }
    const filePath = path.join(app.getPath("exe"), "../download");
    app.setPath("downloads", filePath);
    if (process.argv && process.argv.length > 1) {
      port = Number(process.argv[process.argv.length - 1])
        ? Number(process.argv[process.argv.length - 1])
        : 20426;
    }
    await showLoading();
    const res = await checkPortAvailable(port);
    if (res !== 1) {
      port = await findPort();
    }
    startServerWithSpawn();
    await waitPortStart(port);
    await getWebConfig();
    createWindow();
  } else {
    await showLoading();
    // testUpdate()
    createWindow();
  }
  app.on("activate", function () {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});
app.on("window-all-closed", function () {
  log.info("window-all-closed");
  if (process.platform !== "darwin") {
    app.quit();
  }
});
app.on("will-quit", () => {
  log.info("will-quit");
  if (NODE_ENV == "product") closeServerWithSpawn();
  globalShortcut.unregisterAll();
});
app.on(
  "certificate-error",
  (event, webContents, url, error, certificate, callback) => {
    // if (url === 'https://github.com') {
    // 	// Verification logic.
    // 	event.preventDefault()
    // 	callback(true)
    // } else {
    // 	callback(false)
    // }
    log.error(
      "certificate-error",
      event,
      webContents,
      url,
      error,
      certificate,
      callback
    );
  }
);
// app.on('select-client-certificate', (event, webContents, url, list, callback) => {
// 	// event.preventDefault()
// 	// callback(list[0])
// 	log.info('select-client-certificate', event, webContents, url, list, callback)
// })
app.on("render-process-gone", (event, webContents, details) => {
  log.error("app:render-process-gone", event, webContents, details);
});
app.on("child-process-gone", (event, details) => {
  log.error("app:child-process-gone", event, details);
});
