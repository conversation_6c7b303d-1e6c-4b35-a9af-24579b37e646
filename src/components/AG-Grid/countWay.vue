<template>
  <a-modal
    wrapClassName="modal_count"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    width="375px"
    :maskClosable="false"
  >
    <div class="user-select">
      <div class="modal_top">
        <p>{{ $t('计算') }}</p>
        <close-outlined class="pointer" @click="emits('cancel')" />
      </div>
      <div class="modal_content relative">
        <a-input-number v-model:value="numericalValue" :style="{width:'100%'}">
          <template #addonBefore>
            <a-select v-model:value="addonBeforeValue" style="width: 60px">
              <a-select-option value="mul">×</a-select-option>
              <a-select-option value="div">÷</a-select-option>
              <a-select-option value="add">+</a-select-option>
              <a-select-option value="sub">−</a-select-option>
            </a-select>
          </template>
        </a-input-number>

        <div class="modal_btns">
          <a-button @click="handleClear"
            type="primary"
            ghost
            size="small"
            :disabled="numericalValue == null"
          >
            {{ $t('清空') }}
          </a-button>
          <a-button @click="handleCancel" size="small" :style="{margin:'0 17px'}">{{ $t('取消') }}</a-button>
          <a-button @click="handleOk" type="primary" :style="{color:'#fff'}" size="small">{{ $t('确认') }}</a-button>
        </div>
      </div>
      <!-- <div class="modal_btn">
        <a-button @click="handleClear" type="primary" ghost>清空</a-button>
        <a-button @click="handleCancel">取消</a-button>
        <a-button @click="handleOk" type="primary" :style="{color:'#fff'}">确认</a-button>
      </div> -->

    </div>
  </a-modal>
</template>
<script setup>
import { ref, reactive, defineEmits, onMounted } from 'vue'
import message from '@/utils/message'
import { t } from '@/utils/common'
const emits = defineEmits(['confirm', 'cancel'])

const numericalValue = ref(null)
const addonBeforeValue = ref('mul')

const state = reactive({
	visible: true
})

const handleOk = () => {
	if (addonBeforeValue.value == 'div' && numericalValue.value == 0) return message.warning(t('除数不可以为零'))

	emits('confirm', numericalValue.value, addonBeforeValue.value)
}

const handleCancel = () => {
	emits('cancel')
}
const closeModal = () => {
	emits('cancel')
}

const handleClear = () => {
	numericalValue.value = ''
}

onMounted(() => {

})

</script>
<style lang="scss" scoped>
  .modal_count{
    .ant-modal{
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 17px 35px;
            text-align: center;
            .ant-input-number .ant-input-number-input {
              width: 100%;
              height: 35px;
            }
          }
          .modal_btns{
            margin-top: 17px;
            text-align: center;
            button{
              width: 90px;
              height: 30px;
              letter-spacing: 0;
            }
          }

        }
      }
    }
  }
</style>
