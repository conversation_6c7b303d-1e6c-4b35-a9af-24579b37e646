<template>
  <a-modal
    wrapClassName="modal_sourceTime"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    :maskClosable="false"
  >
    <screen-scale>
      <div class="modal_top">
        <p>{{ $t(`${props.headerName}`)  }}</p>
        <close-outlined class="pointer" @click="closeModal" />
      </div>
      <div class="modal_content">
        <div class="transfer-title">
          <div>
            <a-checkbox
              v-model:checked="state.checkAll"
              :indeterminate="state.indeterminate"
              @change="onCheckAllChange"
            >
              {{ $t('全选') }}
            </a-checkbox>
            <a-checkbox
              v-if="props.col_source == 'timeseries'"
              v-model:checked="state.typeChecked"
              @change="onCheckTypeChange"
            >
              {{ $t('本表类型') }}
            </a-checkbox>
          </div>
          <div>{{ $t('已选择') }}</div>
        </div>
        <div class="transfer-box">
          <div>
            <div class="transfer-search">
              <a-input-search
                v-model:value="state.searchSource"
                :placeholder="$t('搜索')"
                style="width: 100%"
                @search="onSearchSource"
              />
            </div>
            <div class="transfer-content">
              <RecycleScroller
                v-slot="{ item }"
                ref="recycleScrollRef"
                class="RecycleScrollerClass"
                :item-size="34"
                :items="state.sourceOptions"
                key-field="key"
              >
                <a-col :span="24" >
                  <a-tooltip :mouseEnterDelay="0.5">
                    <template #title>{{ item.label }}</template>
                      <a-checkbox v-model:checked="item.checked" @change="onCheckChange(item)">{{ item.label }}</a-checkbox>
                  </a-tooltip>
                </a-col>
              </RecycleScroller>

            </div>
          </div>
          <div>
            <div class="transfer-search">
              <a-input-search
                v-model:value="state.searchTarget"
                :placeholder="$t('搜索')"
                style="width: 100%"
                @search="onSearchTarget"
              />
            </div>
            <div class="transfer-content">
              <RecycleScroller
                v-slot="{ item }"
                ref="recycleScrollRef1"
                class="RecycleScrollerClass"
                :item-size="34"
                :items="state.targetOptions"
                key-field="key"
              >
                <a-col :span="24">
                  <a-tooltip :mouseEnterDelay="0.5">
                    <template #title>{{ item.label }}</template>
                    <CloseSquareOutlined @click="removeItem(item)" /> {{ item.label }}
                  </a-tooltip>
                </a-col>
              </RecycleScroller>
            </div>
          </div>
        </div>

        <div class="modal_btns">
          <a-button v-if="props.col_source == 'timeseries'" @click="handleAdd" type="primary" :style="{color:'#fff'}" size="small">{{ $t('新建') }}</a-button>
          <a-button @click="handleClear"
            type="primary"
            ghost
            size="small"
            :disabled="state.sourceValue.length <= 0"
          >
            {{ $t('清空') }}
          </a-button>
          <a-button @click="closeModal" size="small">{{ $t('取消') }}</a-button>
          <a-button @click="handleOk" type="primary" :style="{color:'#fff'}" size="small">{{ $t('确认') }}</a-button>
        </div>
      </div>

    </screen-scale>
  </a-modal>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { reactive, defineEmits, onMounted } from 'vue'

import { useRoute } from 'vue-router'

import { getReadNameCol } from '@/api/exampleApi'

const props = defineProps({
	headerName: {
		type: String,
		default: ''
	},
	col_source: {
		type: String,
		default: ''
	},
	targetValue: {
		type: Array,
		default: () => []
	}
})

const emits = defineEmits(['cancel', 'confirm', 'add'])
const route = useRoute()

const state = reactive({
	visible: true,
	loading: false,
	typeChecked: true,
	isMultiple: 'multiple',
	allData: [],
	checkAll: false,
	indeterminate: false,
	searchSource: '',
	searchTarget: '',
	sourceValue: [],
	sourceOptions: [],
	targetOptions: [],
	treeType: ''
})
const onSearchSource = searchValue => {
	if (state.sourceValue.length == 0) {
		state.checkAll = false
		state.indeterminate = false
	} else if (state.sourceValue.length < state.allData.length) {
		state.checkAll = false
		state.indeterminate = true
	} else {
		state.checkAll = true
		state.indeterminate = false
	}

	state.sourceOptions = state.allData
	state.sourceOptions = state.sourceOptions.filter(item => item.label.toLowerCase().indexOf(searchValue.toLowerCase()) >= 0)
}

const onSearchTarget = searchValue => {
	state.targetOptions = state.allData.filter(item => state.sourceValue.includes(item.key)).filter(item => item.label.toLowerCase().indexOf(searchValue.toLowerCase()) >= 0)
}

const onCheckTypeChange = e => {
	getReadNameColData()
}

const onCheckAllChange = e => {
	state.sourceOptions.forEach(item => {
		item.checked = e.target.checked
	})
	state.allData.forEach(item => {
		if (state.sourceOptions.find(item1 => item1.key == item.key)) {
			item.checked = e.target.checked
		}
	})
	state.sourceValue = state.allData.filter(item => item.checked).map(item => item.key)
	state.targetOptions = state.allData.filter(item => item.checked && item.label.toLowerCase().indexOf(state.searchTarget.toLowerCase()) >= 0)

	state.indeterminate = false
}

const onCheckChange = (val) => {
	state.sourceOptions.forEach(item => {
		if (item.key == val.key) {
			item.checked = val.checked
		}
	})
	state.allData.forEach(item => {
		if (item.key == val.key) {
			item.checked = val.checked
		}
	})
	state.sourceValue = state.allData.filter(item => item.checked).map(item => item.key)
	state.targetOptions = state.allData.filter(item => item.checked && item.label.toLowerCase().indexOf(state.searchTarget.toLowerCase()) >= 0)

	changeIndeterminate()
}

const removeItem = (val) => {
	state.sourceOptions.forEach(item => {
		if (item.key == val.key) {
			item.checked = false
		}
	})
	state.allData.forEach(item => {
		if (item.key == val.key) {
			item.checked = false
		}
	})

	state.sourceValue = state.allData.filter(item => item.checked).map(item => item.key)
	state.targetOptions = state.allData.filter(item => item.checked && item.label.toLowerCase().indexOf(state.searchTarget.toLowerCase()) >= 0)

	changeIndeterminate()
}

const changeIndeterminate = () => {
	if (state.targetOptions.length == 0) {
		state.checkAll = false
		state.indeterminate = false
	} else if (state.targetOptions.length < state.allData.length) {
		state.checkAll = false
		state.indeterminate = true
	} else {
		state.checkAll = true
		state.indeterminate = false
	}
}

const handleAdd = () => {
	emits('add')
}

const handleOk = () => {
	emits('confirm', state.sourceValue)
}

const closeModal = () => {
	emits('cancel', props.targetValue)
}

const handleClear = () => {
	state.sourceOptions.forEach(item => {
		item.checked = false
	})
	state.allData.forEach(item => {
		item.checked = false
	})
	state.sourceValue = []
	state.targetOptions = []
	state.checkAll = false
	state.indeterminate = false
}

const handleRefresh = () => {
	getReadNameColData()
}
Mitt.on('handleRefresh', handleRefresh)

const getReadNameColData = () => {
	getReadNameCol({
		'import_string_func': 'teapcase:read_name_col_from_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'ts_relation_sheet_name': state.typeChecked ? state.treeType : '',
			'sheet_names': [props.col_source]
		}
	}).then(res => {
		if (res.code == 1) {
			const { data } = res.func_result
			const tempType = props.col_source == 'timeseries' ? 'timeseries_scenario' : props.col_source
			state.allData = data[tempType].map(item => {
				return {
					key: item.index,
					label: item.name,
					checked: props.targetValue.includes(item.index)
				}
			})
			state.relation_timeseries_data = data['relation_timeseries_scenario']
			if (props.col_source == 'timeseries' && state.typeChecked) {
				state.sourceOptions = state.allData.filter(item => state.relation_timeseries_data.some(item1 => item1.index == item.key))
			} else {
				state.sourceOptions = state.allData
			}
			if (props.targetValue.length > 0) {
				state.sourceValue = [...props.targetValue]
				state.targetOptions = state.allData.filter(item => state.sourceValue.includes(item.key))
				changeIndeterminate()
			}
		}
		state.loading = false
	})
}
onMounted(() => {
	state.loading = true
	state.treeType = sessionStorage.getItem('treeType')
	getReadNameColData()
})

</script>
<style lang="scss">
.modal_sourceTime{
  .ant-modal{
    width: auto!important;
    .ant-modal-body{
      >div{
        .RecycleScrollerClass {
          height: 345px;
          overflow-y: auto;
        }
        .modal_content{
          width: 720px;
          padding: 17px 25px;
          text-align: center;
        }
        .transfer-title {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 30px;
          >div{
            background: #fff;
            text-align: left;
            padding: 0 10px;
          }
          >div:first-child{
            display: flex;
            justify-content: space-between;
          }
        }

        .transfer-box {
          // width: 600px;
          display: grid;
          grid-template-columns: 1fr 1fr;
          // justify-content: space-between;
          >div{
            // width: 258px;
            height: 400px;
            padding: 5px 10px;
            box-sizing: border-box;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            .transfer-search {
              // padding: 0 10px
              height: 40px;
            }
            .transfer-content{
              height: 350px;
              background: #fff;
              text-align: left;
              white-space: nowrap;
              // overflow: auto;
            }

          }
          >div:first-child {
              margin-right: 15px;
            }
        }

        .modal_btns{
          margin-top: 17px;
          text-align: center;
          button{
            width: 90px;
            height: 30px;
            letter-spacing: 0;
            margin-left: 15px;
          }
        }

      }
    }
  }
}
</style>
