<template>
  <a-modal
    wrapClassName="modal_sourceTime"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    width="475px"
    :maskClosable="false"
  >
    <div class="user-select">
      <div class="modal_top">
        <p>{{ $t('新增时序') }}</p>
        <close-outlined class="pointer" @click="closeModal" />
      </div>
      <div class="modal_content relative">
        <a-form
          ref="formRef"
          :model="formState"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
          autocomplete="off"
        >
          <a-form-item
            :label="`${$t('名称')}`"
            name="name"
            :rules="[{ required: true, message: $t('请输入')+'!' }]"
          >
            <a-input v-model:value="formState.name" />
          </a-form-item>

          <a-form-item
            :label="`${$t('时序类型')}`"
            name="type"
            :rules="[{ required: true, message: $t('请输入')+'!' }]"
          >
            <a-select
              v-model:value="formState.type"
              show-search
              :options="state.options"
              :filter-option="filterOption"
            ></a-select>

          </a-form-item>

          <a-form-item
            :label="`${$t('数据类型')}`"
            name="value_type"
            :rules="[{ required: true, message: $t('请输入')+'!' }]"
          >
            <a-select v-model:value="formState.value_type">
              <a-select-option value="multiply">{{ $t('数据倍乘') }}</a-select-option>
              <a-select-option value="replace">{{ $t('数据替换') }}</a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item
            :label="`${$t('时间周期')}`"
            name="period"
            :rules="[{ required: true, message: $t('请输入')+'!' }]"
          >
            <a-select v-model:value="formState.period">
              <a-select-option value="逐时数据">{{ $t('逐时数据') }}</a-select-option>
              <a-select-option value="日×24时数据">{{ $t('日×24时数据') }}</a-select-option>
              <a-select-option value="月×24时数据">{{ $t('月×24时数据') }}</a-select-option>
              <a-select-option value="年×12月数据">{{ $t('年×12月数据') }}</a-select-option>
              <a-select-option value="自定义时间段">{{ $t('自定义时间段') }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            :label="`${$t('时间段数量')}`"
            name="periodNum"
            v-if="formState.period == '自定义时间段'"
          >
            <a-input-number id="inputNumber" v-model:value="formState.periodNum" :min="1" :max="365" :step="1" style="width: 100%"/>
          </a-form-item>
          <a-form-item
            :label="`${$t('计算场景')}`"
            name="scenario"
          >
            <a-input v-model:value="formState.scenario" />
          </a-form-item>

        </a-form>

        <div class="modal_btns">
          <a-button @click="handleClear"
            type="primary"
            ghost
            size="small"
            :disabled="sourceValue.length <= 0"
          >
            {{ $t('清空') }}
          </a-button>
          <a-button @click="closeModal" size="small" :style="{margin:'0 17px'}">{{ $t('取消') }}</a-button>
          <a-button @click="handleOk" type="primary" :style="{color:'#fff'}" size="small">{{ $t('确认') }}</a-button>
        </div>
      </div>

    </div>
  </a-modal>
</template>
<script setup>
import { ref, reactive, defineEmits, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getReadNameCol } from '@/api/exampleApi'

const route = useRoute()

const formRef = ref()

const emits = defineEmits(['cancel', 'confirm'])

const formState = reactive({
	name: '',
	type: '',
	value_type: '',
	scenario: '',
	period: '逐时数据',
	periodNum: null,
	index: -999999
})

const state = reactive({
	visible: true,
	isMultiple: 'multiple',
	options: []
})
const sourceValue = ref([])

const filterOption = (input, option) => {
	return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const handleOk = () => {
	formRef.value
		.validate()
		.then(() => {
			emits('confirm', formState)
		})
		.catch(error => {
			console.log('error', error)
		})
}

const closeModal = () => {
	emits('cancel')
}

const handleClear = () => {
	sourceValue.value = []
}

const getReadNameColData = (isItemized, treeNode) => {
	getReadNameCol({
		'import_string_func': 'teapcase:read_name_col_from_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_names': null
		}
	}).then(res => {
		if (res.code == 1) {
			const { ts_type } = res.func_result
			if (isItemized) {
				const treeNodeStr = treeNode.split('.')[0]
				state.options = ts_type.ts_type_config_dict[treeNodeStr]
				formState.type = state.options[0].value
			} else {
				state.options = ts_type.ts_type_config_list
				formState.type = state.options[0].options[0].value
			}
		}
	})
}

defineExpose({ getReadNameColData })

onMounted(() => {
	getReadNameColData()
})

</script>
<style lang="scss" scoped>
  .modal_sourceTime{
    .ant-modal{
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 17px 35px;
            text-align: left;
            .ant-input-number .ant-input-number-input {
              width: 100%;
              height: 35px;
            }
          }

          .modal_btns{
            margin-top: 17px;
            text-align: center;
            button{
              width: 90px;
              height: 30px;
              letter-spacing: 0;
            }
          }

        }
      }
    }
  }
</style>
