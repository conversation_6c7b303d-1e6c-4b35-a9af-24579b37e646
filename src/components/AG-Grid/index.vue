<template>

  <div class="ag-grid-body">
    <div class="ag-searchinput" v-if="props.isInputSearch">
      <span>{{ $t('搜索') }}：</span>
      <a-input v-model:value="searchValue" :placeholder="$t('请输入')" @change="onFilterTextBoxChanged()" :style="{'width': '200px'}" />
    </div>
    <ag-grid-vue
      :treeData="props.istree"
      :getDataPath="getDataPath"
      :style="tableStyle"
      class="ag-theme-quartz"
      :headerHeight="35"
      :rowHeight="30"
      :scrollbarWidth="scrollBarWidth"
      :columnDefs="columnDefs"
      @grid-ready="onGridReady"
      :defaultColDef="defaultColDef"
      :columnTypes="columnTypes"
      :rowData="rowData"
      :rowSelection="rowSelection"
      :rowMultiSelectWithClick="false"
      :suppressRowClickSelection="true"
      :enableRangeSelection="true"
      :undoRedoCellEditing="true"
      :enableCellChangeFlash="false"
      :rowDragManaged="false"
      :getRowStyle="getRowStyle"
      :autoGroupColumnDef="autoGroupColumnDef"
      :tooltipShowDelay="tooltipShowDelay"
      :localeText="localeText"
      :getRowId="getRowId"
      :statusBar="statusBar"
      :processCellForClipboard="processCellForClipboard"
      :processDataFromClipboard="processDataFromClipboard"
      @selection-changed="onSelectionChanged"
      @cellValueChanged="onCellValueChanged"
      @cellEditingStarted="onCellEditingStarted"
      @rowEditingStarted="onRowEditingStarted"
      @pasteEnd="onPasteEnd"
      @filterChanged="onFilterChanged"
      :showContextMenu="showContextMenu"
    ></ag-grid-vue>
  </div>

  <SourceModal v-if="state.sourceTimeVisible"
    :headerName="state.headerName"
    :col_source="state.col_source"
    :targetValue="state.sourceValue"
    @confirm="handleConfirmTime"
    @cancel="handleCancelTime"
    @add="timeseriesAdd(false)"
  />

  <SourceBusModal v-if="state.sourceVisible"
    @confirm="handleSaveSource"
    @cancel="state.sourceVisible = false"
  />

  <SourceCityModal v-if="state.sourceCityVisible"
    @confirm="handleCityConfirm"
    @cancel="state.sourceCityVisible = false"
  />

  <SourceStorageModal v-if="state.storageVisible"
    :sourceTitle="state.sourceTitle"
    :sourceVal="state.sourceVal"
    @confirm="handleSaveStorage"
    @cancel="state.storageVisible = false"
  ></SourceStorageModal>

  <SourceTypeModal v-if="state.typeVisible"
    ref="sourceTypeRef"
    @confirm="handleSaveType"
    @cancel="state.typeVisible = false"
  />

  <ReplaceModal v-if="state.replaceVisible"
    @confirm="handleSaveReplace"
    @cancel="state.replaceVisible = false"
  ></ReplaceModal>

   <CountWay v-if="state.countWayVisible"
    @confirm="handleSaveCount"
    @cancel="state.countWayVisible = false"
  ></CountWay>

  <TimeseriesDetail v-if="state.timeseriesVisible"
    ref="timeseriesRef"
    v-model:open="state.timeseriesVisible"
    @cancel="timeseriesDetailClose"
  />

  <TimeseriesQuote v-if="state.timeseriesQuoteVisible"
    ref="timeseriesQuoteRef"
    v-model:open="state.timeseriesQuoteVisible"
    @cancel="state.timeseriesQuoteVisible = false"
  />

  <TimeseriesForm v-if="state.timeseriesFormVisible"
    ref="timeseriesFormRef"
    @confirm="handleSaveTimeseriesAdd"
    @cancel="state.timeseriesFormVisible = false"
  />
  <TimeseriesAdd v-if="state.timeseriesAddVisible"
    ref="timeseriesAddRef"
    v-model:open="state.timeseriesAddVisible"
    @cancel="state.timeseriesAddVisible = false"
  />

   <type-change
  	v-if="state.typeChangeVisible"
	:data="state.typeChangeData"
	@confirm="confirmTypeChange"
    @close="state.typeChangeVisible = false"
   ></type-change>

  <delete-message v-if="state.deteleMessageVisible"
    ref="deleteMessageRef"
    v-model:open="state.deteleMessageVisible"
    @confirm="handleDeleteConfirm"
    @cancel="handleDeleteCancle"
  />

  <time-match v-if="state.timeMatchShow"
    @refresh="handleTimeMatchRefresh"
    :data="state.selectedData"
    :treeValue="state.treeValue"
    @close="state.timeMatchShow=false">
  </time-match>

  <line-chart
    v-if="state.lineShow"
    :index="state.timeRowIndex"
    :name="state.timeRowName"
    @close="state.lineShow=false"
  >
  </line-chart>
</template>
<script setup>

import 'ag-grid-community/styles/ag-grid.css'
import 'ag-grid-community/styles/ag-theme-quartz.css'
import 'ag-grid-enterprise'
import { LicenseManager } from 'ag-grid-enterprise'

import Mitt from '@/utils/mitt.js'
import { AgGridVue } from 'ag-grid-vue3'
import { ref, reactive, onMounted, onBeforeMount, nextTick, defineProps, defineEmits } from 'vue'
import { useRoute } from 'vue-router'
import { Modal } from 'ant-design-vue'
import message from '@/utils/message'
import { loadingStore } from '@/store/loadingStore'
import { settingStore } from '@/store/settingStore'
import { routeStore } from '@/store/routeStore'

import { getModifyDataApi, saveBaseDataApi, paramsReplenApi, deviceInitParam, updateDeviceInitParam, basicApi, DownloadCaseApi, resetDeviceInitParam } from '@/api/exampleApi'
import { downloadApiFile } from '@/utils/common.js'

LicenseManager.setLicenseKey('DownloadDevTools_COM_NDEwMjM0NTgwMDAwMA==59158b5225400879a12a96634544f5b6')

import SourceBusModal from './sourceBusModal.vue'
import SourceModal from './sourceModal.vue'
import SourceStorageModal from './sourceStorageModal.vue'
import ReplaceModal from './replaceModal.vue'
import CountWay from './countWay.vue'

import SourceTypeModal from './sourceTypeModal.vue'

import TimeseriesDetail from './TimeseriesDetail.vue'
import TimeseriesQuote from './TimeseriesQuote.vue'
import TimeseriesForm from './TimeseriesForm.vue'
import TimeseriesAdd from './TimeseriesAdd.vue'

import { useI18n } from 'vue-i18n'
const { t } = useI18n()

const route = useRoute()
const store = settingStore()
const storeLoading = loadingStore()
const storeRoute = routeStore()

const emits = defineEmits(['handleCorrelate'])

const props = defineProps({
	istree: {
		type: Boolean,
		default: false
	},
	isEdit: {
		type: String,
		default: ''
	},
	treeNode: {
		type: String,
		default: ''
	},
	isInputSearch: {
		type: Boolean,
		default: false
	}
})

const sourceTypeRef = ref()

const tableMarginTop = ref(props.isEdit != 'capacityBalance' ? '40px' : '0px')
const tableStyle = ref(props.isEdit != 'capacityBalance' ? 'width: 100%; height: 1000%;' : 'width: 100%; height: 85%;')

class RowIndexRenderer {
	init(params) {
		this.eGui = document.createElement('div')

		this.eGui.innerHTML = '' + (params.node.rowIndex + 1)
	}
	refresh(params) {
		return false
	}
	getGui() {
		return this.eGui
	}
}

class CustomElements {
	eGui
	eButton
	cButton
	eventListener
	eventListenerForCurve
	init(params) {
		if (state.routePath !== route.fullPath) return
		this.eGui = document.createElement('div')
		this.eGui.classList.add('custom-element')
		this.eGui.innerHTML = `
      <button class="btn-detail">${t('详情')}</button>
      <button class="btn-curve">${t('曲线')}</button>
      <button class="btn-quote">${t('引用')}</button>
    `

		this.eButton = this.eGui.querySelector('.btn-detail')
		this.cButton = this.eGui.querySelector('.btn-curve')
		this.qButton = this.eGui.querySelector('.btn-quote')

		this.eventListener = () => {
			timeseriesDetail(params.node)
		}
		this.eventListenerForCurve = () => {
			timeseriesCurve(params.node)
		}
		this.eventListenerForQuote = () => {
			timeseriesQuote(params.node)
		}
		this.eButton.addEventListener('click', this.eventListener)
		this.cButton.addEventListener('click', this.eventListenerForCurve)
		this.qButton.addEventListener('click', this.eventListenerForQuote)
	}

	getGui() {
		return this.eGui
	}

	refresh(params) {
		return false
	}
}

const localeText = ref(null)

const searchValue = ref('')
const fistColumn = ref({ headerName: t('序号'), maxWidth: 70, pinned: 'left', headerCheckboxSelection: true, headerCheckboxSelectionFilteredOnly: true, checkboxSelection: true, cellRenderer: RowIndexRenderer })
const actionBarColumn = ref({ headerName: t('操作'), minWidth: 180, editable: false, cellRenderer: CustomElements })

const tooltipShowDelay = ref(null)

const statusBar = ref(null)

const rowData = ref(null)
const getRowId = ref(null)
const timeseriesRef = ref(null)
const timeseriesQuoteRef = ref(null)
const timeseriesFormRef = ref(null)
const timeseriesAddRef = ref(null)
const deleteMessageRef = ref(null)

const gridApi = ref()
const columnTypes = ref(null)
const autoGroupColumnDef = ref(null)
const getDataPath = ref(null)
const rowSelection = ref(null)

const defaultColDef = ref({

	width: 150,

	editable: props.isEdit !== 'balanceSheet' && props.isEdit !== 'costSheet' && props.isEdit !== 'curveTimeseries',
	filter: true,
	menuTabs: ['generalMenuTab', 'filterMenuTab']

})

const columnDefs = ref([

])

const scrollBarWidth = 16

const state = reactive({
	routePath: route.fullPath,
	confirm_flag: true,
	confirm_time_flag: null,
	sourceVisible: false,
	sourceCityVisible: false,
	sourceTimeVisible: false,
	storageVisible: false,
	typeVisible: false,
	replaceVisible: false,
	countWayVisible: false,
	typeChangeVisible: false,
	typeChangeData: [],
	sourceValue: [],
	col_source: '',
	headerName: '',
	sourceTimeOpenWay: 'cell',

	sourceTitle: '',
	sourceVal: '',

	isedit: false,

	tableData: [],
	beforeDelData: [],
	timeRowIndex: null,
	timeRowName: '',

	treeValue: '',
	filterData: [],
	isItemized: false,
	treeNode: null,
	rowId: null,
	timeserieFormData: {},

	org_hierarchy_list: [],
	new_value_list: [],
	ratio_value_list: [],

	startRow: null,
	endRow: null,
	rangeColumns: [],

	editRowIndex: null,
	editField: '',
	isEdit: false,
	timeseriesVisible: false,
	timeseriesQuoteVisible: false,
	timeseriesFormVisible: false,
	timeseriesAddVisible: false,
	timeMatchShow: false,
	lineShow: false,
	deteleMessageVisible: false,
	selectedData: [],
	insertObj: {},
	timeTypeOptions: {},
	sourceOptions: {},
	typeOptions: {},
	paramsTypeOptions: {},

	max_index: -9999999
})

const getRowStyle = params => {
	// if (params.node.rowIndex % 2 === 0) {

	// }
}

const onFilterTextBoxChanged = () => {
	gridApi.value.setGridOption(
		'quickFilterText',
		searchValue.value
	)
}

const insertRow = (count) => {
	if (state.routePath !== route.fullPath) return
	const treeValue = sessionStorage.getItem('treeType')

	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})

	if (props.isEdit !== 'itemizedTimeseries' && props.isEdit !== 'paramsManage' && treeValue !== 'timeseries') {
		if (treeValue !== 'dc_line' && treeValue !== 'bus') {
			state.insertObj.type = treeValue.split('.')[1]
		}

		if (Object.keys(state.insertObj).includes('in_service')) {
			state.insertObj.in_service = true
		}
	}
	const newItems = Array(count).fill({ ...state.insertObj })
	const tempArr = JSON.parse(JSON.stringify(newItems))
	tempArr.forEach((item, index) => {
		state.max_index = item.index = state.max_index + 1
	})
	const addIndex = undefined
	gridApi.value.applyTransaction({
		add: tempArr,
		addIndex: addIndex
	})
}

const onRemoveSelected = (treeVal) => {
	gridApi.value.stopEditing()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})
	state.treeValue = treeVal
	state.beforeDelData = allRowData
	const selectedData = gridApi.value.getSelectedRows()
	const selectedArr = gridApi.value.getSelectedRows().map(item => item.index)
	gridApi.value.applyTransaction({ remove: selectedData })
	state.isedit = true
	storeRoute.setTabs(route.fullPath, true)
	storeRoute.setSaveTabs(route.fullPath, false)

	if ((treeVal == 'bus') && selectedArr.length > 0) {
		basicApi({
			'import_string_func': 'teapcase:check_element_relation',
			'func_arg_dict': {
				'sheet_name': treeVal,
				'file_name': route.query.filePath,
				'row_id_list': selectedArr
			}
		}).then(res => {
			if (res.code == 1 && res.func_result.code == 0) {
				state.deteleMessageVisible = true
				nextTick(() => {
					deleteMessageRef.value.getDeleteInfo(res.func_result)
				})
			}
		})
	}
}

const handleDeleteConfirm = () => {
	state.deteleMessageVisible = false
	saveAgtable()
}

const handleDeleteCancle = () => {
	state.deteleMessageVisible = false
	gridApi.value.setRowData(state.beforeDelData)
}

const getSelected = () => {
	const selectedData = gridApi.value.getSelectedRows()

	return selectedData
}

const removeRow = () => {

}

const onCellEditingStarted = (e) => {
	if (state.routePath !== route.fullPath) return
	const treeValue = sessionStorage.getItem('treeType')
	if (e.colDef.col_source || e.colDef.field == 'type' || e.colDef.field == 'data_source' || e.colDef.field == 'location_city') {
		state.editField = e.colDef.field
		if (e.colDef.field == 'location_city' || e.data.col_source == 'location_city') {
			state.sourceCityVisible = true
			gridApi.value.stopEditing()
		} else if (e.colDef.col_source == 'timeseries' || e.colDef.col_type == 'index_list') {
			state.sourceValue = e.value || []
			state.col_source = e.colDef.col_source
			state.headerName = e.colDef.headerName
			state.sourceTimeVisible = true
			state.sourceTimeOpenWay = 'cell'
			state.isItemized = false
		} else if (e.data.col_type == 'index_list') {
			state.sourceValue = e.value || []
			state.col_source = e.data.col_source
			state.headerName = e.data.name
			state.sourceTimeVisible = true
			state.sourceTimeOpenWay = 'itemizedButton'
			state.isItemized = true
		} else if ((treeValue == 'timeseries' || props.isEdit == 'itemizedTimeseries') && route.query.type !== 'params') {
			if (e.colDef.field == 'type') {
				state.typeVisible = true
				gridApi.value.stopEditing()
				nextTick(() => {
					sourceTypeRef.value.getReadNameColData(props.isEdit, props.treeNode)
				})
			}
		} else {
			return
		}
		state.editRowIndex = e.data.index
	}
}

const onBtExport = (fileName) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.exportDataAsExcel({ fileName, sheetName: 'Sheet1' })
}

const onCellValueChanged = (data) => {
	if (state.routePath !== route.fullPath) return
	state.org_hierarchy_list.push(data.data.orgHierarchy)
	state.new_value_list.push(data.value)
	state.ratio_value_list.push(data.value / data.oldValue)
	state.isedit = true
	if (props.isEdit != 'capacityBalance') {
		storeRoute.setTabs(route.fullPath, true)
		storeRoute.setSaveTabs(route.fullPath, false)
	}
}

const onRowEditingStarted = (e) => {
	if (state.routePath !== route.fullPath) return
}

const onSelectionChanged = (e) => {
	if (state.routePath !== route.fullPath) return
}

const processCellForClipboard = (params) => {
	return Array.isArray(params.value) ? JSON.stringify([...params.value]) : params.value
}

const processDataFromClipboard = (params) => {
	if (state.routePath !== route.fullPath) return
	if (props.isEdit == 'balanceSheet' || props.isEdit == 'costSheet' || props.isEdit == 'costLineSheet') return
	state.isedit = true
	storeRoute.setTabs(route.fullPath, true)
	storeRoute.setSaveTabs(route.fullPath, false)
	const treeValue = sessionStorage.getItem('treeType')
	const data = [...params.data]
	const emptyLastRow =
        data[data.length - 1][0] === '' && data[data.length - 1].length === 1
	if (emptyLastRow) {
		data.splice(data.length - 1, 1)
	}

	const lastIndex = params.api.getModel().getRowCount() - 1
	const focusedCell = params.api.getFocusedCell()
	const focusedIndex = focusedCell.rowIndex
	if (focusedIndex + data.length - 1 > lastIndex) {
		const resultLastIndex = focusedIndex + (data.length - 1)
		const numRowsToAdd = resultLastIndex - lastIndex
		const rowsToAdd = []
		for (let i = 0; i < numRowsToAdd; i++) {
			const index = data.length - 1
			const row = data.slice(index, index + 1)[0]

			const rowObject = {}
			let currentColumn = focusedCell.column
			row.forEach((item) => {
				if (!currentColumn) {
					return
				}
				rowObject[currentColumn.colDef.field] = item
				currentColumn = params.api.getDisplayedColAfter(currentColumn)
			})

			for (const key in state.insertObj) {
				if (rowObject[key] == undefined) {
					rowObject[key] = null
				}
			}

			if (treeValue.includes('gen') || treeValue.includes('stogen') || treeValue.includes('storage') || treeValue.includes('gen_plan') || treeValue.includes('stogen_plan') || treeValue.includes('storage_plan')) {
				rowObject.type = treeValue.split('.')[1]
			}

			if (Object.keys(rowObject).includes('in_service')) {
				rowObject.in_service = true
			}
			state.max_index = rowObject.index = state.max_index + 1
			rowsToAdd.push(rowObject)
		}
		params.api.applyTransaction({ add: rowsToAdd })
	}
	return data
}

const showContextMenu = (params) => {
	return {
		x: 0,
		y: 0
	}
}

const onPasteEnd = (e) => {
	gridApi.value.redrawRows()

	gridApi.value.refreshClientSideRowModel()
}

const onFilterChanged = (e) => {
	state['filterModel-' + state.treeValue] = gridApi.value.getFilterModel()
}

const clearFilter = () => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.setFilterModel(null)
}
Mitt.on('clearFilter', clearFilter)

const onCopyClick = () => {
	gridApi.value.copySelectedRangeToClipboard()
}

const onCutClick = () => {
	gridApi.value.cutToClipboard()
}

const onPasteClick = () => {
	gridApi.value.pasteFromClipboard()
}

const onBtExcludeMedalColumns = () => {
	if (state.routePath !== route.fullPath) return
	const colDefsMedalsExcluded = [
		{ field: 'athlete' },
		{ field: 'age' }
	]
	gridApi.value.setGridOption('columnDefs', colDefsMedalsExcluded)
}

const setHeaderNames = () => {
	if (state.routePath !== route.fullPath) return
	columnDefs.value.forEach(function(colDef, index) {
		colDef.headerName = 'C' + index
	})
	gridApi.value.setGridOption('columnDefs', columnDefs.value)
}

const onBtHide = () => {
	if (state.routePath !== route.fullPath) return
	columnDefs.value.forEach((colDef) => {
		if (colDef.field === 'age' || colDef.field === 'athlete') {
			colDef.hide = true
		}
	})
	gridApi.value.setGridOption('columnDefs', columnDefs.value)
}

const onBtPinnedOn = () => {
	if (state.routePath !== route.fullPath) return
	columnDefs.value.forEach((colDef) => {
		if (colDef.field === 'athlete') {
			colDef.pinned = 'left'
		}
		if (colDef.field === 'age') {
			colDef.pinned = 'right'
		}
	})
	gridApi.value.setGridOption('columnDefs', columnDefs.value)
}

const onAddRange = () => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.addCellRange({
		rowStartIndex: 4,
		rowEndIndex: 8,
		columnStart: 'age',
		columnEnd: 'date'
	})
}

const undo = () => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.undoCellEditing()
}

const redo = () => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.redoCellEditing()
}

const timeseriesDetail = (val) => {
	if (state.routePath !== route.fullPath) return
	state.timeseriesVisible = true
	nextTick(() => {
		timeseriesRef.value.getTimeseriesInit(val.data, route.query.filePath)
	})
}
const timeseriesDetailClose = (val) => {
	if (state.routePath !== route.fullPath) return
	state.timeseriesVisible = false
	if (props.isEdit == 'curveTimeseries') return
	nextTick(() => {
		getAgTableData(state.treeValue, state.filterData)
	})
}

const timeseriesCurve = (val) => {
	if (state.routePath !== route.fullPath) return
	state.timeRowIndex = val.data.index
	state.timeRowName = '时序曲线'
	state.lineShow = true
}

const timeseriesQuote = (val) => {
	if (state.routePath !== route.fullPath) return
	state.timeseriesQuoteVisible = true

	nextTick(() => {
		timeseriesQuoteRef.value.getQuoteData(val.data.index)
	})
}

const timeseriesAdd = (val, treeNode, rowId) => {
	if (state.routePath !== route.fullPath) return
	state.isItemized = val
	state.treeNode = treeNode
	state.rowId = rowId
	state.timeseriesFormVisible = true
	nextTick(() => {
		timeseriesFormRef.value.getReadNameColData(val, treeNode)
	})
}

const handleSaveSource = (val) => {
	if (state.routePath !== route.fullPath) return

	state.sourceVisible = false
	const rowNode = gridApi.value.getRowNode(state.editRowIndex)
	rowNode.setDataValue(state.editField, val)
}

const handleCityConfirm = (val) => {
	if (state.routePath !== route.fullPath) return

	state.sourceCityVisible = false
	const rowNode = gridApi.value.getRowNode(state.editRowIndex)
	rowNode.setDataValue(state.editField, val.areaName)
	rowNode.setDataValue('longitude', val.center[0])
	rowNode.setDataValue('latitude', val.center[1])
}

const handleConfirmTime = (val) => {
	if (state.routePath !== route.fullPath) return
	if (!state.isItemized || state.col_source !== 'timeseries') {
		gridApi.value.stopEditing()
		state.sourceTimeVisible = false
		const rowNode = gridApi.value.getRowNode(state.editRowIndex)
		rowNode.setDataValue(state.editField, val)
	} else {
		state.sourceTimeVisible = false

		emits('handleCorrelate', val)
	}
}
const handleCancelTime = (val) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()
	state.sourceTimeVisible = false
	if (state.sourceTimeOpenWay == 'itemizedButton') return
	const rowNode = gridApi.value.getRowNode(state.editRowIndex)
	rowNode.setDataValue(state.editField, val)
}

const handleCorrelateData = (val) => {
	if (state.routePath !== route.fullPath) return
	state.sourceValue = val || []
	state.col_source = 'timeseries'
	state.headerName = '时序曲线'
	state.sourceTimeVisible = true
	state.sourceTimeOpenWay = 'itemizedButton'
	state.isItemized = true
}

const handleSaveStorage = (val) => {
	if (state.routePath !== route.fullPath) return

	state.storageVisible = false
	const rowNode = gridApi.value.getRowNode(state.editRowIndex)
	rowNode.setDataValue(state.editField, val)
}

const handleSaveType = (val) => {
	if (state.routePath !== route.fullPath) return

	state.typeVisible = false
	const rowNode = gridApi.value.getRowNode(state.editRowIndex)
	rowNode.setDataValue(state.editField, val)
}

const handleParamsReplen = (treeVal) => {
	if (state.routePath !== route.fullPath) return
	const selectedRows = gridApi.value.getSelectedRows()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})

	const table_list = selectedRows.length <= 0 ? allRowData : selectedRows
	paramsReplenApi({
		'import_string_func': 'teapcase:get_init_col_data',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_name': treeVal,
			'replace': false,
			'row_id_list': table_list.map(item => item.index),
			'col_list': []

		}
	}).then(res => {
		const { input_equals_output } = res.func_result[treeVal]

		if (!input_equals_output) {
			getAgTableData(treeVal)
			Mitt.emit('getTreeMenuList', 'saveRefresh')

			storeRoute.setTabs(route.fullPath, true)
			storeRoute.setSaveTabs(route.fullPath, false)
		}
		message.success(t('参数补充成功！'))
	})
}

const handleTimeMatch = () => {
	if (state.routePath !== route.fullPath) return

	const isHaveTimeseries = columnDefs.value.some(item => item.field == 'timeseries')
	if (!isHaveTimeseries) return message.warning(t('不允许关联时序曲线'))

	state.selectedData = gridApi.value.getSelectedRows().map(item => item.index)
	if (state.selectedData.length <= 0) return message.warning(t('请选择数据'))
	saveAgtable(state.treeValue, 'timeMatch')
}
const confirmTypeChange = (val) => {
	state.typeChangeVisible = false
	storeLoading.showModal()
	basicApi({
		'import_string_func': 'teapcase:power_ele_type_convert',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'source_sheet_name': state.treeValue,
			'target_sheet_name': val,
			'selected_row_id_list': gridApi.value.getSelectedRows().map(item => item.index)
		}
	}).then(res => {
		if (res.code == 1) {
			message.success(t('类型转换成功！'))
			getAgTableData(state.treeValue)
			Mitt.emit('getTreeMenuList')
		}
		storeLoading.hiddenModal()
	}).catch(() => {
		storeLoading.hiddenModal()
	})
}

const handleTypeChange = () => {
	if (state.routePath !== route.fullPath) return
	state.selectedData = gridApi.value.getSelectedRows().map(item => item.index)
	if (state.selectedData.length <= 0) return message.warning(t('请选择数据'))
	basicApi({
		'import_string_func': 'teapcase:get_valid_type_convert_sheet_info',
		'func_arg_dict': {
			'source_sheet_name': state.treeValue
		}
	}).then(res => {
		if (res.code == 1) {
			state.typeChangeData = res.func_result
			state.typeChangeVisible = true
		}
	})
}

const handleTimeMatchRefresh = () => {
	state.timeMatchShow = false
	getAgTableData(state.treeValue)
}

const setCurveTimeData = (data) => {
	if (state.routePath !== route.fullPath) return
	const tableData = JSON.parse(JSON.stringify(data))
	const { relation_sheet_index, type, ts_type } = tableData
	for (const key in relation_sheet_index) {
		state.sourceOptions[key] = relation_sheet_index[key].reduce((acc, item) => Object.assign(acc, { [item.index]: item.name }), {})
	}
	for (const key in type) {
		state.typeOptions[key] = type[key].reduce((acc, item) => Object.assign(acc, { [item.value]: item.label }), {})
	}
	state.timeTypeOptions = ts_type.ts_type_name_map

	if (tableData.total_row > 0) {
		tableData.columns.push(actionBarColumn.value)
	}
	updateData(tableData)
}

const setTimeDeatilData = (data) => {
	if (state.routePath !== route.fullPath) return
	const tableData = JSON.parse(JSON.stringify(data))
	updateData(tableData)
}

const setBlanceData = (data) => {
	if (state.routePath !== route.fullPath) return
	const tableData = JSON.parse(JSON.stringify(data))
	updateData(tableData)
}

const setBlanceGoalsData = (data) => {
	if (state.routePath !== route.fullPath) return
	const tableData = JSON.parse(JSON.stringify(data))
	updateData(tableData)
}

const openReplace = () => {
	const rangeSelections = gridApi.value.getCellRanges()
	if (rangeSelections.length <= 0) {
		return message.warning(t('请选择要替换的数据'))
	} else {
		state.replaceVisible = true
		state.startRow = rangeSelections[0].startRow.rowIndex
		state.endRow = rangeSelections[0].endRow.rowIndex
		state.rangeColumns = rangeSelections[0].columns
	}
}

const handleSaveReplace = (searchValue, replaceValue) => {
	const itemsToUpdate = []
	gridApi.value.forEachNodeAfterFilterAndSort(function(rowNode, index) {
		if (index >= state.startRow && index <= state.endRow) {
			const data = rowNode.data
			state.rangeColumns.forEach(item => {
				if (item.colDef.col_source) {
					const tempSearch = Number(Object.keys(state.sourceOptions[item.colDef.col_source]).find(key => state.sourceOptions[item.colDef.col_source][key] === searchValue))
					const tempeRplace = Number(Object.keys(state.sourceOptions[item.colDef.col_source]).find(key => state.sourceOptions[item.colDef.col_source][key] === replaceValue))
					if (item.colDef.col_source == 'timeseries') {
						if (data[item.colId].includes(tempSearch)) {
							data[item.colId] = data[item.colId].map(item => (item === tempSearch ? tempeRplace : item))
						}
					} else {
						if (data[item.colId] == tempSearch) {
							data[item.colId] = tempeRplace
						}
					}
				} else {
					if (item.colDef.field == 'type') {
						if (item.colDef.editable) {
							const typeSearch = Object.keys(state.typeOptions[state.treeValue]).find(key => state.typeOptions[state.treeValue][key] === searchValue)
							const typeRplace = Object.keys(state.typeOptions[state.treeValue]).find(key => state.typeOptions[state.treeValue][key] === replaceValue)
							if (data[item.colId] == typeSearch) {
								data[item.colId] = typeRplace
							}
						}
					} else {
						data[item.colId] = JSON.parse(JSON.stringify(data[item.colId]).replace(searchValue, replaceValue))
					}
				}

				itemsToUpdate.push(data)
			})
			itemsToUpdate.push(data)
		}
	})
	gridApi.value.applyTransaction({ update: itemsToUpdate })

	state.replaceVisible = false
	state.isedit = true
	storeRoute.setTabs(route.fullPath, true)
	storeRoute.setSaveTabs(route.fullPath, false)
}

const onCount = () => {
	const rangeSelections = gridApi.value.getCellRanges()
	if (rangeSelections.length <= 0) {
		return message.warning(t('请选择要计算的数据'))
	} else {
		state.countWayVisible = true
		state.startRow = rangeSelections[0].startRow.rowIndex
		state.endRow = rangeSelections[0].endRow.rowIndex
		state.rangeColumns = rangeSelections[0].columns
	}
}

const handleSaveCount = (value, way) => {
	if (value !== null) {
		const itemsToUpdate = []
		gridApi.value.forEachNodeAfterFilterAndSort(function(rowNode, index) {
			if (index >= state.startRow && index <= state.endRow) {
				const data = rowNode.data
				state.rangeColumns.forEach(item => {
					if (way == 'sub') {
						data[item.colId] = data[item.colId] - value
					} else if (way == 'mul') {
						data[item.colId] = data[item.colId] * value
					} else if (way == 'div') {
						data[item.colId] = data[item.colId] / value
					} else {
						data[item.colId] = data[item.colId] + value
					}
					itemsToUpdate.push(data)
				})
				itemsToUpdate.push(data)
			}
		})
		gridApi.value.applyTransaction({ update: itemsToUpdate })
	}
	state.countWayVisible = false
	state.isedit = true
	storeRoute.setTabs(route.fullPath, true)
	storeRoute.setSaveTabs(route.fullPath, false)
}

const onGridReady = (params) => {
	gridApi.value = params.api
}

function onBtExpandTopLevel() {
	gridApi.value.forEachNode(function(node) {
		if (node.level == 0) {
			node.setExpanded(true)
		}
	})
}

const getReadNameData = (data) => {
	const { relation_sheet_index, ts_type, type } = data
	for (const key in relation_sheet_index) {
		state.sourceOptions[key] = relation_sheet_index[key].reduce((acc, item) => Object.assign(acc, { [item.index]: item.name }), {})
	}

	for (const key in type) {
		state.typeOptions[key] = type[key].reduce((acc, item) => Object.assign(acc, { [item.value]: item.label }), {})
	}

	state.timeTypeOptions = ts_type.ts_type_name_map
}

const getAgTableData = (val, filterArr) => {
	if (state.routePath !== route.fullPath) return
	state.treeValue = val || sessionStorage.getItem('treeType')
	state.filterData = filterArr
	getModifyDataApi({
		'import_string_func': 'teapcase:read_from_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'filtered_index_list': filterArr,
			'sheet_name': val || sessionStorage.getItem('treeType')
		}
	}).then(res => {
		if (res.code == 1) {
			const { relation_sheet_index, type, ts_type } = res.func_result[val || sessionStorage.getItem('treeType')]
			for (const key in relation_sheet_index) {
				state.sourceOptions[key] = relation_sheet_index[key].reduce((acc, item) => Object.assign(acc, { [item.index]: item.name }), {})
			}

			for (const key in type) {
				state.typeOptions[key] = type[key].reduce((acc, item) => Object.assign(acc, { [item.value]: item.label }), {})
			}
			state.timeTypeOptions = ts_type.ts_type_name_map

			state.tableData = res.func_result[val || sessionStorage.getItem('treeType')]
			state.tableData.columns.unshift(fistColumn.value)
			if (val == 'timeseries' || sessionStorage.getItem('treeType') == 'timeseries') {
				if (res.func_result.timeseries.total_row > 0) {
					state.tableData.columns.push(actionBarColumn.value)
				}
				Mitt.emit('getGlobalParameters', {
					start_datetime: res.func_result.start_datetime,
					end_datetime: res.func_result.end_datetime,
					data_freq: res.func_result.data_freq
				})
			}

			state.max_index = state.tableData.max_index
			storeRoute.setSaveTabs(route.fullPath, res.func_result.is_saved)

			updateData(state.tableData, null, 'getAgTableData')
		}
	})
}

const getParamsAgTable = (val) => {
	if (state.routePath !== route.fullPath) return
	state.treeValue = val || sessionStorage.getItem('treeType')
	deviceInitParam({
		device_name: val
	}).then(res => {
		if (res.code == 1) {
			const { columns, data } = res
			for (const key in res.type) {
				state.typeOptions[key] = res.type[key].reduce((acc, item) => Object.assign(acc, { [item.value]: item.label }), {})
			}
			columns.unshift(fistColumn.value)
			const tableData = {
				columns: columns,
				data: data
			}
			storeRoute.setTabs(route.fullPath, false)
			storeRoute.setSaveTabs(route.fullPath, true)
			updateData(tableData, val)
		}
	})
}

const resetParamsAgTable = (val, paramsTreeData, isCurrent) => {
	if (state.routePath !== route.fullPath) return
	resetDeviceInitParam({
		device_names: isCurrent ? [val] : paramsTreeData
	}).then(res => {
		if (res.code == 1) {
			message.success(res.message || t('恢复默认成功'))
			getParamsAgTable(val)
		}
	})
}

const updateData = (data, deviceId, type) => {
	if (state.routePath !== route.fullPath) return
	const treeValue = sessionStorage.getItem('treeType')
	state.insertObj = {}
	let tableData

	columnDefs.value = data.columns.map(item => {
		if (item.cellDataType == 'number' && !item.col_source) {
			item.valueFormatter = currencyNumFormatter
		}

		if (item.field == 'value_type') {
			item.cellEditor = 'agSelectCellEditor'
			item.cellEditorParams = {
				values: ['replace', 'multiply']
			}
			item.refData = {
				replace: t('数据替换'),
				multiply: t('数据倍乘')
			}
			item.filter = 'agSetColumnFilter'
		}

		if (item.col_source && item.col_type !== 'index_list' && !item.type) {
			item.cellEditor = 'agRichSelectCellEditor'
			item.cellEditorParams = {
				values: Object.keys(state.sourceOptions[item.col_source]).map(Number),
				formatValue: value => state.sourceOptions[item.col_source][value] == '' ? ' ' : state.sourceOptions[item.col_source][value],
				allowTyping: true,
				filterList: true,
				highlightMatch: true,
				valueListMaxHeight: 220
			}

			item.valueFormatter = currencyValFormatter
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: currencyValFormatter }
		}

		if (item.col_source == 'timeseries') {
			item.cellEditor = 'agTextCellEditor'
			item.valueFormatter = currencyValTimeFormatter
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: currencyValFormatter }
		}

		if (item.field == 'type' && item.headerName !== '时序类型') {
			if (state.typeOptions[treeValue]) {
				item.cellEditor = 'agRichSelectCellEditor'
				item.cellEditorParams = {
					values: Object.keys(state.typeOptions[treeValue]),
					formatValue: value => state.typeOptions[treeValue][value],
					allowTyping: true,
					filterList: true,
					highlightMatch: true,
					valueListMaxHeight: 220
				}
			}

			item.valueFormatter = currencyTypeFormatter
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: currencyTypeFormatter }
		}

		if (item.type) {
			item.cellEditor = 'agRichSelectCellEditor'
			item.cellEditorParams = {
				values: Object.keys(state.typeOptions[`${item.col_source}.${item.field}`]),
				formatValue: value => state.typeOptions[`${item.col_source}.${item.field}`][value],
				allowTyping: true,
				filterList: true,
				highlightMatch: true,
				valueListMaxHeight: 220
			}
			item.valueFormatter = (params) => {
				if (params.value && state.typeOptions[`${item.col_source}.${item.field}`]) {
					return state.typeOptions[`${item.col_source}.${item.field}`][params.value]
				} else {
					return params.value
				}
			}
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: (params) => {
				if (params.value && state.typeOptions[`${item.col_source}.${item.field}`]) {
					return state.typeOptions[`${item.col_source}.${item.field}`][params.value]
				} else {
					return params.value
				}
			} }
		}

		if (deviceId && item.field == 'type' && deviceId !== 'ac_line' && deviceId !== 'dc_line') {
			item.cellEditor = 'agRichSelectCellEditor'
			item.cellEditorParams = {
				values: Object.keys(state.typeOptions[deviceId]),
				formatValue: value => state.typeOptions[deviceId][value],
				allowTyping: true,
				filterList: true,
				highlightMatch: true,
				valueListMaxHeight: 220
			}
			item.valueFormatter = (params) => {
				if (params.value && state.typeOptions[deviceId]) {
					return state.typeOptions[deviceId][params.value]
				} else {
					return params.value
				}
			}
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: (params) => {
				if (params.value && state.typeOptions[deviceId]) {
					return state.typeOptions[deviceId][params.value]
				} else {
					return params.value
				}
			} }
		}
		if ((sessionStorage.getItem('treeType') && sessionStorage.getItem('treeType').includes('timeseries')) || props.isEdit == 'itemizedTimeseries' || props.isEdit == 'curveTimeseries') {
			if (item.field == 'type') {
				item.valueFormatter = currencyTimeTypeFormatter
				item.filter = 'agSetColumnFilter'
				item.filterParams = { valueFormatter: currencyTimeTypeFormatter }
			}
		}

		if (item.col_type == 'index_list') {
			item.valueFormatter = currencyValTimeFormatter
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: currencyValTimeFormatter }
		}

		if (item.field == 'data_source') {
			if (item.cellDataType !== 'object') {
				item.cellEditor = 'agRichSelectCellEditor'
				item.cellEditorParams = currencyCellEditorParams
			}
			item.valueFormatter = currencySourceFormatter
			item.filter = 'agSetColumnFilter'
			item.filterParams = { valueFormatter: currencySourceFormatter }
		}

		if (item.field == 'value') {
			item.cellDataType = null
			item.valueParser = itemValueParser
			item.valueFormatter = currencyValueFormatter
		}

		if (item.datetime || item.field == 'datetime') {
			item.cellEditor = 'agDateStringCellEditor'
		}

		if (item.col_type == 'list') {
			if (!item.hide) {
				item.cellDataType = 'array'
				item.valueParser = currencyValueParser
				item.valueFormatter = powerLevelsValueFormatter
			}
		}

		if (item.required) {
			item.headerComponentParams = {
				template: `<div class="ag-cell-label-container" role="presentation">
	                  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>
	                  <div ref="eLabel" class="ag-header-cell-label" role="presentation">
                      <span ref="eSortOrder" class="ag-header-icon ag-sort-order ag-hidden"></span>
                      <span ref="eSortAsc" class="ag-header-icon ag-sort-ascending-icon ag-hidden"></span>
                      <span ref="eSortDesc" class="ag-header-icon ag-sort-descending-icon ag-hidden"></span>
                      <span ref="eSortMixed" class="ag-header-icon ag-sort-mixed-icon ag-hidden"></span>
                      <span ref="eSortNone" class="ag-header-icon ag-sort-none-icon ag-hidden"></span>
                      <span ref="eText" class="ag-header-cell-text" role="columnheader"></span> ※
                      <span ref="eFilter" class="ag-header-icon ag-filter-icon"></span>
	                  </div>
	              </div>`
			}
		}
		return item
	})
	columnDefs.value.forEach(item => {
		item.cellStyle = { 'font-family': 'SiYuan Normal' }
		if (item.cellDataType == 'text') {
			state.insertObj[item.field] = ''
		}
		if (item.cellDataType == 'boolean') {
			state.insertObj[item.field] = null
		} else if (item.cellDataType == 'number') {
			state.insertObj[item.field] = null
			if (!item.col_source) {
				item.cellStyle = { 'font-family': 'SiYuan Normal', 'text-align': 'right' }
			}
		} else if (item.cellDataType == 'object') {
			state.insertObj[item.field] = []
		} else if (item.field && !item.cellDataType) {
			state.insertObj[item.field] = null
		}

		if (props.isEdit == 'balanceSheet' || props.isEdit == 'costSheet') {
			item.cellStyle = { 'font-family': 'SiYuan Normal', 'text-align': 'right' }
		}
	})

	if (data.data.length <= 0) {
		const tempInsertObj = JSON.parse(JSON.stringify(state.insertObj))
		state.max_index = tempInsertObj.index = state.max_index + 1
		if (props.isEdit !== 'itemizedTimeseries' && props.isEdit !== 'balanceSheet' && props.isEdit !== 'costSheet' && props.isEdit !== 'curveTimeseries' && treeValue !== 'timeseries') {
			if (treeValue !== 'dc_line' && treeValue !== 'bus') {
				tempInsertObj.type = treeValue.split('.')[1] == undefined ? null : treeValue.split('.')[1]
			}
			if (Object.keys(tempInsertObj).includes('in_service')) {
				tempInsertObj.in_service = true
			}
		}
		tableData = [...data.data, tempInsertObj]
	} else {
		tableData = data.data
	}

	if (type == 'getAgTableData') {
		tableData.forEach(element => {
			columnDefs.value.forEach(item => {
				for (const key in element) {
					if (item.field == key && item.col_ratio && element[key]) {
						element[key] = element[key] * item.col_ratio
					}
				}
			})
		})
	}

	rowData.value = tableData

	getRowId.value = (params) => params.data.index
	if (props.isEdit == 'curveTimeseries') return
	gridApi.value.clearRangeSelection()
	gridApi.value.deselectAllFiltered()
	gridApi.value.deselectAllOnCurrentPage()
	storeLoading.hiddenModal()

	if (props.isEdit == 'costSheet') {
		nextTick(() => {
			onBtExpandTopLevel()
		})
	}
	const filterModel = state['filterModel-' + state.treeValue]
	if (props.isEdit == 'editor' && filterModel && Object.keys(filterModel).length !== 0) {
		nextTick(() => {
			gridApi.value.setFilterModel(filterModel)
		})
	}
}

const currencyCellEditorParams = (params) => {
	return {
		values: Object.keys(state.sourceOptions[params.data.col_source]).map(Number),

		allowTyping: true,
		filterList: true,
		highlightMatch: true,
		valueListMaxHeight: 220
	}
}

const currencyNumFormatter = (params) => {
	if (params.value && !Number.isInteger(params.value)) {
		const strNumber = params.value.toString()

		const decimalIndex = strNumber.indexOf('.')
		if (decimalIndex !== -1 && strNumber.length - decimalIndex > 4) {
			return parseFloat(params.value.toFixed(3))
		} else {
			return params.value
		}
	} else {
		return params.value
	}
}

const currencyValFormatter = (params) => {
	if (state.sourceOptions[params.colDef.col_source][params.value]) {
		return state.sourceOptions[params.colDef.col_source][params.value]
	} else if (state.sourceOptions[params.colDef.col_source][params.value] == '') {
		return ''
	} else {
		return params.value
	}
}

const currencyValTimeFormatter = (params) => {
	if (Array.isArray(params.value) && params.value.length > 0) {
		const tempArr = params.value.map(item => item = state.sourceOptions[params.colDef.col_source][item])
		return tempArr
	} else {
		return params.value
	}
}

const currencyTypeFormatter = (params) => {
	let treeNode = sessionStorage.getItem('treeType') || 'bus'
	treeNode = treeNode.includes('.') ? treeNode.split('.')[0] : treeNode
	if (params.value && state.typeOptions[treeNode]) {
		return state.typeOptions[treeNode][params.value]
	} else {
		return params.value
	}
}

const currencyTimeTypeFormatter = (params) => {
	if (state.timeTypeOptions[params.value]) {
		return state.timeTypeOptions[params.value]
	} else {
		return params.value
	}
}

const itemValueParser = (params) => {
	if (params.data.key == 'power_levels') {
		if (!params.newValue || params.newValue.length <= 0) return []
		const paramsCellValue = Array.isArray(params.newValue) ? params.newValue : params.newValue.split(',')
		const tempArr = paramsCellValue.map(item => {
			const num = Number(item)
			return isNaN(num) ? null : num
		})
		if (tempArr.includes(null)) {
			alert(t('格式错误,请输入英文逗号分隔的数值！示例：1,2,3'))
		}
		return tempArr.includes(null) ? [] : tempArr
	} else {
		return params.newValue
	}
}
const currencyValueFormatter = (params) => {
	if (params.data.key == 'type') {
		return state.typeOptions[params.data.col_source] ? state.typeOptions[params.data.col_source][params.value] : params.value
	} else if (params.data.col_type == 'float') {
		if (params.value && !Number.isInteger(params.value)) {
			const strNumber = JSON.stringify(params.value)

			const decimalIndex = strNumber.indexOf('.')
			if (decimalIndex !== -1 && strNumber.length - decimalIndex > 4) {
				return parseFloat(Number(params.value).toFixed(3))
			} else {
				return params.value
			}
		} else {
			return params.value
		}
	} else if (params.data.key == 'power_levels') {
		return !params.value || params.value.length <= 0 ? params.value : params.value.join(',')
	} else {
		return params.value
	}
}
const currencySourceFormatter = (params) => {
	if (params.value !== '—') {
		if (params.data.col_type == 'index_list') {
			if (Array.isArray(params.value) && params.value.length > 0) {
				const tempArr = params.value.map(item => item = state.sourceOptions[params.data.col_source][item])
				return tempArr
			} else {
				return params.value
			}
		} else {
			return state.sourceOptions[params.data.col_source][params.value]
		}
	} else {
		return params.value
	}
}

const currencyValueParser = (params) => {
	if (!params.newValue || params.newValue.length <= 0) return []
	const paramsCellValue = Array.isArray(params.newValue) ? params.newValue : params.newValue.split(',')
	const tempArr = paramsCellValue.map(item => {
		const num = Number(item)
		return isNaN(num) ? null : num
	})
	if (tempArr.includes(null)) {
		alert(t('格式错误,请输入英文逗号分隔的数值！示例：1,2,3'))
	}
	return tempArr.includes(null) ? [] : tempArr
}

const powerLevelsValueFormatter = (params) => {
	return !params.value || params.value.length <= 0 ? params.value : params.value.join(',')
}

const saveAgtable = (treeVal, val, fileType) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()

	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})

	const columnTempDefs = JSON.parse(JSON.stringify(columnDefs.value))
	allRowData.forEach(element => {
		columnTempDefs.forEach(item => {
			for (const key in element) {
				if (item.field == key && item.col_ratio && element[key]) {
					element[key] = element[key] / item.col_ratio
				}
			}
		})
	})

	saveBaseDataApi({
		'import_string_func': 'teapcase:write_to_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_name': treeVal || state.treeValue,
			'data': allRowData,
			'confirm_flag': state.confirm_flag,
			'ignore_temp_save': val == 'ignoreTempSave' || val == 'newBuilt' || val == 'globalToSave' || val == 'newBuiltToCalculate' || val == 'saveAndClose'
		}
	}, true).then(res => {
		storeLoading.hiddenModal()
		if (res.code == 1 && res.func_result.code == 1) {
			state.isedit = false
			if (val == 'saveAndClose') return Mitt.emit('onAfterSaveClose', fileType)
			storeRoute.setTabs(route.fullPath, false)

			if (val == 'ignoreTempSave' || val == 'newBuilt' || val == 'globalToSave' || val == 'newBuiltToCalculate') {
				storeRoute.setSaveTabs(route.fullPath, true)
				message.success(res.func_result.message || t('保存成功') + '！')
				if (val == 'newBuilt' || val == 'newBuiltToCalculate') return Mitt.emit('saveNewFile', val)
			}

			if (val == 'globalToSaveTemp' || val == 'globalToSave') {
				Mitt.emit('handleUploadCase')
			}

			if (val == 'timeMatch') {
				state.timeMatchShow = true
			}
			if (val == 'paramsReplen') return handleParamsReplen(treeVal)
			if (val == 'copyData') return Mitt.emit('handleCopyData')
			if (val == 'pasteData') return Mitt.emit('handlePasteData')
			if (val == 'tableUpload') return Mitt.emit('handleImportFile')
			if (val == 'tableDownload') return Mitt.emit('exportXls')
			if (val == 'curvePreview') return Mitt.emit('handleCurve')
			if (val == 'statistic') return Mitt.emit('handleStatistic')
			if (val == 'saveAs') return getSaveAs(fileType)
			if (val == 'unitChange') return getAgTableData()
			if (val == 'showGis') return Mitt.emit('gisReadyShow')

			Mitt.emit('getTreeMenuList', 'saveRefresh')
			if (val.type && val.type == 'treeChange') {
				return Mitt.emit('onSelectTreeChange', val.treeNode_new)
			}

			getAgTableData()
		} else if (res.code == -2) {
			Modal.confirm({
				title: '注意',
				content: res.message,
				okText: t('另存为'),
				cancelText: '取消',
				onOk() {
					getSaveAs('tc')
				},
				onCancel() {
					state.confirm_flag = false
				}
			})
		} else {
			message.error(res.func_result.message || t('保存失败') + '！')
		}
	}).catch(() => {
		storeLoading.hiddenModal()
	})
}

const getSaveAs = (fileType) => {
	if (navigator.userAgent.includes('Electron') && fileType == 'tc') {
		window.electronApi.sendToMain('will_download')
		window.electronApi.receiveFromMain('download_finished', (args) => {
			Mitt.emit('SaveAs', args)
		})
	}
	DownloadCaseApi({
		'tc_filename': route.query.filePath,
		'file_type': fileType == 'v2Xlsx' ? 'xlsx' : fileType,
		'v2_flag': fileType == 'v2Xlsx'
	}, true).then(res => {
		downloadApiFile(res)
		storeLoading.hiddenModal()
		if (navigator.userAgent.includes('Electron') && fileType == 'tc') {
			basicApi({
				'import_string_func': 'teapcase:delete_tc_instance',
				'func_arg_dict': {
					'file_name': route.query.filePath
				}
			})
		}
	}).catch(() => {
		storeLoading.hiddenModal()
	})
}

const saveInElectron = (val) => {
	if (state.routePath !== route.fullPath) return
	if (state.isedit) {
		gridApi.value.stopEditing()
		const allRowData = []
		gridApi.value.forEachNode(function(node) {
			allRowData.push(node.data)
		})
		allRowData.forEach((item, index) => {
			if (!item.index && item.index != 0) {
				item.index = Number(`-${index + 1}`)
			}
			return item
		})
		saveBaseDataApi({
			'import_string_func': 'teapcase:write_to_tc',
			'func_arg_dict': {
				'file_name': route.query.filePath,
				'sheet_name': state.treeValue,
				'data': allRowData,
				'confirm_flag': state.confirm_flag,
				'ignore_temp_save': true
			}
		}, true).then(res => {
			if (res.code == 1 && res.func_result.code == 1) {
				Mitt.emit('canSave', true)
			} else {
				Mitt.emit('canSave', false)
			}
			storeLoading.hiddenModal()
		}).catch(() => {
			Mitt.emit('canSave', false)
			storeLoading.hiddenModal()
		})
	} else {
		Mitt.emit('canSave', true)
	}
}
Mitt.on('saveInElectron', saveInElectron)

const saveTsimeseriestable = (treeVal, id, args) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})

	const tempArr = allRowData.map(item => {
		delete item.max_value
		delete item.min_value
		delete item.sum_value
		delete item.relation_count

		return item
	})

	saveBaseDataApi({
		'import_string_func': 'teapcase:update_timeseries_in_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_name': treeVal,
			'row_id': Number(id),
			'confirm': state.confirm_time_flag,
			'data': tempArr,
			'ignore_temp_save': args == 'ignoreTempSave'
		}
	}, true).then(res => {
		storeLoading.hiddenModal()
		if (res.code == 1 && res.func_result.code == 1) {
			state.confirm_time_flag = null
			state.isedit = false
			storeRoute.setTabs(route.fullPath, false)
			if (args == 'ignoreTempSave') {
				storeRoute.setSaveTabs(route.fullPath, true)
			}
			if (args == 'globalToSaveTemp' || args == 'globalToSave') {
				Mitt.emit('handleUploadCase')
			}

			if (args && args.type == 'saveAs') return getSaveAs(args.fileType)

			Mitt.emit('getTreeMenuList')

			if (args.type && args.type == 'treeChange') {
				Mitt.emit('onSelectTreeChange', args.treeNode_new)
				return
			}

			Mitt.emit('getDeviceRowClick')
		} else if (res.code == 1 && res.func_result.code == 2) {
			const messageTemp = res.func_result.relation_ts_name_list.join('、')
			Modal.confirm({
				title: t('注意'),
				content: `${messageTemp} ${t('已关联其他设备，请确认操作')}`,
				okText: t('全部修改'),
				cancelText: t('创建副本'),
				onOk() {
					state.confirm_time_flag = true
					saveTsimeseriestable(treeVal, id)
				},
				onCancel() {
					state.confirm_time_flag = false
					saveTsimeseriestable(treeVal, id)
					storeLoading.hiddenModal()
				}
			})
		} else if (res.func_result.code !== 1) {
			message.error(res.func_result.message || t('保存失败') + '！')
		}
	}).catch(() => {
		storeLoading.hiddenModal()
	})
}

const saveTimeDeatilData = (id, totalHours) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})

	let timeArr = JSON.parse(JSON.stringify(allRowData)).map(item => {
		delete item.index
		delete item.day
		delete item.time
		item = Object.values(item)
		return item
	}).flat(Infinity)
	if (timeArr.length > totalHours) {
		timeArr = timeArr.splice(0, totalHours)
	}
	saveBaseDataApi({
		'import_string_func': 'teapcase:write_one_ts_value_to_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'row_id': id,
			'data': timeArr
		}
	}, true).then(res => {
		storeLoading.hiddenModal()
		if (res.code == 1 && res.func_result.code == 1) {
			message.success(res.func_result.message || t('更新成功') + '！')
		} else {
			message.error(res.func_result.message || t('更新失败') + '！')
		}
	}).catch(() => {
		storeLoading.hiddenModal()
	})
}

const getAgData = () => {
	gridApi.value.stopEditing()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})
	return allRowData
}

const saveTimeAddData = () => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})

	return allRowData
}

const handleSaveTimeseriesAdd = (formData) => {
	state.timeseriesFormVisible = false
	state.timeserieFormData = formData

	Mitt.emit('handleActionBar', 'saveTemp')

	state.timeseriesAddVisible = true
	nextTick(() => {
		timeseriesAddRef.value.getTimeseriesInit(state.timeserieFormData, route.query.filePath, state.isItemized ? Number(state.rowId) : null)
	})
}

const saveBalanceGoals = (sheet_name, group_columns, group_type, target_column) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()
	setTimeout(() => {
		saveBaseDataApi({
			'import_string_func': 'teapcase:update_relation_bus_data',
			'func_arg_dict': {
				'file_name': route.query.filePath,
				'sheet_name': sheet_name,
				'group_columns': group_columns,
				'group_type': group_type,
				'target_column': target_column,
				'org_hierarchy_list': state.org_hierarchy_list,
				'new_value_list': state.new_value_list,
				'ratio_value_list': state.ratio_value_list
			}
		}).then(res => {
			if (res.code == 1 && res.func_result.code == 1) {
				message.success(res.func_result.message || t('更新成功') + '！')
				state.org_hierarchy_list = []
				state.new_value_list = []
				state.ratio_value_list = []
				Mitt.emit('refreshBalanceGoals')
			} else {
				message.error(res.func_result.message || t('更新失败') + '！')
				state.org_hierarchy_list = []
				state.new_value_list = []
				state.ratio_value_list = []
			}
		}).catch(() => {

		})
	}, 200)
}

const saveParamsAgtable = (val, targetNode) => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()

	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})

	allRowData.forEach((item, index) => {
		if (!item.index && item.index != 0) {
			item.index = Number(`-${index + 1}`)
		}
		return item
	})

	updateDeviceInitParam({
		'device_name': val,
		'data': allRowData
	}).then(res => {
		if (res.code == 1) {
			message.success(res.message || t('保存成功') + '！')
			state.isedit = false
			storeRoute.setTabs(route.fullPath, false)
			storeRoute.setSaveTabs(route.fullPath, true)
			if (targetNode) {
				Mitt.emit('onSelectParamsTreeChange', targetNode)
			}
		}
	}).catch(() => {

	})
}

const saveCapacityBalance = () => {
	if (state.routePath !== route.fullPath) return
	gridApi.value.stopEditing()
	const allRowData = []
	gridApi.value.forEachNode(function(node) {
		allRowData.push(node.data)
	})
	saveBaseDataApi({
		'import_string_func': 'teapcase_cb:update_capacity_balance_power_cof_table',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'data': allRowData
		}
	}).then(res => {
		if (res.code == 1) {
			setTimeDeatilData(res.func_result.capacity_balance_power_cof)
		}
	}).catch(() => {

	})
}

const handleSaveAgtable = (saveType) => {
	if (state.routePath !== route.fullPath) return

	if (!state.treeValue || state.treeValue == '') return
	saveAgtable(state.treeValue, saveType)
}
Mitt.on('handleSaveAgtable', handleSaveAgtable)

const stopEditing = (saveType) => {
	gridApi.value.stopEditing()
}
Mitt.on('stopEditing', stopEditing)

defineExpose({ onBtExcludeMedalColumns, setHeaderNames, onBtHide, onBtPinnedOn, setTimeDeatilData, setBlanceData, onCopyClick, onCutClick, onPasteClick,
	onAddRange, undo, redo, clearFilter, getAgTableData, insertRow, removeRow, saveAgtable, saveTimeDeatilData, getParamsAgTable, saveTimeAddData,
	handleParamsReplen, onRemoveSelected, handleTimeMatch, saveInElectron, openReplace, setBlanceGoalsData, onCount, saveParamsAgtable, handleSaveAgtable,
	onBtExport, timeseriesAdd, saveTsimeseriestable, handleCorrelateData, getSelected, saveBalanceGoals, updateData, getAgData, getReadNameData, setCurveTimeData,
	resetParamsAgTable, saveCapacityBalance, handleTypeChange
})

onMounted(async() => {

})

onBeforeMount(() => {
	rowSelection.value = 'multiple'

	tooltipShowDelay.value = 500
	statusBar.value = {
		statusPanels: [

			{ statusPanel: 'agTotalRowCountComponent', align: 'left' },
			{ statusPanel: 'agFilteredRowCountComponent' },
			{ statusPanel: 'agSelectedRowCountComponent' },
			{ statusPanel: 'agAggregationComponent' }
		]
	}

	autoGroupColumnDef.value = {
		minWidth: 220,
		pinned: 'left'
	}

	getDataPath.value = (data) => {
		return data.orgHierarchy
	}
})

</script>
<style lang="scss" scoped>
.ag-grid-body {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  ::-webkit-scrollbar {
    width:16px;
    height: 16px;
  }
  ::-webkit-scrollbar-thumb {
    background: #92c1e4;
      border-radius: 2px;
  }
  ::-webkit-scrollbar-track {
      background: #d1dee8;
      border-radius: 2px;
  }
  ::-webkit-scrollbar-button {
    // background: #92c1e4!important;///////////////////
    width: 16px!important;
    height: 16px!important;
    border: 0;
    // background-image: url('../../assets/bofang.png');
  }

  // ::-webkit-scrollbar-button:single-button {
  //   background-color: #bbbbbb;
  //   display: block;
  //   border-style: solid;
  //   height: 8px;
  //   width: 16px;
  // }

  ::-webkit-scrollbar-button:single-button:vertical:decrement {
    // border-width: 0 8px 8px 8px;
    // border-color: transparent transparent #000 transparent;
    background-image: url('../../assets/scrollbar/up.png');
    background-size: 16px 16px;
    background-repeat: no-repeat;
  }

  // ::-webkit-scrollbar-button:single-button:vertical:decrement:hover {
  //   border-color: transparent transparent #777777 transparent;
  // }

  ::-webkit-scrollbar-button:single-button:vertical:increment {
    background-image: url('../../assets/scrollbar/down.png');
    background-size: 16px 16px;
    background-repeat: no-repeat;
  }
  // ::-webkit-scrollbar-button:vertical:single-button:increment:hover {
  //   border-color: #777777 transparent transparent transparent;
  // }

  ::-webkit-scrollbar-button:horizontal:start {
    background-image: url('../../assets/scrollbar/left.png');
    background-size: 16px 16px;
    background-repeat: no-repeat;
  }

  ::-webkit-scrollbar-button:horizontal:end {
    background-image: url('../../assets/scrollbar/right.png');
    background-size: 16px 16px;
    background-repeat: no-repeat;
  }

  .ag-searchinput {
    position: absolute;
    top: 0;
    right: 0;

    // font-size: 13px;
    @include add-size(13px, $size);
    margin: 5px;
  }
  .cell-act {
    background: rgba(255, 0, 0, 0.1);
  }

  .cell-bud {
    background: rgba(0, 255, 0, 0.1);
  }
  .ag-theme-quartz {
    margin-top: v-bind(tableMarginTop);

    --ag-grid-size: 4px; // 文字或图标  与单元格之间的间隙

    --ag-header-foreground-color: #474747;
    --ag-header-background-color: #F4F4F4;
    --ag-header-column-resize-handle-color: #D7D7D7;

    //--ag-font-size: .875rem;
    --ag-font-size: v-bind(store.agFontSize);

    // @include add-ag-size(14px, $size);
    // --ag-font-family: 'SiYuan Normal';

    --ag-data-color: #474747;
    --ag-background-color: #eef3f5;
    --ag-odd-row-background-color: #eff8ff;

    --ag-active-color: #3481B9;

    --ag-selected-row-background-color: #74B1FA;   // 网格和下拉菜单中所选行的背景色

    --ag-range-selection-border-color: #3481B9;
    --ag-range-selection-background-color: #88B7E2;

    --ag-value-change-value-highlight-background-color: transparent;

    // --ag-range-selection-highlight-color   在从单元格区域复制或粘贴到单元格区域时将其短暂应用于背景色

    // --ag-value-change-delta-up-color  当单元格数据的值在 agAnimateShowChangeCellRenderer 单元格中增加时临时应用于单元格数据的颜色
  }
  .ag-theme-quartz .ag-header-cell {
    font-size: 14px;
  }

}

.modal_source{
  .ant-modal{
    .ant-modal-body{
      >div{
        .modal_content{
          padding: 17px 35px;
          text-align: center;
          .ant-input-number .ant-input-number-input {
            width: 100%;
            height: 35px;
          }
        }

        .modal_btns{
          margin-top: 17px;
          text-align: center;
          button{
            width: 90px;
            height: 30px;
            letter-spacing: 0;
          }
        }

      }
    }
  }
}
</style>
