<template>
  <a-modal
    wrapClassName="modal_replace"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    width="375px"
    :maskClosable="false"
  >
    <div class="user-select">
      <div class="modal_top">
        <p>{{ $t('替换') }}</p>
        <close-outlined class="pointer" @click="emits('cancel')" />
      </div>
      <div class="modal_content relative">
        <a-input v-model:value="state.searchValue" :placeholder="$t('请输入将要筛选的内容')" />
        <p>{{ $t('替换为') }}：</p>
        <a-input v-model:value="state.replaceValue" :placeholder="$t('请输入替换后的内容')" />

        <div class="modal_btns">
          <a-button @click="handleClear"
            type="primary"
            ghost
            size="small"
            :disabled="state.searchValue == '' && state.replaceValue == ''"
          >
            {{ $t('清空') }}
          </a-button>
          <a-button @click="handleCancel" size="small" :style="{margin:'0 17px'}">{{ $t('取消') }}</a-button>
          <a-button @click="handleOk" type="primary" :style="{color:'#fff'}" size="small">{{ $t('确认') }}</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script setup>
import { reactive, defineEmits, onMounted } from 'vue'

const emits = defineEmits(['cancel', 'confirm'])

const state = reactive({
	visible: true,
	searchValue: null,
	replaceValue: null
})

const handleOk = () => {
	if (state.searchValue == null || state.replaceValue == null) return
	emits('confirm', state.searchValue, state.replaceValue)
}

const handleCancel = () => {
	emits('cancel')
}
const closeModal = () => {
	emits('cancel')
}

const handleClear = () => {
	state.searchValue = null
	state.replaceValue = null
}

onMounted(() => {

})

</script>
<style lang="scss" scoped>
  .modal_replace{
    .ant-modal{
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 17px 35px;
            text-align: center;
            .ant-input-number .ant-input-number-input {
              width: 100%;
              height: 35px;
            }
          }

          .modal_btns{
            margin-top: 17px;
            text-align: center;
            button{
              width: 90px;
              height: 30px;
              letter-spacing: 0;
            }
          }

        }
      }
    }
  }
</style>
