<template>
  <a-modal
    wrapClassName="modal_source_city"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    width="400px"
    :maskClosable="false"
  >
    <div class="user-select">
      <div class="modal_top">
        <p>{{ $t('所在城市') }}</p>
        <close-outlined class="pointer" @click="emits('cancel')" />
      </div>
      <div class="modal_content relative">
        <a-form
          :model="formState"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
          autocomplete="off"
        >
          <a-form-item
            :label="`${ $t('省/直辖市') }`"
            name="province"
            :rules="[{ required: true, message:$t('请选择省/直辖市')+ '!' }]"
          >
            <a-select
              v-model:value="formState.province"
              show-search
              :placeholder="$t('请选择')"
              :options="state.provinceList"
              :filter-option="filterOption"
              style="width: 100%"
              @change="changeProvince"
            ></a-select>
          </a-form-item>

          <a-form-item
            :label="`${ $t('市') }`"
            name="city"
          >
            <a-select
              v-model:value="formState.city"
              show-search
              :placeholder="$t('请选择')"
              :options="state.cityList"
              :filter-option="filterCityOption"
              style="width: 100%"
            ></a-select>
          </a-form-item>
        </a-form>

        <div class="modal_btns">
          <a-button @click="handleClear"
            type="primary"
            ghost
            size="small"
            :disabled="!formState.province && !formState.city"
          >
            {{ $t('清空') }}
          </a-button>
          <a-button @click="closeModal" size="small" :style="{margin:'0 17px'}">{{ $t('取消') }}</a-button>
          <a-button @click="handleOk" type="primary" :style="{color:'#fff'}" size="small">{{ $t('确认') }}</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script setup>
import { reactive, defineEmits, onMounted } from 'vue'

import { getBaseDataApi } from '@/api/exampleApi'

const emits = defineEmits(['cancel', 'confirm'])

const state = reactive({
	visible: true,
	provinceList: [],
	cityObj: {},
	cityList: []
})

const formState = reactive({
	province: '',
	city: ''
})

const filterOption = (input, option) => {
	return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}
const filterCityOption = (input, option) => {
	return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const changeProvince = () => {
	formState.city = undefined
	state.cityList = state.cityObj[formState.province]
}

const handleOk = () => {
	if (!formState.province && !formState.city) return
	let areaName = ''
	let center
	if (!formState.city) {
		areaName = formState.province
		center = state.provinceList.find(item => item.value == formState.province).center
	} else {
		areaName = formState.province + '-' + formState.city
		center = state.cityObj[formState.province].find(item => item.value == formState.city).center
	}

	emits('confirm', {
		areaName,
		center
	})
}

const closeModal = () => {
	emits('cancel')
}

const handleClear = () => {
	formState.province = ''
	formState.city = ''
}

const getListAllCity = () => {
	getBaseDataApi({
		'import_string_func': 'teapcase:list_all_city',
		'func_arg_dict': {}
	}).then(res => {
		if (res.code == 1) {
			const { func_result } = res
			state.provinceList = func_result
			const obj = {}
			func_result.forEach((item) => {
				obj[item.label] = item.children ? item.children : []
			})
			state.cityObj = obj
		}
	})
}

onMounted(() => {
	getListAllCity()
})

</script>
<style lang="scss" scoped>
  .modal_source_city{
    .ant-modal{
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 17px 35px;
            text-align: center;
            .ant-input-number .ant-input-number-input {
              width: 100%;
              height: 35px;
            }
          }

          .modal_btns{
            margin-top: 17px;
            text-align: center;
            button{
              width: 90px;
              height: 30px;
              letter-spacing: 0;
            }
          }

        }
      }
    }
  }
</style>
