<template>
<a-modal
  wrapClassName="modal_sourceType"
  :afterClose="closeModal"
  :centered="true"
  v-model:open="state.visible"
  :footer="null"
  :closable="false"
  width="375px"
  :maskClosable="false"
>
  <div class="user-select">
    <div class="modal_top">
      <p>{{ $t('类型数据源') }}</p>
      <close-outlined class="pointer" @click="emits('cancel')" />
    </div>
    <div class="modal_content relative">
      <a-select
        v-model:value="sourceValue"
        show-search
        :placeholder="$t('请输入')"
        :options="options"
        :filter-option="filterOption"
        style="width: 100%"
      ></a-select>

      <div class="modal_btns">
        <a-button @click="handleClear"
          type="primary"
          ghost
          size="small"
          :disabled="sourceValue == null"
        >
          {{ $t('清空') }}
        </a-button>
        <a-button @click="closeModal" size="small" :style="{margin:'0 17px'}">{{ $t('取消') }}</a-button>
        <a-button @click="handleOk" type="primary" :style="{color:'#fff'}" size="small">{{ $t('确认') }}</a-button>
      </div>
    </div>
  </div>
</a-modal>
</template>
<script setup>
import { ref, reactive, defineEmits, onMounted } from 'vue'
import { useRoute } from 'vue-router'

import { getReadNameCol } from '@/api/exampleApi'

const emits = defineEmits(['cancel', 'confirm'])
const route = useRoute()

const state = reactive({
	visible: true
})
const sourceValue = ref(null)
const options = ref([])

const filterOption = (input, option) => {
	return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const handleOk = () => {
	if (sourceValue.value == null) return
	emits('confirm', sourceValue.value)
}

const closeModal = () => {
	emits('cancel')
}

const handleClear = () => {
	sourceValue.value = null
}

const getReadNameColData = (isEdit, treeNode) => {
	getReadNameCol({
		'import_string_func': 'teapcase:read_name_col_from_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_names': null
		}
	}).then(res => {
		if (res.code == 1) {
			const { type, ts_type } = res.func_result
			if (isEdit == 'itemizedTimeseries') {
				const treeNodeStr = treeNode.split('.')[0]
				options.value = ts_type.ts_type_config_dict[treeNodeStr]
			} else {
				if (sessionStorage.getItem('treeType').includes('gen')) {
					options.value = type.gen
				}
				if (sessionStorage.getItem('treeType').includes('stogen')) {
					options.value = type.stogen
				}
				if (sessionStorage.getItem('treeType').includes('storage')) {
					options.value = type.storage
				}
				if (sessionStorage.getItem('treeType').includes('csp')) {
					options.value = type.csp
				}
				if (sessionStorage.getItem('treeType') == 'timeseries') {
					options.value = ts_type.ts_type_config_list
				}
			}
		}
	})
}

defineExpose({ getReadNameColData })

onMounted(() => {

})

</script>
<style lang="scss" scoped>
.modal_sourceType{
  .ant-modal{
    .ant-modal-body{
      >div{
        .modal_content{
          padding: 17px 35px;
          text-align: center;
          .ant-input-number .ant-input-number-input {
            width: 100%;
            height: 35px;
          }
        }

        .modal_btns{
          margin-top: 17px;
          text-align: center;
          button{
            width: 90px;
            height: 30px;
            letter-spacing: 0;
          }
        }

      }
    }
  }
}
</style>
