<template>
  <a-modal
    wrapClassName="modal_time_detail"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="visible"
    :footer="null"
    :closable="false"
    :maskClosable="false"
  >
  	<screen-scale>
      <div class="modal_top">
        <p>{{ $t('时序详情') }}</p>
        <close-outlined class="pointer" @click="emits('cancel')" />
      </div>
      <div class="modal_content relative">
        <div class="btn_box">
          <a-tooltip>
            <template #title>{{ state.timeseriesName }}</template>
            <span>{{ $t('名称') }}：{{ formatCloud(state.timeseriesName) }}</span>
          </a-tooltip>
          <span class="tableBar_btn"> {{ $t('曲线类型') }}：{{ state.ts_type[state.timeseriesType] }}</span>
          <span class="tableBar_btn"> {{ $t('数据类型') }}：{{ state.timeseriesDataType }}</span>
          <a-tooltip>
            <template #title>{{ state.timeseriesScene }}</template>
            <span class="tableBar_btn"> {{ $t('计算场景') }}：{{ formatCloud(state.timeseriesScene) }}</span>
          </a-tooltip>
          <span class="tableBar_btn"> {{ $t('显示形式') }}：</span>
          <a-radio-group
            v-model:value="state.timeseriesDisplay"
            size="small"
            button-style="solid"
            @change="changeTimeseriesDisplay"
          >
            <a-radio-button value="day">{{ $t('24列') }}</a-radio-button>
            <a-radio-button value="hour">{{ $t('单列') }}</a-radio-button>
          </a-radio-group>
          <a-button @click="handleCount" type="primary" :style="{color:'#fff',marginLeft: '15px'}" size="small">{{ $t('计算') }}</a-button>
          <a-button @click="handleReplace" type="primary" :style="{color:'#fff',marginLeft: '15px'}" size="small">{{ $t('替换') }}</a-button>
        </div>
        <div class="agClass">
          <!-- <AgGrid ref="agGridRef" :isTimeseriesDetail="true"></AgGrid> -->
          <AgGrid ref="agGridRef"></AgGrid>
        </div>
        <div class="modal_btns">
          <a-button @click="handleOk" type="primary" :style="{color:'#fff',marginLeft: '15px'}" >{{ $t('保存修改') }}</a-button>
        </div>
      </div>
	</screen-scale>
  </a-modal>
</template>
<script setup>
import { ref, reactive, toRef, defineProps, defineEmits, onMounted, nextTick, computed } from 'vue'

import { basicApi, globalParameters } from '@/api/exampleApi'

import { useI18n } from 'vue-i18n'
const { t } = useI18n()

const props = defineProps(['visible', 'sourceType'])
const visible = toRef(props, 'visible')

const emits = defineEmits(['cancel', 'confirm'])

const agGridRef = ref(null)

const state = reactive({
	id: '',
	routeId: '',
	timeseriesName: '',
	timeseriesType: '',
	timeseriesScene: '',
	timeseriesDataType: '',
	timeseriesDisplay: 'day',
	totalHours: 8760,
	mod_24_col_data: [],
	mod_single_col_data: [],
	ts_type: {}
})

const closeModal = () => {
	emits('cancel')
}

const formatCloud = computed(() => {
	return function(title) {
		if (title.length > 5) {
			return title.substring(0, 5) + '...'
		} else {
			return title
		}
	}
})

const handleCount = () => {
	nextTick(() => {
		agGridRef.value.onCount()
	})
}

const handleReplace = () => {
	nextTick(() => {
		agGridRef.value.openReplace()
	})
}

const changeTimeseriesDisplay = () => {
	getTimeseriesDetailDta()
}

const getTimeseriesDetailDta = () => {
	basicApi({
		'import_string_func': 'teapcase:read_one_ts_value_from_tc',
		'func_arg_dict': {
			'file_name': state.routeId,
			'row_id': state.id,
			'table_view': true
		}
	}).then(res => {
		if (res.code == 1) {
			const { mod_24_col_data, mod_single_col_data, ts_type } = res.func_result
			state.mod_24_col_data = mod_24_col_data
			state.mod_single_col_data = mod_single_col_data
			state.mod_24_col_data.columns.forEach((item, index) => {
				if (item.field == 'day') {
					item.width = 150
				} else {
					item.width = 80
				}
			})
			state.mod_single_col_data.columns.forEach((item, index) => {
				if (item.field == 'time') {
					item.width = 200
				} else {
					item.width = 80
				}
			})

			state.mod_24_col_data.data.forEach((item, index) => {
				item.index = index
			})
			state.mod_single_col_data.data.forEach((item, index) => {
				item.index = index
			})

			state.ts_type = ts_type.ts_type_name_map

			nextTick(() => {
				agGridRef.value.setTimeDeatilData(state.timeseriesDisplay == 'day' ? state.mod_24_col_data : state.mod_single_col_data)
			})
		}
	})
}

const getTimeseriesInit = (row, routeId) => {
	state.id = row.index
	state.timeseriesName = row.name
	state.timeseriesType = row.type
	state.timeseriesScene = row.scenario
	state.timeseriesDataType = row.value_type == 'multiply' ? t('数据倍乘') : t('数据替换')
	state.routeId = routeId
	getGlobalParameters(routeId)
	getTimeseriesDetailDta()
}

const getGlobalParameters = (routeId) => {
	globalParameters(
		{
			'import_string_func': 'teapcase:read_from_tc',
			'func_arg_dict': {
				'file_name': routeId,
				'sheet_name': 'parameter'
			}
		}
	).then(res => {
		const { parameter } = res.func_result

		const startDate = new Date(parameter.data.case_info.start_datetime)
		const endDate = new Date(parameter.data.case_info.end_datetime.split(' ')[0] + ' 23:59:59')
		const totalMilliseconds = endDate - startDate
		const totalHours = totalMilliseconds / (1000 * 60 * 60)
		state.totalHours = Math.ceil(totalHours)
	})
}

const handleOk = () => {
	agGridRef.value.saveTimeDeatilData(state.id, state.totalHours)
}
defineExpose({ getTimeseriesInit })
onMounted(() => {

})

</script>
<style lang="scss">
.modal_time_detail{
  .ant-modal{
    width: 72%!important;
    .ant-modal-body{
      >div{
        .modal_content{
          height: 540px;
          padding: 0px 30px 20px 30px;
          text-align: center;
          position: relative;
          .btn_box {
            position: absolute;
            top: 0;
            left: 30px;
            z-index: 33;
            margin-top: 8px;
            text-align: left;
            .tableBar_btn {
              margin-left: 10px;
            }
          }
        }
        .agClass {
          width: 100%;
          height: 480px;
        }
        .modal_btns {
          text-align: right;
          margin-top: 15px;
        }

      }
    }
  }
}
</style>
