<template>
  <a-modal
    wrapClassName="modal_confirm"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    width="375px"
    :maskClosable="false"
  >
  <div class="user-select">
      <div class="modal_top">
        <p>{{ props.confirmTitle }}</p>
        <close-outlined class="pointer" @click="emits('close')" />
      </div>
      <div class="modal_content relative">
        <p>{{ props.prompt }}</p>
        <div class="modal_btns">
          <a-button @click="handleclose" size="small">{{ $t('取消') }}</a-button>
          <a-button @click="handleOk" type="primary" :style="{margin:'0 17px'}" ghost size="small">{{ props.okText }}</a-button>
          <a-button @click="handleCancel" type="primary" size="small" > {{ props.cancelText }}</a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script setup>
import { reactive, defineEmits, onMounted } from 'vue'
import { t } from '@/utils/common'

const emits = defineEmits(['cancel', 'close', 'confirm'])

const props = defineProps({
	confirmTitle: {
		type: String,
		default: t('提示')
	},
	prompt: {
		type: String,
		default: t('请确认当前操作') + '!'
	},
	okText: {
		type: String,
		default: t('确认')
	},
	cancelText: {
		type: String,
		default: t('取消')
	}
})

const state = reactive({
	visible: true
})

const handleOk = () => {
	emits('confirm')
}

const handleCancel = () => {
	emits('cancel')
}

const handleclose = () => {
	emits('close')
}

onMounted(() => {

})

</script>
<style lang="scss" scoped>
  .modal_confirm{
    .ant-modal{
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 17px 35px;
            text-align: center;
            .ant-input-number .ant-input-number-input {
              width: 100%;
              height: 35px;
            }
            img {
              width: 36px;
              height: 36px;
            }
          }

          .modal_btns{
            margin-top: 17px;
            text-align: center;
            button{
              width: 90px;
              height: 30px;
              letter-spacing: 0;
            }
          }

        }
      }
    }
  }
</style>
