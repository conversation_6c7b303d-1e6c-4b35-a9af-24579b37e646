<template>
  <a-modal
    wrapClassName="modal_balance_time"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    :maskClosable="false"
  >
    <screen-scale>
      <div class="modal_top">
        <p>{{ $t('自定义盈亏时段电力平衡表') }}</p>
        <close-outlined class="pointer" @click="emits('cancel')" />
      </div>
      <div class="modal_content relative">
        <div class="downLoad" @click="downLoadClick"><DownloadOutlined /></div>
        <div class="datePicker">
          <a-date-picker v-model:value="state.dateValue" @change="dateChange" :disabled-date="disabledDate" valueFormat="YYYY-MM-DD"/>
          <a-select
            v-model:value="state.timeValue"
            style="width: 80px;margin-left: 15px;"
            :options="state.timeOptions"
            :disabled="!state.dateValue"
            @change="dateChange"
          >
            <template #suffixIcon><ClockCircleOutlined /></template>
          </a-select>
        </div>
        <!-- <div class="balance_title">
          {{ state.balance_title }}
          <div class="unitBox">
            <a-select
              ref="select"
              size="small"
              :bordered="false"
              v-model:value="state.unitValue"
              style="width: 100px"
              :options="state.unitOptions"
              @change="handleUnitChange"
            ></a-select>
          </div>

        </div> -->

        <div class="neps_table">
          <AgGrid ref="agGridRef" :isEdit="'balanceSheet'" :istree="true"></AgGrid>
        </div>
      </div>
    </screen-scale>
  </a-modal>
</template>
<script setup>
import { ref, reactive, defineEmits, onMounted, nextTick } from 'vue'

import { GetSimulationTaskResult } from '@/api/index'
import { GetMidTermTaskResult } from '@/api/exampleApi'
import { generateDateTimeArray } from '@/utils/teap.js'
import { useRoute } from 'vue-router'

const route = useRoute()

const emits = defineEmits(['cancel', 'confirm'])

const agGridRef = ref(null)

const state = reactive({
	visible: true,
	zoom: 1,
	id: '',
	routeId: '',
	timeseriesName: '',
	timeseriesType: '',
	timeseriesScene: '',
	timeseriesDataType: '',
	timeseriesDisplay: 'day',
	caseStartDate: '',
	caseEndDate: '',
	totalHours: 8760,
	mod_24_col_data: [],
	mod_single_col_data: [],
	ts_type: {},
	caseDate: '',
	dateValue: null,
	timeValue: '00',
	initIndex: 0,
	timeOptions: [
		{ value: '00', label: '00' },
		{ value: '01', label: '01' },
		{ value: '02', label: '02' },
		{ value: '03', label: '03' },
		{ value: '04', label: '04' },
		{ value: '05', label: '05' },
		{ value: '06', label: '06' },
		{ value: '07', label: '07' },
		{ value: '08', label: '08' },
		{ value: '09', label: '09' },
		{ value: '10', label: '10' },
		{ value: '11', label: '11' },
		{ value: '12', label: '12' },
		{ value: '13', label: '13' },
		{ value: '14', label: '14' },
		{ value: '15', label: '15' },
		{ value: '16', label: '16' },
		{ value: '17', label: '17' },
		{ value: '18', label: '18' },
		{ value: '19', label: '19' },
		{ value: '20', label: '20' },
		{ value: '21', label: '21' },
		{ value: '22', label: '22' },
		{ value: '23', label: '23' }
	]
})

const disabledDate = (current) => {
	return current < new Date(state.caseStartDate) || current > new Date(state.caseEndDate)
}

const closeModal = () => {
	emits('cancel')
}

const downLoadClick = () => {
	const fileName = route.query.name.split('-')
	agGridRef.value.onBtExport(`${fileName[1]}-${fileName[2]}.xlsx`)
}

const dateChange = () => {
	const years = generateDateTimeArray(state.caseDate, false, true)
	const startIndex = years.findIndex(item => item == state.dateValue.substring(5) + ' ' + state.timeValue + ':00:00')
	getBalanceTime(startIndex - state.initIndex)
}

const getBalanceTime = (startIndex) => {
	const url = route.query.type == 'mid_term' ? '/backend/teap_api_v3/get_mid_term_task_result/' : '/backend/teap_api_v3/get_long_term_task_result/'
	const tempQuery = {
		'group': `_result.balance_df.neps_alltime_power.${startIndex}`,
		'result_file_path': route.query.filePath
	}
	GetSimulationTaskResult(url, tempQuery).then(res => {
		const { columns, data } = res
		const tableData = {
			columns,
			data
		}
		nextTick(() => {
			agGridRef.value.setBlanceData(tableData)
		})
	})
}

const getMidTermResult = () => {
	GetMidTermTaskResult({
		'group': 'parameter',
		'result_file_path': route.query.filePath
	}).then(res => {
		const { case_info } = res.data
		console.log(123, res.data[case_info.sim_mode].simulation.start_datetime)
		const startDate = res.data[case_info.sim_mode].simulation.start_datetime
		const endDate = res.data[case_info.sim_mode].simulation.end_datetime
		state.caseDate = startDate.substring(0, 4)
		state.startYear = startDate.substring(0, 4)
		state.dateValue = startDate.substring(0, 10)
		state.startTime = startDate.substring(0, 10)

		const years = generateDateTimeArray(state.caseDate, false, true)
		state.initIndex = years.findIndex(item => item == state.dateValue.substring(5) + ' ' + state.timeValue + ':00:00')

		state.caseStartDate = startDate
		state.caseEndDate = endDate
		console.log(state.dateValue)
		dateChange()
	})
}
defineExpose({ })
onMounted(() => {
	getMidTermResult()
})

</script>
<style lang="scss">
.modal_balance_time{
  .ant-modal{
    width: 72%!important;
    .ant-modal-body{
      >div{
        .modal_content{
          height: 540px;
          padding: 0px 30px 20px 30px;
          text-align: center;
          position: relative;
          .datePicker {
            position: absolute;
            top: 0px;
            left: 30px;
            z-index: 30;
            font-size: 18px;
          }
          .downLoad {
            position: absolute;
            top: 5px;
            right: 320px;
            z-index: 30;
            font-size: 18px;
            // color: #1f7fed;
          }
          // .balance_title {
          //   position: absolute;
          //   top: -5px;
          //   left: 50%;
          //   transform: translate(-50%, 0);
          //   z-index: 30;
          //   width: 40%;
          //   height: 40px;
          //   border: 1px solid #999;
          //   border-radius: 4px;
          //   line-height: 40px;
          //   text-align: center;
          //   margin: 0 auto;
          //   // position: relative;
          // }
          .unitBox {
            position: absolute;
            top: 0;
            right: 0;
          }
          .neps_table {
            margin-top: 10px;
            width: 100%;
            height: 100%;
          }

        }
        .agClass {
          width: 100%;
          height: 480px;
        }
        // .modal_btns {
        //   text-align: right;
        //   margin-top: 15px;
        // }

      }
    }
  }
}
</style>
