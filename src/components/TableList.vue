<template>
  <div class="table_list relative user-select">
    <div ref="tableBoxRef" :class="state.logShow? 'table-box':'table-box1'">
      <a-table id="table"
        :pagination="false"
        @change="handleChange"
        rowKey="id"
        ref="scroll_table"
        :loading="state.loading"
        :rowClassName="rowClassName"
        :dataSource="props.type==1?state.fliterTable:state.tableData"
        :scroll="{y:state.tableHeight2}"
        :customRow="customRow"
        :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange,onSelect:onSelect,columnWidth:36 }"
        :columns="props.type==1?task_columns:result_columns">
        <template #bodyCell="{ column,record,text}">
            <template v-if="['status_id'].includes(column.dataIndex)">
              <div class="center">
                <div class="table_state center">
                  <a-tooltip :mouseLeaveDelay="0">
                    <template #title>{{ $t('开始算例') }}</template>
                    <div class="wrap_span bofang" @click.stop="startCase(record)" v-show="record.status_operation_id==0">

                    </div>
                  </a-tooltip>
                  <a-tooltip :mouseLeaveDelay="0">
                    <template #title>{{ $t('停止算例') }}</template>
                    <div class="wrap_span" v-show="record.status_operation_id==2">
                      <pause-outlined @click.stop="pauseCal(record.id)"/>
                    </div>
                  </a-tooltip>
                  <a-tooltip :mouseLeaveDelay="0">
                    <template #title>{{ $t('停止算例') }}</template>
                    <div class="wrap_span" v-show="record.status_operation_id==1">
                      <span class="stop" @click.stop="pauseCal(record.id)"></span>
                    </div>
                  </a-tooltip>
                </div>
                <div class="table_state_name center" v-show="record.status_id!=1">
                  <span :class="getState(record.status_id).className">{{ getState(record.status_id).name }}</span>
                </div>
                <div class="center" v-show="record.status_id==1&&record.job_type_id==3">
                  <div :style="{gridTemplateColumns:(state.jobExecPercent)+'fr '+(100-state.jobExecPercent)+'fr '}" class="cal_animate">
                      <span></span>
                      <span></span>
                      <p :style="{backgroundImage: 'linear-gradient(to right,#ADDED6 0%,#ADDED6 '+state.jobExecPercent+'%,#27b148 '+state.jobExecPercent+'%,#27b148 100%)'}">{{state.jobExecPercent+'%'}}</p>
                  </div>
                </div>
                <div @click.stop="state.logShow=true" class="center progress_active_name" v-show="record.status_id==1&& [4,5,6,101,102,103,104].includes(record.job_type_id)">
                  <span>{{ $t('计算中') }}</span>
                </div>
              </div>
            </template>
            <template v-if="['job_type_id'].includes(column.dataIndex)">
                {{ record.job_type_short_name }}
            </template>
            <template v-if="['case_file_name'].includes(column.dataIndex)">
              <div :class="props.type==2?'table_name table_name_result':'table_name'">
                  <a-tooltip :mouseEnterDelay="2" :mouseLeaveDelay="0">
                    <template #title>{{ text.replace(/.xlsx/g,"")}}</template>
                    <p class="ellipsis">{{ text.replace(/.xlsx/g,"") }}</p>
                  </a-tooltip>
                  <a-tooltip :mouseLeaveDelay="0">
                    <template #title>{{ $t('打开结果') }}</template>
                    <div class="wrap_span" v-show="props.type==2" @click.stop="openResult(record)">
                      <FolderOpenOutlined />
                    </div>
                  </a-tooltip>
                  <a-tooltip :mouseLeaveDelay="0">
                    <template #title>{{ $t('下载算例') }}</template>
                    <div :class="['wrap_span']" v-show="props.type==2" @click.stop="downloadResultCase(record)">
                      <cloud-download-outlined/>
                    </div>
                  </a-tooltip>
                  <a-tooltip :mouseLeaveDelay="0">
                    <template #title>{{ $t('打开算例') }}</template>
                      <div :class="['wrap_span',[101,102,103,104].includes(record.job_type_id)?'disabled_span':'']" v-show="props.type==2" @click.stop="openCase(record,'result')">
                        <FileTextOutlined />
                      </div>
                  </a-tooltip>
                </div>
            </template>
            <template v-if="['option'].includes(column.dataIndex)">
                <div class="center">
                  <a-tooltip :open="record.open">
                    <template #title>{{ $t('移动算例') }}</template>
                    <div class="wrap_span move" @click.stop="" v-if="props.type==1&&state.fliterTable.length>1&&(state_search.job_type_id==null||state_search.job_type_id.length==3)" @mouseenter="onMouseenter(record,$event)" @mouseleave="onMouseleave(record,$event)">
                    </div>
                  </a-tooltip>
                  <a-tooltip  :mouseLeaveDelay="0" >
                    <template #title>{{ $t('置顶算例') }}</template>
                    <div class="wrap_span" @click.stop="toUp(record.id)" v-if="props.type==1&&state.fliterTable.length>1&&(state_search.job_type_id==null||state_search.job_type_id.length==3)">
                      <vertical-align-top-outlined />
                    </div>
                  </a-tooltip>
                  <a-tooltip  :mouseLeaveDelay="0">
                    <template #title>{{ $t('下载算例') }}</template>
                    <div class="wrap_span" @click.stop="download(record.id)">
                      <cloud-download-outlined/>
                    </div>
                  </a-tooltip>
                  <a-tooltip :mouseLeaveDelay="0">
                    <template #title>{{ $t('打开算例') }}</template>
                      <div class="wrap_span" @click.stop="openCase(record,'task')">
                        <FileTextOutlined />
                      </div>
                  </a-tooltip>
                </div>
            </template>
            <template v-if="['case_file_note'].includes(column.dataIndex)">
              <div class="table_input align-items-center" v-if="state.selectId==record.id&&state.selectTextId==record.id">
                <a-input ref="note" @keyup="noteKeyup(record.id,state.editVal,$event)" @click.stop="" v-model:value="state.editVal" />
                <a-tooltip>
                    <template #title>{{ $t('取消修改') }}</template>
                  <close-circle-filled class="pointer" @click.stop="state.selectTextId=undefined"/>
                </a-tooltip>
                <a-tooltip>
                    <template #title>{{ $t('确认修改') }}</template>
                  <check-circle-filled class="pointer" @click.stop="confirm(record.id,state.editVal)"/>
                </a-tooltip>
              </div>
              <div class="table_text" v-else @click.stop="openEdit(record.id,record.case_file_note,record)">
                <a-tooltip :mouseEnterDelay="2" :mouseLeaveDelay="0">
                    <template #title>{{ text }}</template>
                    <p class="ellipsis">{{ text }}</p>
                  </a-tooltip>
              </div>
            </template>
        </template>
        <template #footer>

        </template>
      </a-table>
    </div>
    <div :class="state.logShow? 'btn_list btn_list_move':'btn_list'" v-if="props.type==1">
        <a-button :disabled="state.selectedRowKeys.length==0" @click="startSelectCase">{{ $t('开始计算') }}</a-button>
        <a-button :disabled="state.selectedRowKeys.length==0" @click="stopSelectCase">{{ $t('停止计算') }}</a-button>
        <a-button :disabled="state.selectedRowKeys.length==0" @click="deleteCase">{{ $t('删除') }}</a-button>
    </div>
    <div :class="state.logShow? 'table_footer':'table_footer'" v-if="props.type==2">
      <a-pagination :pageSize="20" v-model:current="paginationProps.current" simple  :total="paginationProps.total" @change="changeSize" />
      <a-input-search
          v-model:value="state_search.searchText"
          :placeholder="$t('输入名称或备注')"
          @pressEnter="searchEnter"
          @search="searchEnter"
        />
      <a-button :disabled="state.selectedRowKeys.length==0" @click="deleteCase">{{ $t('删除') }}</a-button>
    </div>
    <div class="textarea" v-show="state.logShow">
      <div class="textarea_header">
        <p>{{ $t('仿真日志') }}</p>
        <DownSquareOutlined @click="state.logShow=false;state.selectId=undefined" />
      </div>
      <div class="textarea_content" @mouseenter="onmouseenter" @mousewheel="onmousewheel" @mousemove="onmousemove" @mouseleave="onmouseleave" ref="textarea" v-html="state.log_value">
    </div>
    </div>
  </div>
</template>
<script setup>

import { computed, onMounted, ref, reactive, watch, nextTick, onUnmounted } from 'vue'
import Mitt from '@/utils/mitt.js'
import { openModal, getFilterData, getLogList } from '@/utils/teap'
import network from '@/config/teap.config'
import message from '@/utils/message'
import { CheckAuthExpire, getLogApiNew, EditNoteApi, SortCaseApi, DownLoadCaseApi, StartCalCaseApi, StopCalCaseApi, DeleteCaseApi, checkBeforeStart, DownloadTempFile } from '@/api/index'
import { GetH5FromTeapFile } from '@/api/exampleApi'
import { GetLineJobTaskResult } from '@/api/gis'
import { downloadApiFile } from '@/utils/common.js'
import { VerticalAlignTopOutlined, CloudDownloadOutlined, CloseCircleFilled, CheckCircleFilled } from '@ant-design/icons-vue'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
import { parseFilePath, debounce } from '@/utils/gis.js'
import { t } from '@/utils/common'
const store = settingStore()
const { wsAcceptType,	sim_job_config, permissionList, jobExecPercent, isChromeHigh } = storeToRefs(store)

const props = defineProps({
	type: {
		type: Number,
		default: 1
	},
	total: {
		type: Number,
		default: undefined
	},
	data: {
		type: Array,
		default: () => []
	},
	debug: {
		type: Boolean,
		default: false
	}
})
const tableBoxRef = ref()
const note = ref()
const textarea = ref()
const ws = ref()
const emit = defineEmits(['initTable', 'pageChange', 'search'])
const state = reactive({
	fliterTable: [],
	selectItem: {},
	selectId: undefined,
	selectTextId: undefined,
	startIndex: undefined,
	endIndex: undefined,
	editVal: '',
	loading: true,
	targetArr: [],
	selectedRowKeys: [],
	selectedTable: [],
	tableData: [],
	logShow: false,
	log_value: '',
	cal_log_value: '',
	cal_log_id: undefined,
	timer: undefined,
	isScroll: true,

	tableHeight2: undefined,
	btnLock: true,
	sim_job_config,
	permissionList,
	jobExecPercent
})
const state_search = reactive({
	searchText: '',
	job_type_id: undefined
})
const paginationProps = reactive({
	pageSize: 20,
	current: 1,
	total: 1
})
const scroll_table = ref()
const task_columns = ref()
const result_columns = ref()
const screenScale = () => {
	nextTick(() => {
		state.tableHeight2 = (tableBoxRef.value.clientHeight - 32 - 8)
	})
}
const debouncedScreenScale = debounce(screenScale, 250)
const changePageSize = (val) => {
	paginationProps.current = val
}
const openResult = (record) => {
	Mitt.emit('openResultView', record)
}
const openCase = (record, type) => {
	if (state.btnLock) {
		state.btnLock = false
		setTimeout(() => {
			state.btnLock = true
			type == 'result' ? Mitt.emit('openResultCase', record) : Mitt.emit('openTaskCase', record)
		}, 700)
	}
}
const onmouseenter = (e) => {
	if (state.timer) return
	state.timer = setTimeout(() => {
		state.isScroll = true
	}, 5000)
}
const onmouseleave = (e) => {
	clearTimeout(state.timer)
	state.isScroll = true
}
const onmousemove = (e) => {
	state.isScroll = false
	clearTimeout(state.timer)
	state.timer = setTimeout(() => {
		state.isScroll = true
	}, 5000)
}
const onmousewheel = (e) => {
	state.isScroll = false
	clearTimeout(state.timer)
	state.timer = setTimeout(() => {
		state.isScroll = true
	}, 5000)
}
const download = (id) => {
	state.loading = true
	DownLoadCaseApi({
		task_record_id: id
	}).then(res => {
		state.loading = false
		downloadApiFile(res)
	}).catch(() => {
		state.loading = false
	})
}

const downloadResultCase = (record) => {
	if ([101, 102, 103, 104].includes(record.job_type_id)) {
		state.loading = true
		GetLineJobTaskResult({
			result_file_path: record.result_file_path,
			group: 'case_file'
		}).then(res => {
			state.loading = false
			DownloadTempFile(parseFilePath(res.data)).then(res1 => {
				downloadApiFile(res1)
			})
		}).catch(() => {
			state.loading = false
		})
	} else {
		const fileName = record.case_file_name
		state.loading = true
		GetH5FromTeapFile({
			result_file_path: record.result_file_path,
			tc_file_name: `${fileName}.tc`,
			download_flag: true
		}, true).then(res => {
			downloadApiFile(res)
			state.loading = false
		}).catch(() => {
			state.loading = false
		})
	}
}
const deleteCase = () => {
	if (props.type == 1 && state.selectedTable.find(item => !item.status_batch_operation_id_list.includes(2))) {
		message.warning(t('计算中及停止中的算例无法删除，请等待状态切换后重试'))
		return
	}
	openModal(t('提示') + ': ' + t('确认删除选中算例?')).then(res => {
		DeleteCaseApi({
			task_record_id_list: state.selectedTable.map(item => item.id)
		}).then(res => {
			if (res.code == 1) {
				message.success(t('删除算例成功'))
				if (state.selectedRowKeys.includes(state.selectId)) {
					state.logShow = false
					state.selectId = undefined
					state.log_value = ''
				}
				state.selectedRowKeys = []
				state.selectedTable = []
				emit('initTable', 'deleteSelect')
			} else {
				message.success(t('删除算例失败'))
			}
		})
	})
}

const startSelectCase = async() => {
	if (state.selectedTable.find(item => !item.status_batch_operation_id_list.includes(0))) {
		message.warning(t('计算中等待中及停止中的算例无法计算，请等待状态切换后重试'))
		return
	}
	const res = await CheckAuthExpire()
	if (!res.auth_expired_flag) {
		message.error(res.message)
		return
	}
	checkBeforeStart().then(res => {
		if (res.code == 1) {
			startCals()
		} else {
			openModal(t('提示') + ': ' + res.message).then(res => {
				startCals()
			})
		}
	}).catch(() => {
		openModal(t('提示') + ': ' + res.message).then(res => {
			startCals()
		})
	})
}
const startCals = () => {
	StartCalCaseApi({
		task_record_id_list: state.selectedTable.map(item => item.id)
	}).then(res => {
		if (res.code == 1) {
			message.success(t('算例开始成功'))
			state.selectedRowKeys = []
			state.selectedTable = []
			emit('initTable', 'startSelect')
		} else {
			message.error(t('算例开始失败'))
		}
	})
}
const stopSelectCase = () => {
	if (
		state.selectedTable.find(item => !item.status_batch_operation_id_list.includes(1))) {
		message.warning(t('未开始及停止中的算例无法停止，请等待状态切换后重试'))
		return
	}
	StopCalCaseApi({
		task_record_id_list: state.selectedTable.map(item => item.id)
	}).then(res => {
		if (res.code == 1) {
			message.success(t('算例停止成功'))
			state.selectedRowKeys = []
			state.selectedTable = []
			emit('initTable', 'stopSelect')
		} else {
			message.error(t('算例停止失败'))
		}
	})
}
const startCal = (id) => {
	StartCalCaseApi({
		task_record_id_list: [id]
	}).then(res => {
		if (res.code == 1) {
			message.success(t('算例开始成功'))
			if (state.selectId == id) {
				state.log_value = ''
			}
			emit('initTable', 'start')
		} else {
			state.loading = false
		}
	}).catch(() => {
		state.loading = false
	})
}
const toUp = (id) => {
	state.loading = true
	SortCaseApi({
		task_record_id_list: [id].concat(state.tableData.filter(items => items.id != id).map(item => item.id))
	}).then(res => {
		if (res.code == 1) {
			emit('initTable', 'sort')
		}
		state.loading = false
	}).catch(() => {
		state.loading = false
	})
}
const startCase = async(record) => {
	const res = await CheckAuthExpire()
	if (!res.auth_expired_flag) {
		message.error(res.message)
		return
	}
	state.loading = true
	checkBeforeStart().then(res => {
		if (res.code == 0) {
			openModal(t('提示') + res.message).then(res => {
				startCal(record.id)
			}).catch(() => {
				state.loading = false
			})
		} else {
			startCal(record.id)
		}
	}).catch(() => {
		openModal(t('提示') + res.message).then(res => {
			startCal(record.id)
		}).catch(() => {
			state.loading = false
		})
	})
}
const pauseCal = (id) => {
	state.loading = true
	StopCalCaseApi({
		task_record_id_list: [id]
	}).then(res => {
		if (res.code == 1) {
			message.success(t('算例停止成功'))
			emit('initTable', 'stop')
		} else {
			state.loading = false
		}
	}).catch(() => {
		state.loading = false
	})
}
const noteKeyup = (id, name, event) => {
	if (event.key == 'Escape') {
		state.selectTextId = undefined
	} else if (event.key == 'Enter') {
		confirm(id, name)
	}
}
const getState = computed(() => (val) => {
	let name = ''
	let className = ''
	if (val == 0) {
		name = t('未开始')
		className = 'unStart'
	} else if (val == 1) {
		name = t('计算中')
		className = 'computed'
	} else if (val == 2) {
		name = t('等待')
		className = 'loading'
	} else if (val == 3) {
		name = t('失败')
		className = 'fail'
	} else {
		name = t('停止中')
		className = 'stop'
	}

	return { name, className }
})
const confirm = (id, val) => {
	state.loading = true
	EditNoteApi({
		task_record_id: id,
		case_file_note: val
	}).then(res => {
		if (res.code == 1) {
			message.success(t('修改备注成功'))
			state.selectTextId = undefined
			emit('initTable', 'editText')
		} else {
			state.loading = false
		}
	}).catch(() => {
		state.loading = false
	})
}
const changeSize = (page, pageSize) => {
	paginationProps.current = page
	state.loading = true
	emit('pageChange', paginationProps)
}
const searchEnter = () => {
	paginationProps.current = 1
	if (props.type == 1) {
		// console.log('searchEnter', state_search)
	} else {
		state.loading = true
		emit('search', state_search)
	}
}
const initData = () => {
	if (props.type == 1) {
		const find = props.data.find(item => item.status_id == 1)
		if (find) starkCalLog(find.id)
		state.tableData = props.data.map((item, index) => {
			return Object.assign(item, { index: index + 1, open: false })
		})
		const selectItemNew = state.selectId ? state.tableData.find(item => item.id == state.selectId) : undefined
		if (selectItemNew && selectItemNew.status_id != state.selectItem.status_id) {
			state.selectItem = selectItemNew
			if (selectItemNew.status_id == 3) {
				if (!state.cal_log_id) {
					startLog()
				}
			} else if (selectItemNew.status_id == 0) {
				state.logShow = false
			}
		}
		if (state.selectId && !selectItemNew) {
			state.selectId = undefined
			state.logShow = false
		}
		if (state.selectedTable.length > 0) {
			state.selectedTable = state.tableData.filter(item => state.selectedTable.find(items => items.id == item.id))
		}
		state.fliterTable = getFilterData(state_search, state.tableData)
	} else {
		state.tableData = props.data.map((item, index) => {
			return Object.assign(item, { index: index + 1, open: false })
		})
	}
	state.loading = false
}
const handleChange = (pagination, filters, sorter) => {
	state_search.job_type_id = filters.job_type_id
	if (props.type == 1) {
		state.fliterTable = getFilterData(state_search, state.tableData)
	} else {
		state.loading = true
		emit('search', state_search)
	}
}
const onMouseenter = (record, event) => {
	var ev = event || window.event
	record.open = true
	ev.target.closest('tr.ant-table-row').draggable = true
}
const onMouseleave = (record, event) => {
	var ev = event || window.event
	record.open = false
	ev.target.closest('tr.ant-table-row').draggable = false
}
const onSelect = (record, selected, selectedRows, nativeEvent) => {

}
const onSelectChange = (selectedRowKeys, selectedRows) => {
	if (props == 1) {
		state.selectedRowKeys = selectedRowKeys
		state.selectedTable = selectedRows
	} else {
		state.selectedRowKeys = state.selectedRowKeys.filter(item => !state.tableData.find(items => item == items.id)).concat(selectedRowKeys)
		state.selectedTable = state.selectedTable.filter(item => !state.tableData.find(items => item.id == items.id)).concat(selectedRows)
	}
}
const rowClassName = (record) => {
	return record.id === state.selectId ? 'clickRowStyl' : ''
}
const openEdit = (id, text, record) => {
	if (!state.selectId) {
		state.selectId = id

		return
	}
	if (state.selectId != id) {
		state.selectId = id
		state.selectTextId = undefined

		return
	}

	state.selectTextId = id
	state.editVal = text
	nextTick(() => {
		note.value.focus()
	})
}
const closeWs = () => {
	if (ws.value) {
		ws.value.close()
	}
}

const starkCalLog = (id) => {
	if (state.cal_log_id && state.cal_log_id == id) return
	state.cal_log_id = id
	const wsprefix = navigator.userAgent.includes('Electron') ? 'ws://' : location.protocol === 'https:' ? 'wss://' : 'ws://'
	ws.value = new WebSocket(wsprefix + network.websocket_Prefix + '/backend/teap_simulation_ws/' + id + '/')
	ws.value.onmessage = function(event) {
		const data = JSON.parse(event.data)
		state.cal_log_value = state.cal_log_value
			? state.cal_log_value + getLogList(data, wsAcceptType.value.show_error_log, wsAcceptType.value.show_info_log, wsAcceptType.value.show_warning_log) : getLogList(data, wsAcceptType.value.show_error_log, wsAcceptType.value.show_info_log, wsAcceptType.value.show_warning_log)
		if (state.selectId == id) {
			state.logShow = true
			state.log_value = state.cal_log_value
		}
		nextTick(() => {
			if (state.isScroll) {
				textarea.value.scrollTop = textarea.value.scrollHeight
			}
			ws.value.send('teap')
		})
	}
	ws.value.onopen = function() { ws.value.send('teap') }
	ws.value.onclose = function(e) {
		state.cal_log_id = undefined
		state.cal_log_value = ''
	}
	ws.value.onerror = function(error) {
		console.error(t('WebSocket发生错误：'), error)
	}
}
const startLog = () => {
	state.log_value = ''
	state.logShow = true
	getLogApiNew(Object.assign({
		task_record_id: state.selectId
	}
	)).then(res => {
		state.log_value = getLogList(res.task_log_list, wsAcceptType.value.show_error_log, wsAcceptType.value.show_info_log, wsAcceptType.value.show_warning_log)
	})
}
const customRow = (record, index) => {
	return {
		props: {

		},
		style: {

		},
		onClick: () => {
			if (state.selectId == record.id) {
				state.selectId = undefined
				state.selectItem = {}
				state.logShow = false
				return
			}
			state.selectTextId = undefined
			if (props.type == 2) {
				if ((state.selectId == undefined)) {
					state.selectId = record.id
					state.selectItem = record
					startLog()

					setTimeout(() => {
						if (isChromeHigh.value) {
							scroll_table.value.$el.querySelector('.ant-table-body').scrollTop = (36 + 1 / (document.getElementsByClassName('home-body')[0].style.zoom || 1) * (1 / window.devicePixelRatio)) * index
						} else {
							scroll_table.value.$el.querySelector('.ant-table-body').scrollTop = (36 + 1 / (document.body.style.zoom || 1) * (1 / window.devicePixelRatio)) * index
						}
					})
				} else {
					state.selectId = record.id
					state.selectItem = record
					startLog()
				}
			} else {
				state.selectId = record.id
				state.selectItem = record
				if (record.status_id == 1) {
					state.logShow = true
					state.log_value = state.cal_log_value
				} else if (record.status_id == 3) {
					startLog()
				} else {
					state.log_value = ''
					state.logShow = false
				}
			}
		},
		onmousedown: (event) => {

		},
		onmouseup: (event) => {

		},
		onMousemove: (event) => {

		},
		onMouseenter: (event) => {
		},
		onMouseleave: (event) => {
		},
		onDragstart: (event) => {
			record.open = false

			var ev = event || window.event

			ev.stopPropagation()

			state.startIndex = index
		},

		onDragover: (event) => {
			var ev = event || window.event

			ev.preventDefault()
			if (index == state.startIndex) {
				state.targetArr.forEach((item) => {
					item.classList.remove('beforLine')
					item.classList.remove('afterLine')
				})
				return
			}

			var nowLine = ev?.target.closest('tr.ant-table-row ')
			if (!state.targetArr.includes(nowLine)) {
				state.targetArr.push(nowLine)
			}
			if (index > state.startIndex) {
				if (!nowLine.classList.contains('afterLine')) {
					state.targetArr.forEach((item) => {
						item.classList.remove('beforLine')
						item.classList.remove('afterLine')
					})
					nowLine.classList.add('afterLine')
				}
			} else {
				if (!nowLine.classList.contains('beforLine')) {
					state.targetArr.forEach((item) => {
						item.classList.remove('beforLine')
						item.classList.remove('afterLine')
					})
					nowLine.classList.add('beforLine')
				}
			}
		},

		onDrop: (event) => {
			var ev = event || window.event
			ev.stopPropagation()
			state.endIndex = index
			state.targetArr.forEach((item) => {
				item.classList.remove('beforLine')
				item.classList.remove('afterLine')
			})
			state.targetArr = []

			if (state.endIndex == state.startIndex) return

			const item = state.tableData[state.startIndex]
			state.tableData.splice(state.startIndex, 1)
			state.tableData.splice(state.endIndex, 0, item)
			state.loading = true
			SortCaseApi({
				task_record_id_list: state.tableData.map(item => item.id)
			}).then(res => {
				if (res.code == 1) {
					emit('initTable', 'drop')
				} else {
					state.loading = false
				}
			}).catch(() => {
				state.loading = false
			})
		}
	}
}
defineExpose({
	changePageSize, closeWs, screenScale
})
const beforeDestroy = () => {
	clearTimeout(state.timer)
}

watch(() => props.data, v => {
	initData()
})
watch(() => props.total, v => {
	paginationProps.total = v
})
watch(() => state.logShow, (newValues, oldValues) => {
	nextTick(() => {
		screenScale()
	})
})

onMounted(() => {
	screenScale()
	window.addEventListener('beforeunload', beforeDestroy)
	window.addEventListener('resize', debouncedScreenScale)

	const filterArr = state.permissionList.filter(item => Object.keys(state.sim_job_config).includes(item)).map(item => {
		return {
			text: state.sim_job_config[item].full_name,
			value: state.sim_job_config[item].job_id
		}
	})
	task_columns.value = [
		{
			title: t('名称'),
			dataIndex: 'case_file_name',
			key: 'case_file_name',
			width: 189,
			align: 'center'
		},
		{
			title: t('类型'),
			dataIndex: 'job_type_id',
			key: 'job_type_id',
			width: 65,
			align: 'center',
			filters: filterArr

		},
		{
			title: t('状态'),
			dataIndex: 'status_id',
			key: 'status_id',
			width: 96,
			align: 'center'
		},
		{
			title: t('操作'),
			dataIndex: 'option',
			key: 'option',
			width: 90,
			align: 'center'
		},
		{
			title: t('备注'),
			dataIndex: 'case_file_note',
			key: 'case_file_note',

			width: 166,
			align: 'center'
		}
	]
	result_columns.value = [
		{
			title: t('名称'),
			dataIndex: 'case_file_name',
			key: 'case_file_name',
			width: 229,
			align: 'center'
		},
		{
			title: t('类型'),
			dataIndex: 'job_type_id',
			key: 'job_type_id',
			width: 65,
			align: 'center',
			filters: filterArr

		},
		{
			title: t('完成时间'),
			dataIndex: 'job_end_datetime',
			key: 'job_end_datetime',
			width: 142,
			align: 'center'
		},
		{
			title: t('备注'),
			dataIndex: 'case_file_note',
			key: 'case_file_note',
			width: 180,
			align: 'center'
		}
	]
})
onUnmounted(() => {
	window.removeEventListener('beforeunload', beforeDestroy)
	window.removeEventListener('resize', debouncedScreenScale)
})

</script>
<style lang="scss" scoped>
  .table_list{
    height: 100%;
    .cal_animate{
      position: relative;
      display: grid;
      height: 20px;
      width: 50px;
      line-height: 20px;
      border-radius: 5px;
      background: rgb(245, 245, 245);
      overflow: hidden;
      >span:first-child{
        background-color:#27b148;
      }
      >p{
        width: 100%;
        -webkit-background-clip: text;
        background-clip:text;
        color: transparent;
        letter-spacing: 1px;
        font-size: 14px;
        position: absolute;
        text-align: center;
        font-weight: bolder;
      }
    }
    .table_state_name{
      .unStart,.computed,.loading,.fail,.stop{
        color: #fff;
        font-size: 12px;
        width: 50px;
        line-height: 20px;
        border-radius: 5px;
      }
      .unStart{
        background: #919496;
      }
      .computed{
        background: #27b148;
      }
      .loading{
        background: #fcca00;
      }
      .fail{
        background: #ff2525;
      }
      .stop{
        background: #007bff;
      }
    }
    .table_state{
      font-size: 20px;
      img{
        height: 16px;
        width: 16px;
      }
      .stop{
        width: 13px;
        height: 13px;
        background-color: rgba(0, 0, 0, 0.85);
      }
    }
    .table_input{
      input{
        padding: 2px 5px;
      }
      >span{
        color: red;
        margin-left: 5px;
        background: #fff;
        border-radius: 100%;
      }
      >span:last-child{
        color:#27b148;
      }
    }
    .wrap_span{
      width: 22px;
      height: 22px;
      border-radius: 5px;
      display: flex;
      justify-content: center;
      align-items: center;
      &:hover{
        background-color: rgb(232, 233, 233);
        cursor: pointer;
      }
      img{
        height: 16px;
        width: 16px;
      }
    }
	.disabled_span{
		opacity: 0.3;
		pointer-events: none;
	}
    .move{
      background-image: url('@/assets/move.png');
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
    }
    .bofang{
      background-image: url('@/assets/bofang.png');
      background-size: 16px 16px;
      background-repeat: no-repeat;
      background-position: center;
    }
    .table_name{
        text-align: left;
        .ellipsis{
          user-select:text;
          -moz-user-select: text;
          -ms-user-select: text;
          -webkit-user-select: text;
        }
    }
	.table_name_result{
		display: grid;
		grid-template-columns: 7fr 1fr 1fr 1fr;
	}
    .table_text{
      height: 26px;
    }
    :deep(.ant-table){
        .ant-table-cell{
            padding:5px 10px!important;
            line-height: 26px;
        }
        .ant-table-container{
          border: none;
        }
        .ant-table-header{
          table{
            border: 1px solid #f0f0f0;
            border-bottom: none;
          }
        }
        .ant-table-body{
          min-height: 0px;
          &::-webkit-scrollbar{
            width:6px;
            height:6px;
          }
          border-top: none;
		  .ant-table-placeholder td{
			border-bottom: none!important;
		  }
          tr>td:first-child{
            border-left: 1px solid #f0f0f0;
          }
          tr>td{
            transition:none;
          }
        }
        .ant-table-footer{
          padding: 0px;
          border: none;
        }
        .ant-empty-normal{
        //   margin: 200px 0;
        }
        .clickRowStyl{
          td{
            background:rgba(var(--base-color-rgb), 0.35)!important;
          }
        }
        .ant-table-tbody>.clickRowStyl:hover>td{
          background:rgba(var(--base-color-rgb), 0.35);
        }
        .ant-table-cell-row-hover{
          background:rgba(var(--base-color-rgb), 0.2)!important;
        }
    }
    :deep(.beforLine) {
        .ant-table-cell{
            border-top: 1.1px dashed var(--base-color) !important;
        }
    }
    :deep(.afterLine) {
        .ant-table-cell {
            border-bottom: 1px dashed var(--base-color) !important;
        }
    }
    :deep(.ant-input){
      border: none;
    }
    .table-box {
      width: 100%;
      height: calc(100% - 373px - 32px - 8px);
    }
    .table-box1 {
      width: 100%;
      height: calc(100% - 32px - 8px);
    }
    .btn_list{
      display: flex;
      justify-content: space-around;
      margin-bottom: 6px;
      // position: absolute;
    //   bottom: 10px;
      // bottom: 6px;
      width: 100%;
      button{
        width: 150px;
        border-color: transparent;
      }
      button:first-child{
        background-color: var(--base-color);
        color: #fff;
      }
      button:nth-child(2){
        color: var(--base-color);
        border-color: var(--base-color);
      }
      button:last-child{
        background-color: #FDE3E6;
        color: #882C39;
        border: 1px solid #882C39;
      }
    }
    .btn_list_move{
    //   bottom: 380px;
      bottom: 379px;
    }
    .textarea{
        // padding:0 0 0 10px;
        height: 373px;
        // position: absolute;
        // bottom: 0px;
        width: 100%;
        font-size: 16px;
        background-color: rgb(242, 242, 242);
		    user-select: text;
        // background: #fff!important;
        .textarea_header{
          text-align: center;
          color: var(--base-color);
          position: relative;
          p{
            font-weight: bolder;
            font-size: 18px;
            letter-spacing: 2px;
            line-height: 40px;
          }
          >span{
            position: absolute;
            right: 10px;
            top: 8px;
            font-size: 24px;
            color: #ccc;
            &:hover{
              cursor: pointer;
              color: var(--base-color);
            }
          }
        }
        .textarea_content{
          height: 323px;
          overflow: auto;
          background-color: #fff;
          padding: 5px 5px 5px 15px;
          // font-family: serif;
          font-family: monospace;
          // overflow-x: hidden;
          font-size: 14px;
        }
    }
    .table_footer{
      display: flex;
      justify-content: space-between;
      width: 100%;
      position: absolute;
      align-items: center;
    //   bottom: 10px;
      bottom: 6px;
      padding: 0 20px;
      &:deep(){
        .ant-input-group-wrapper{
          width: 240px;
        }
        .ant-input{
          border: 1px solid #d9d9d9;
        }
      }
      // >span{
      //   font-size: 18px;
      //   width: 30px;
      //   height: 30px;
      //   border-radius: 5px;
      //   display: flex;
      //   justify-content: center;
      //   align-items: center;
      //   &:hover{
      //     background-color: rgb(232, 233, 233);
      //     cursor: pointer;
      //   }
      // }
      button{
        width: 100px;
        background-color: #FDE3E6;
        color: #882C39;
        border: 1px solid #882C39;
      }
    }
    // .table_footer_move{
    //   bottom: 380px;
      // bottom: 379px;
    // }
  }
</style>
