<template>
  <a-modal
    wrapClassName="modal_case_split"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    :maskClosable="false"
  >
    <screen-scale>
      <div class="modal_top">
        <p>{{ $t('算例拆分') }}</p>
        <close-outlined class="pointer" @click="closeModal" />
      </div>
      <div class="modal_content">
        <a-radio-group v-model:value="state.type" button-style="solid" >
          <a-radio-button value="bus">{{ $t('按节点拆分') }}</a-radio-button>
          <a-radio-button value="zone">{{ $t('按分区拆分') }}</a-radio-button>
        </a-radio-group>
        <div v-show="state.type == 'bus'">
          <div class="transfer-title">
            <div>
              <a-checkbox
                v-model:checked="state.checkAll"
                :indeterminate="state.indeterminate"
                @change="onCheckAllChange"
              >
                {{ $t('全选') }}
              </a-checkbox>
            </div>
            <div>{{ $t('已选择') }}</div>
          </div>
          <div class="transfer-box">
            <div>
              <div class="transfer-search">
                <a-input-search
                  v-model:value="state.searchSource"
                  :placeholder="$t('搜索')"
                  style="width: 100%"
                  @search="onSearchSource"
                />
              </div>
              <div class="transfer-content">
                <RecycleScroller
                  v-slot="{ item }"
                  ref="recycleScrollRef"
                  class="RecycleScrollerClass"
                  :item-size="34"
                  :items="state.sourceOptions"
                  key-field="key"
                >
                  <a-col :span="24" >
                    <a-checkbox v-model:checked="item.checked" @change="onCheckChange(item)">{{ item.label }}</a-checkbox>
                  </a-col>
                </RecycleScroller>

              </div>
            </div>
            <div>
              <div class="transfer-search">
                <a-input-search
                  v-model:value="state.searchTarget"
                  :placeholder="$t('搜索')"
                  style="width: 100%"
                  @search="onSearchTarget"
                />
              </div>
              <div class="transfer-content">
                <RecycleScroller
                  v-slot="{ item }"
                  ref="recycleScrollRef1"
                  class="RecycleScrollerClass"
                  :item-size="34"
                  :items="state.targetOptions"
                  key-field="key"
                >
                  <a-col :span="24">
                    <CloseSquareOutlined @click="removeItem(item)" /> {{ item.label }}
                  </a-col>
                </RecycleScroller>
              </div>
            </div>
          </div>
        </div>
        <div v-show="state.type == 'zone'">
          <div class="transfer-title">
            <div>
              <a-checkbox
                v-model:checked="state.checkAll1"
                :indeterminate="state.indeterminate1"
                @change="onCheckAllChange1"
              >
                {{ $t('全选') }}
              </a-checkbox>
            </div>
            <div>{{ $t('已选择') }}</div>
          </div>
          <div class="transfer-box">
            <div>
              <div class="transfer-search">
                <a-input-search
                  v-model:value="state.searchSource1"
                  :placeholder="$t('搜索')"
                  style="width: 100%"
                  @search="onSearchSource1"
                />
              </div>
              <div class="transfer-content">
                <RecycleScroller
                  v-slot="{ item }"
                  ref="recycleScrollRef"
                  class="RecycleScrollerClass"
                  :item-size="34"
                  :items="state.sourceOptions1"
                  key-field="key"
                >
                  <a-col :span="24" >
                    <a-checkbox v-model:checked="item.checked" @change="onCheckChange1(item)">{{ item.label }}</a-checkbox>
                  </a-col>
                </RecycleScroller>

              </div>
            </div>
            <div>
              <div class="transfer-search">
                <a-input-search
                  v-model:value="state.searchTarget1"
                  :placeholder="$t('搜索')"
                  style="width: 100%"
                  @search="onSearchTarget1"
                />
              </div>
              <div class="transfer-content">
                <RecycleScroller
                  v-slot="{ item }"
                  ref="recycleScrollRef1"
                  class="RecycleScrollerClass"
                  :item-size="34"
                  :items="state.targetOptions1"
                  key-field="key"
                >
                  <a-col :span="24">
                    <CloseSquareOutlined @click="removeItem1(item)" /> {{ item.label }}
                  </a-col>
                </RecycleScroller>
              </div>
            </div>
          </div>
        </div>

        <div class="modal_btns">
          <a-button @click="handleClear"
            type="primary"
            ghost
            size="small"
            :disabled="state.sourceValue.length <= 0"
          >
            {{ $t('清空') }}
          </a-button>
          <a-button @click="closeModal" size="small">{{ $t('取消') }}</a-button>
          <a-button @click="handleOk" type="primary" :style="{color:'#fff'}" size="small">{{ $t('确认') }}</a-button>
        </div>
      </div>
    </screen-scale>
  </a-modal>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { reactive, defineEmits, onMounted } from 'vue'

import { useRoute } from 'vue-router'
import message from '@/utils/message'
import { getReadNameCol, splitCaseApi } from '@/api/exampleApi'
import { t } from '@/utils/common'

const emits = defineEmits(['cancel', 'confirm'])
const route = useRoute()

const state = reactive({
	visible: true,
	type: 'bus',
	isMultiple: 'multiple',
	allData: [],
	checkAll: false,
	indeterminate: false,
	searchSource: '',
	searchTarget: '',
	sourceValue: [],
	sourceOptions: [],
	targetOptions: [],
	allData1: [],
	checkAll1: false,
	indeterminate1: false,
	searchSource1: '',
	searchTarget1: '',
	sourceValue1: [],
	sourceOptions1: [],
	targetOptions1: []
})
const onSearchSource = searchValue => {
	if (state.sourceValue.length == 0) {
		state.checkAll = false
		state.indeterminate = false
	} else if (state.sourceValue.length < state.allData.length) {
		state.checkAll = false
		state.indeterminate = true
	} else {
		state.checkAll = true
		state.indeterminate = false
	}

	state.sourceOptions = state.allData
	state.sourceOptions = state.sourceOptions.filter(item => item.label.toLowerCase().indexOf(searchValue.toLowerCase()) >= 0)
}

const onSearchTarget = searchValue => {
	state.targetOptions = state.allData.filter(item => state.sourceValue.includes(item.key)).filter(item => item.label.toLowerCase().indexOf(searchValue.toLowerCase()) >= 0)
}

const onCheckAllChange = e => {
	state.sourceOptions.forEach(item => {
		item.checked = e.target.checked
	})
	state.allData.forEach(item => {
		if (state.sourceOptions.find(item1 => item1.key == item.key)) {
			item.checked = e.target.checked
		}
	})
	state.sourceValue = state.allData.filter(item => item.checked).map(item => item.key)
	state.targetOptions = state.allData.filter(item => item.checked && item.label.toLowerCase().indexOf(state.searchTarget.toLowerCase()) >= 0)

	state.indeterminate = false
}

const onCheckChange = (val) => {
	state.sourceOptions.forEach(item => {
		if (item.key == val.key) {
			item.checked = val.checked
		}
	})
	state.allData.forEach(item => {
		if (item.key == val.key) {
			item.checked = val.checked
		}
	})
	state.sourceValue = state.allData.filter(item => item.checked).map(item => item.key)
	state.targetOptions = state.allData.filter(item => item.checked && item.label.toLowerCase().indexOf(state.searchTarget.toLowerCase()) >= 0)

	changeIndeterminate()
}

const removeItem = (val) => {
	state.sourceOptions.forEach(item => {
		if (item.key == val.key) {
			item.checked = false
		}
	})
	state.allData.forEach(item => {
		if (item.key == val.key) {
			item.checked = false
		}
	})

	state.sourceValue = state.allData.filter(item => item.checked).map(item => item.key)
	state.targetOptions = state.allData.filter(item => item.checked && item.label.toLowerCase().indexOf(state.searchTarget.toLowerCase()) >= 0)

	changeIndeterminate()
}

const changeIndeterminate = () => {
	if (state.targetOptions.length == 0) {
		state.checkAll = false
		state.indeterminate = false
	} else if (state.targetOptions.length < state.allData.length) {
		state.checkAll = false
		state.indeterminate = true
	} else {
		state.checkAll = true
		state.indeterminate = false
	}
}

const onSearchSource1 = searchValue => {
	if (state.sourceValue1.length == 0) {
		state.checkAll1 = false
		state.indeterminate1 = false
	} else if (state.sourceValue1.length < state.allData1.length) {
		state.checkAll1 = false
		state.indeterminate1 = true
	} else {
		state.checkAll1 = true
		state.indeterminate1 = false
	}

	state.sourceOptions1 = state.allData1
	state.sourceOptions1 = state.sourceOptions1.filter(item => item.label.toLowerCase().indexOf(searchValue.toLowerCase()) >= 0)
}

const onSearchTarget1 = searchValue => {
	state.targetOptions1 = state.allData1.filter(item => state.sourceValue1.includes(item.key)).filter(item => item.label.toLowerCase().indexOf(searchValue.toLowerCase()) >= 0)
}

const onCheckAllChange1 = e => {
	state.sourceOptions1.forEach(item => {
		item.checked = e.target.checked
	})
	state.allData1.forEach(item => {
		if (state.sourceOptions1.find(item1 => item1.key == item.key)) {
			item.checked = e.target.checked
		}
	})
	state.sourceValue1 = state.allData1.filter(item => item.checked).map(item => item.key)
	state.targetOptions1 = state.allData1.filter(item => item.checked && item.label.toLowerCase().indexOf(state.searchTarget1.toLowerCase()) >= 0)

	state.indeterminate1 = false
}

const onCheckChange1 = (val) => {
	state.sourceOptions1.forEach(item => {
		if (item.key == val.key) {
			item.checked = val.checked
		}
	})
	state.allData1.forEach(item => {
		if (item.key == val.key) {
			item.checked = val.checked
		}
	})
	state.sourceValue1 = state.allData1.filter(item => item.checked).map(item => item.key)
	state.targetOptions1 = state.allData1.filter(item => item.checked && item.label.toLowerCase().indexOf(state.searchTarget1.toLowerCase()) >= 0)

	changeIndeterminate1()
}

const removeItem1 = (val) => {
	state.sourceOptions1.forEach(item => {
		if (item.key == val.key) {
			item.checked = false
		}
	})
	state.allData1.forEach(item => {
		if (item.key == val.key) {
			item.checked = false
		}
	})

	state.sourceValue1 = state.allData1.filter(item => item.checked).map(item => item.key)
	state.targetOptions1 = state.allData1.filter(item => item.checked && item.label.toLowerCase().indexOf(state.searchTarget1.toLowerCase()) >= 0)

	changeIndeterminate1()
}

const changeIndeterminate1 = () => {
	if (state.targetOptions1.length == 0) {
		state.checkAll1 = false
		state.indeterminate1 = false
	} else if (state.targetOptions1.length < state.allData1.length) {
		state.checkAll1 = false
		state.indeterminate1 = true
	} else {
		state.checkAll1 = true
		state.indeterminate1 = false
	}
}

const handleOk = () => {
	splitCaseApi({
		file_path: route.query.filePath,
		split_by_ele: state.type == 'bus' ? 'bus' : 'zone',
		row_id_list: state.type == 'bus' ? state.targetOptions.map(item => item.key) : state.targetOptions1.map(item => item.key)
	}, true).then(res => {
		if (res.code == 1) {
			emits('confirm', {
				file_name: res.file_name,
				file_path: res.file_path
			})
			message.success(res.msg || t('算例拆分成功') + '！')
		} else {
			message.error(res.msg || t('算例拆分失败') + '！')
		}
	})
}

const closeModal = () => {
	emits('cancel')
}

const handleClear = () => {
	if (state.type == 'bus') {
		state.sourceOptions.forEach(item => {
			item.checked = false
		})
		state.allData.forEach(item => {
			item.checked = false
		})
		state.sourceValue = []
		state.targetOptions = []
		state.checkAll = false
		state.indeterminate = false
	} else {
		state.sourceOptions1.forEach(item => {
			item.checked = false
		})
		state.allData1.forEach(item => {
			item.checked = false
		})
		state.sourceValue1 = []
		state.targetOptions1 = []
		state.checkAll1 = false
		state.indeterminate1 = false
	}
}

const handleRefresh = () => {
	getReadNameColData()
}
Mitt.on('handleRefresh', handleRefresh)

const getReadNameColData = () => {
	getReadNameCol({
		'import_string_func': 'teapcase:read_name_col_from_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,

			'sheet_names': ['bus', 'zone']
		}
	}).then(res => {
		if (res.code == 1) {
			console.log(1234, res)

			const { bus, zone } = res.func_result.data
			state.sourceOptions = state.allData = bus.map(item => {
				return {
					key: item.index,
					label: item.name,
					checked: false
				}
			})
			state.sourceOptions1 = state.allData1 = zone.map(item => {
				return {
					key: item.index,
					label: item.name,
					checked: false
				}
			})
		}
	})
}
onMounted(() => {
	state.treeType = sessionStorage.getItem('treeType').split('.')[0]
	getReadNameColData()
})
</script>
<style lang="scss">
.modal_case_split{
  .ant-modal{
    width: auto!important;
    .ant-modal-body{
      >div{
        .RecycleScrollerClass {
          height: 345px;
          overflow-y: auto;
        }
        .modal_content{
          width: 680px;
          padding: 17px 25px;
          text-align: center;
        }
        .transfer-title {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 30px;
          >div{
            background: #fff;
            text-align: left;
            padding: 0 10px;
          }
          >div:first-child{
            display: flex;
            justify-content: space-between;
          }
        }

        .transfer-box {
          // width: 600px;
          display: grid;
          grid-template-columns: 1fr 1fr;
          // justify-content: space-between;
          >div{
            // width: 258px;
            height: 400px;
            padding: 5px 10px;
            box-sizing: border-box;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            .transfer-search {
              // padding: 0 10px
              height: 40px;
            }
            .transfer-content{
              height: 350px;
              background: #fff;
              text-align: left;
              white-space: nowrap;
              // overflow: auto;
            }

          }
          >div:first-child {
              margin-right: 15px;
            }
        }

        .modal_btns{
          margin-top: 17px;
          text-align: center;
          button{
            width: 90px;
            height: 30px;
            letter-spacing: 0;
            margin-left: 15px;
          }
        }

      }
    }
  }
}
</style>
