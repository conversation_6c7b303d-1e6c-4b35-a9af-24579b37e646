<template>
	<a-spin size="large" :spinning="state.loading">
		<div class="map-container">
			<div class="gis">
				<div ref="gis" @click="clickMap" @contextmenu="contextmenu" @mousedown="mousedown" @mouseup="mouseup" @mouseleave="mouseleave" @wheel="wheel" :style="{transform:`scale(${state.scales})`,zoom:`${state.zooms}`}">

				</div>
			</div>
			<div class="tool_box">
				<div>
					<p>{{ $t('地图') }}</p>
					<MinusOutlined :class="gisState.scale==20?'disabled':''" @click="changeScaleValue(-10)" />
					<a-slider :tooltip-open="false" v-model:value="gisState.scale" :min="20" :max="200" :step="10" @change="changeScale" />
					<PlusOutlined :class="gisState.scale==200?'disabled':''" @click="changeScaleValue(10)" />
				</div>
				<div>
					<p>{{ $t('元件') }}</p>
					<MinusOutlined :class="gisState.iconZoom==0.2?'disabled':''" @click="changeIconScaleValue(-0.1)" />
					<a-slider :tooltip-open="false" v-model:value="gisState.iconZoom" :min="0.2" :max="gisState.iconZoomMax" :step="0.1" @change="changeIconScale" />
					<PlusOutlined :class="gisState.iconZoom==gisState.iconZoomMax?'disabled':''" @click="changeIconScaleValue(0.1)" />
				</div>

			</div>
			<a-button class="tool_boox_btn" @click="openTg">{{ $t('更换tg') }}</a-button>
			<div class="switch_box" v-if="route.query.type=='ac_power_flow'">
				{{ $t('显示无功') }}： <a-switch v-model:checked="state.showMore" :checked-children="$t('是')" :un-checked-children="('否')" @change="initMap('changeShow')" />
			</div>
			<div class="switch_box" v-else-if="route.query.type=='short_circuit'">
				<a-radio-group v-model:value="state.showMore" button-style="solid" @change="initMap('changeShow')">
					<a-radio-button :value="false">{{ $t('单相短路') }}</a-radio-button>
					<a-radio-button :value="true">{{ $t('三相短路') }}</a-radio-button>
				</a-radio-group>
			</div>
		</div>
	</a-spin>
	<gis-message v-if="state.messageShow" @close="state.messageShow=false" :data="state.warningMessage"></gis-message>
</template>
<script setup>
import { markRaw, reactive, ref, onUnmounted, inject, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { getMapServies, echartsResize, initLineData, getGisStyle, getTangentialPoint, getGeoJson, getMapScaleByBbox, pointOnLine, debounce } from '@/utils/gis'
import { basicApi } from '@/api/exampleApi'
import { GetLineJobTaskResult } from '@/api/gis'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const emit = defineEmits(['cancel', 'openTg', 'startReady', 'loading'])
const storeSetting = settingStore()
const { isChromeHigh } = storeToRefs(storeSetting)
const echarts = inject('ec')
const route = useRoute()
const gis = ref()
const props = defineProps({
	tgFilePath: {
		type: String
	},
	isFirstOpen: {
		type: Boolean
	}
})
const state = reactive({
	loading: true,
	messageShow: false,
	scales: 1,
	zooms: 1,
	isSelected: false,
	showMore: false,
	markLineData: [],
	warningMessage: [],
	position: {},
	zlevelId: undefined
})
const gisState = reactive({
	partition: '全省',
	scale: 100,
	originCenter: [],
	mapZoom: 5,
	iconZoom: 1,
	iconZoomMax: 3,
	geoScale: undefined,
	center: undefined,
	isBlank: false,
	isMoving: false
})
const mapData = ref({
	point_data: [],
	line_data: []
})
const mapChart = ref()
const mapOption = ref({})
const resultColor = ['#ff6464', '#ffaa64', '#fff5a5', '#55e9bc', '#11cbd7']
const initMap = (val) => {
	mapOption.value = getMapServies({
		gisState,
		mapData: mapData.value,
		resultType: route.query.type,
		markLineData: state.markLineData,
		showMore: state.showMore,
		resultColor: route.query.type == 'ac_power_flow' ? resultColor : route.query.type == 'short_circuit' ? ['#DC143C', '#ff6464', '#ffaa64', '#fff5a5', '#fff'] : ['#DC143C', '#ff6464', '#ffaa64', '#fff5a5', '#000']
	})
	if (gisState.center) {
		mapOption.value.geo.center = gisState.center
		mapOption.value.series[0].center = gisState.center
	}
	if (gisState.scale != 100) {
		mapOption.value.geo.zoom = gisState.mapZoom * gisState.scale / 100
		mapOption.value.series[0].zoom = gisState.mapZoom * gisState.scale / 100
		mapOption.value.series[1].data.forEach(item => {
			item.lineStyle.width = item.line_data ? (getGisStyle({ vn_kv: item.min_vn_kv }, gisState.iconZoom).width * echartsResize(5)) * gisState.scale / 100 : getGisStyle(item, gisState.iconZoom).width * gisState.scale / 100
		})
	}
	if (val == 'changeIconScale') {
		mapOption.value.series[1].data.forEach(item1 => {
			item1.lineStyle.width = item1.line_data ? (getGisStyle({ vn_kv: item1.min_vn_kv }, gisState.iconZoom).width * echartsResize(5)) * gisState.scale / 100 : getGisStyle(item1, gisState.iconZoom).width * gisState.scale / 100
			const symbolR = getGisStyle({ vn_kv: item1.min_vn_kv }, gisState.iconZoom).width * echartsResize(5)
			if (['boundary1', 'boundary2'].includes(item1.line_type)) {
				const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], (symbolR - getGisStyle({ vn_kv: item1.min_vn_kv }, gisState.iconZoom).width / 2) * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], (symbolR - getGisStyle({ vn_kv: item1.min_vn_kv }, gisState.iconZoom).width / 2) * gisState.geoScale)
				item1.coords = item1.line_type == 'boundary1' ? coords[0] : coords[1]
			} else if (['middle', 'bolder'].includes(item1.line_type)) {
				item1.coords = item1.coord
			} else if (['four1', 'four2'].includes(item1.line_type)) {
				const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], symbolR / 3 * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], symbolR / 3 * gisState.geoScale)
				item1.coords = item1.line_type == 'four1' ? coords[0] : coords[1]
			} else if (['five1', 'five2'].includes(item1.line_type)) {
				const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], symbolR / 2 * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], symbolR / 2 * gisState.geoScale)
				item1.coords = item1.line_type == 'five1' ? coords[0] : coords[1]
			} else if (['six1', 'six4'].includes(item1.line_type)) {
				const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], symbolR / 5 * 3 * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], symbolR / 5 * 3 * gisState.geoScale)
				item1.coords = item1.line_type == 'six1' ? coords[0] : coords[1]
			} else if (['six2', 'six3'].includes(item1.line_type)) {
				const coords = getTangentialPoint(item1.coord[0][0], item1.coord[0][1], symbolR / 5 * gisState.geoScale, item1.coord[1][0], item1.coord[1][1], symbolR / 5 * gisState.geoScale)
				item1.coords = item1.line_type == 'six2' ? coords[0] : coords[1]
			}
			if (route.query.type == 'ac_power_flow') {
				const find = mapOption.value.series[3].data.find(item => item.name == item1.name)
				if (find) find.value = pointOnLine(item1.coords[0], item1.coords[1], 0.5).concat(item1.value)
			}
		})
	}
	mapChart.value.setOption(mapOption.value)
	mapChart.value.on('mousedown', function(params) {
		state.isMoving = false
	})
	mapChart.value.off('click')
	mapChart.value.on('click', async(params) => {
		params.event.stop()
		state.isMoving = true
	})
	mapChart.value.off('georoam')
	mapChart.value.on('georoam', function(params) {
		state.isMoving = true
		const _option = mapChart.value.getOption()
		gisState.center = _option.geo[0].center
		mapOption.value.geo.center = _option.series[0].center
		mapOption.value.series[0].center = _option.series[0].center
		gisState.originCenter = _option.series[0].center
		mapChart.value.setOption(mapOption.value)
	})
	mapChart.value.off('contextmenu')
	mapChart.value.on('contextmenu', (params) => {
		params.event.stop()
		if (params.componentSubType == 'scatter' && params.data.expandable) {
			initNextMap(params.data)
		}
	})
	mapChart.value.on('datarangeselected', (params) => {

	})
}
const initNextMap = (data) => {
	state.loading = true
	if (state.zlevelId == data.station_id) {
		state.zlevelId = undefined
	} else {
		state.zlevelId = data.station_id
	}
	initData()
}
const openTg = () => {
	emit('openTg')
}
const contextmenu = (event) => {
	event.preventDefault()
}
const wheel = (event) => {
	event.preventDefault()
	let scale
	if (event.deltaY < 0) {
		scale = gisState.scale + 10
		if (scale >= 200) {
			scale = 200
		}
	} else if (event.deltaY > 0) {
		scale = gisState.scale - 10
		if (scale <= 20) {
			scale = 20
		}
	}
	changeScale(scale)
}
const mousemove = (e) => {
	if (!state.isMoving) return
	const { clientX: x, clientY: y } = e
	const center = [
		gisState.originCenter[0] + (state.position.x - x) * gisState.geoScale / (gisState.scale / 100),
		gisState.originCenter[1] - (state.position.y - y) * gisState.geoScale / (gisState.scale / 100)
	]
	mapOption.value.series[0].center = center
	mapOption.value.geo.center = center
	mapChart.value.setOption(mapOption.value)
}
const mouseup = () => {
	if (!state.isMoving) {
		return state.isMoving = true
	}
	gisState.originCenter = mapOption.value.series[0].center
	mapChart.value.getDom().removeEventListener('mousemove', mousemove)
}
const mouseleave = () => {
	if (!state.isMoving) return
	gisState.originCenter = mapOption.value.series[0].center
	mapChart.value.getDom().removeEventListener('mousemove', mousemove)
}
const mousedown = (event) => {
	if (!state.isMoving) return
	event.preventDefault()
	const { clientX: x, clientY: y } = event
	state.position = {
		x,
		y
	}
	mapChart.value.getDom().addEventListener('mousemove', mousemove)
}
const changeScaleValue = (val) => {
	const scale = gisState.scale + val
	if (scale > 200) {
		gisState.scale = 200
		return
	} else if (scale < 20) {
		gisState.scale = 20
		return
	} else {
		gisState.scale = scale
		changeScale(scale)
	}
}
const changeIconScaleValue = (val) => {
	const iconZoom = gisState.iconZoom + val
	if (iconZoom > 2) {
		gisState.iconZoom = 2
		return
	} else if (iconZoom < 0.1) {
		gisState.iconZoom = 0.1
		return
	} else {
		gisState.iconZoom = iconZoom
		changeIconScale(iconZoom)
	}
}
const changeScale = (val) => {
	gisState.scale = val
	mapOption.value.geo.zoom = gisState.mapZoom * gisState.scale / 100
	mapOption.value.series[0].zoom = gisState.mapZoom * gisState.scale / 100
	mapOption.value.series[1].data.forEach(item => {
		item.lineStyle.width = item.line_data ? (getGisStyle({ vn_kv: item.min_vn_kv }, gisState.iconZoom).width * echartsResize(5)) * gisState.scale / 100 : getGisStyle(item, gisState.iconZoom).width * gisState.scale / 100
	})
	mapChart.value.setOption(mapOption.value)
}
const changeIconScale = (val) => {
	gisState.iconZoom = val
	initMap('changeIconScale')
}
const screenScale = () => {
	let root
	if (isChromeHigh.value) {
		root = document.getElementsByClassName('home-body')[0].style.zoom || 1
	} else {
		root = document.body.style.zoom
	}
	state.zooms = 1 / root
	state.scales = root
}
const debouncedScreenScale = debounce(screenScale, 200)
onMounted(async() => {
	screenScale()
	window.addEventListener('resize', debouncedScreenScale)
	mapChart.value = markRaw(echarts.init(gis.value))
	initData()
})
const handleRusultPointColor = (item, type) => {
	if (type == 'ac_power_flow') {
		if (item.res_trafo_max_loading_percent > 100) {
			return resultColor[0]
		} else if (item.res_trafo_max_loading_percent > 80) {
			return resultColor[1]
		} else if (item.res_trafo_max_loading_percent > 50) {
			return resultColor[2]
		} else if (item.res_trafo_max_loading_percent > 30) {
			return resultColor[3]
		} else {
			return resultColor[4]
		}
	} else {
		return getGisStyle({ vn_kv: item.vn_kv }).color
	}
}
const initGis = async(jsonData, geoData) => {
	if (geoData.warning_msg && geoData.warning_msg.length > 0 && props.isFirstOpen) {
		state.warningMessage = geoData.warning_msg
		state.messageShow = true
	}
	emit('startReady')
	echarts.registerMap(gisState.partition, jsonData)
	gisState.geoScale = (0.75 / getMapScaleByBbox(jsonData, gis.value.clientWidth, gis.value.clientHeight).scale / 5)
	gisState.originCenter = getMapScaleByBbox(jsonData, gis.value.clientWidth, gis.value.clientHeight).center

	mapData.value.point_data = geoData.station_data.map(item => {
		return Object.assign({ ...item }, {
			value: [item.lon, item.lat, 0],
			itemStyle: {
				borderColor: handleRusultPointColor(item, route.query.type)
			}
		})
	})
	mapData.value.line_data = geoData.channel_data.flatMap(item => initLineData(
		{ 'type': 'result', 'channel': item, 'apiZoom': gisState.iconZoom, 'geoScale': gisState.geoScale, 'resultType': route.query.type }
	))
	if (route.query.type == 'ac_power_flow') {
		state.markLineData = geoData.channel_data.filter(items => items.switch_data.length == 0).map(item => {
			return {
				value: Math.abs(item.line_data.reduce((acc, cur) => {
					if (cur.isSequence) {
						return acc + cur.p_from_mw
					} else {
						return acc - cur.p_from_mw
					}
				}, 0)),
				values: Math.abs(item.line_data.reduce((acc, cur) => {
					if (cur.isSequence) {
						return acc + cur.q_from_mvar
					} else {
						return acc - cur.q_from_mvar
					}
				}, 0)),
				lineNum: item.line_data.length,
				coords: item.coords
			}
		})
	} else if (route.query.type == 'n_1' || route.query.type == 'n_2') {
		// console.log(route.query.type)
	} else if (route.query.type == 'short_circuit') {
		// console.log(route.query.type)
	}
	initMap()
	state.loading = false
	emit('cancel')
}
const initData = () => {
	GetLineJobTaskResult({

		result_file_path: route.query.filePath,
		tg_file_path: props.tgFilePath,
		group: 'gis',
		parent_station_id: state.zlevelId
	}).then(res => {
		if (res.code == 1) {
			if (res.data.geojson_name) {
				gisState.partition = res.data.geojson_name
				basicApi({
					'import_string_func': 'teapgis:get_geojson',
					'func_arg_dict': {
						'tg_file_path': props.tgFilePath,
						'geojson_name': res.data.geojson_name
					}
				}).then(res1 => {
					if (res1.code == 1 && res1.func_result.code == 1) {
						initGis(res1.func_result.data, res.data)
					}
				})
			} else {
				gisState.partition = route.query.name.split('.')[0] + Date.now()
				gisState.isBlank = true
				const jsonData = getGeoJson(res.data)
				initGis(jsonData, res.data)
			}
		}
	}).catch(() => {
		state.loading = false
	})
}
onUnmounted(() => {
	window.removeEventListener('resize', debouncedScreenScale)
})
</script>
<style lang="scss" scoped>
    .map-container{
        height: 100%;
        width: 100%;
        position: relative;
		background-color: #fff;
        .gis,.gis>div:first-child{
            transform-origin: 0 0;
            height: 100%;
            width: 100%;
        }
    }
	.tool_box{
		position: absolute;
		top: 0;
		left: 0;
		background: #F6F8FA;
		border: 1px solid #9B9EA8;
		border-radius: 0px 6px 6px 6px;
		padding: 5px;
		>div{
			display: flex;
			justify-content: space-around;
			align-items: center;
			>p{
				padding: 0 10px;
			}
			>span{
				font-size: 16px;
				padding: 2px;
				&:hover{
					cursor: pointer;
					background: rgb(219, 233, 245);
				}
			}
		}
		.ant-slider{
			width: 150px!important;
		}
	}
	.tool_boox_btn{
		display: flex;
		align-items: center;
		justify-content: center;
		position: absolute;
		top: 5px;
		left: 270px;
		background-color: #007aff;
		color: #fff;
		padding: 5px 20px;
		width: 120px;
		font-size: 14px;
		border-radius: 5px;
		&:hover{
			cursor: pointer;
		}
	}
	.switch_box{
		position: absolute;
		bottom:2px;
		left: 5px;
		display: flex;
		align-items: center;
	}
</style>
