<template>
    <a-modal wrapClassName="AddModal" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <a-spin :spinning="state.loading">
            <div>
                <div class="modal_top">
                    <p>{{getTitle()}}</p>
                    <close-outlined class="pointer" @click="emit('close')" />
                </div>
                <div class="modal-content">
                    <div class="form_content">
                        <a-form
                            ref="formRef"
                            :model="formState"
                        >
                            <a-form-item
                                :label="$t('设备类型')"
                                name="table_type"
                                >
                                <a-select :disabled="!props.isAdd" v-model:value="formState.table_type" :options="state.options" @change="changeType"></a-select>
                            </a-form-item>
                            <div class="grid">
                                <a-form-item
                                    v-for="(item) in state.formData"
                                    :key="item.field"
                                    :rules="[{ required:true, message: $t('请输入')+item.headerName.replace(/\(.*?\)/g,'').replace(/（(.+?)）/,'') }]"
                                    :label="item.headerName.replace(/\(.*?\)/g,'').replace(/（(.+?)）/,'')"
                                    :name="item.field"
                                >
                                    <a-input-number v-if="item.cellDataType=='number'&&!item.col_source" :min="0" :controls="false"  v-model:value="formState[item.field]" >
                                        <template #addonAfter v-if="!(item.headerName.includes($t('率'))&&!item.headerName.includes($t('功率'))||!getUnit(item.headerName))">
                                            <span>{{ getUnit(item.headerName) }}</span>
                                        </template>
                                    </a-input-number>
                                    <a-radio-group v-else-if="item.cellDataType=='boolean'" v-model:value="formState[item.field]" name="radioGroup">
                                        <a-radio :value="true">{{ $t('是') }}</a-radio>
                                        <a-radio :value="false">{{$t('否')}}</a-radio>
                                    </a-radio-group>
                                    <a-select v-else-if="item.field=='bus'" v-model:value="formState[item.field]" :options="state.busOption">

                                    </a-select>
                                    <div v-else-if="item.col_source">
                                        <a-select  v-model:value="formState[item.field]" :options="state.relationOption[item.col_source]||[]">

                                        </a-select>
                                        <a-button type="primary" class="add_btn" shape="circle" :icon="h(PlusOutlined)" @click="addRelation(item.col_source,item.headerName.replace(/\(.*?\)/g,'').replace(/（(.+?)）/,''))" />
                                    </div>
                                    <a-input disabled v-else-if="item.field=='type'" v-model:value="state.typeName"/>
                                    <a-input v-else-if="item.cellDataType=='text'" v-model:value="formState[item.field]"/>
                                </a-form-item>
                            </div>
                            <div>
                                <p class="text_remark">{{ $t('注：其它数据请前往编辑器或生成器完善') }}
                                    <!-- <span v-if="props.isAdd&&formState.table_type" @click="replenish">(参数补充)</span> -->
                                </p>
                            </div>
                        </a-form>
                    </div>
                    <div class="modal_btn">
                        <a-button :disabled="!formState.table_type" @click="confirm" type="primary">{{$t('确认')}}</a-button>
                        <a-button @click="emit('close')">{{$t('取消')}}</a-button>
                    </div>
                </div>
            </div>
            </a-spin>
            <add-simple-modal :types="formState['type']" :typeName="state.typeName" @confirm="confirmAddRelation" v-if="state.addModalShow" @close="state.addModalShow=false" :type="state.addModalType" :title="state.addModalTitle" :columns="state.addModalColumn"></add-simple-modal>
        </screen-scale>
    </a-modal>
</template>
<script setup>
import { onMounted, reactive, ref, computed, h } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { useRoute } from 'vue-router'
import { basicApi } from '@/api/exampleApi'
import { getUnit } from '@/utils/gis'
import { t } from '@/utils/common'
const props = defineProps({
	isAdd: {
		type: Boolean
	},
	data: {
		type: Object
	},
	type: {
		type: String
	},
	options: {
		type: Array
	}
})
const route = useRoute()
const state = reactive({
	ifShow: true,
	loading: true,
	formData: [],
	options: [],
	busOption: props.options,
	relationOption: {},
	relationColumn: {},
	typeName: undefined,
	addModalShow: undefined,
	addModalType: undefined,
	addModalTitle: undefined,
	addModalColumn: undefined
})
const formRef = ref()
const formState = reactive({

})
const emit = defineEmits(['close', 'confirm'])
const getTitle = computed(() => () => {
	if (props.isAdd) {
		return t('新建设备')
	} else {
		return t('编辑设备')
	}
})
/* eslint-disable no-unused-vars */
const replenish = () => {
	state.loading = true
	basicApi({
		'import_string_func': 'teapcase:get_init_col_data',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'col_list': [],
			'replace': false,
			'sheet_name': formState.table_type,
			'row_id_list': [formState.index]
		}
	}).then(res => {
		state.loading = false
		Object.keys(res.func_result[formState.table_type].data[0]).forEach(key => {
			formState[key] = res.func_result[formState.table_type].data[0][key]
		})
	}).catch(() => {
		state.loading = false
	})
}
const confirmAddRelation = (item) => {
	state.addModalShow = false
	state.relationOption[state.addModalType].push(item)
}
const addRelation = (type, title) => {
	state.addModalType = type
	state.addModalTitle = title
	state.addModalColumn = state.relationColumn[type].filter(item => item.required)
	state.addModalShow = true
}
const changeType = async() => {
	state.loading = true
	state.formData = state.options.find(item => item.value == formState.table_type).columns.filter(item => item.required && item.field != 'timeseries')
	state.typeName = state.options.find(item => item.value == formState.table_type).label
	state.formData.forEach(item => {
		formState[item.field] = item.cellDataType == 'boolean' ? true : undefined
	})
	if (formState.table_type.includes('.')) {
		formState['type'] = formState.table_type.split('.')[1]
	}
	if (state.formData.find(item => item.col_source && item.field != 'bus')) {
		const res = await basicApi({
			'import_string_func': 'teapgis:get_relation_sheet_index',
			'func_arg_dict': {
				'file_path': route.query.filePath,
				'ele_name': formState.table_type.includes('.') ? formState.table_type.split('.')[0] : formState.table_type
			}
		})
		state.relationOption = res.func_result.relation_sheet_index
		state.relationColumn = res.func_result.relation_sheet_column
	}
	state.loading = false
}
const confirm = () => {
	formRef.value.validate()
		.then(() => {
			emit('confirm', formState, state.options.find(item => item.value == formState.table_type))
		})
		.catch(error => {
			console.log('error', error)
		})
}
const closeModal = () => {
	emit('close')
}
onMounted(async() => {
	const res1 = await basicApi({
		'import_string_func': 'teapgis:get_station_relation_ele_columns',
		'func_arg_dict': {
			'station_type': props.type
		}
	})
	state.options = res1.func_result.data
	if (props.data.table_type) {
		state.formData = state.options.find(item => item.value == props.data.table_type).columns.filter(item => item.required && item.field != 'timeseries')
		state.typeName = state.options.find(item => item.value == props.data.table_type).label
		if (state.formData.find(item => item.col_source && item.field != 'bus')) {
			const res = await basicApi({
				'import_string_func': 'teapgis:get_relation_sheet_index',
				'func_arg_dict': {
					'file_path': route.query.filePath,
					'ele_name': props.data.table_type.includes('.') ? props.data.table_type.split('.')[0] : props.data.table_type
				}
			})
			state.relationOption = res.func_result.relation_sheet_index
			state.relationColumn = res.func_result.relation_sheet_column
		}
	}
	Object.keys(props.data).forEach(key => {
		formState[key] = props.data[key]
	})
	state.loading = false
})
</script>
<style lang="scss">
    .AddModal {
        .ant-modal{
            width: auto!important;
            .modal-content{
                min-width: 800px;
                padding: 0 0 70px;
                .form_content{
                    padding: 30px 80px 0;
                    .grid{
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        grid-column-gap: 40px;
                    }
                    .ant-input{
                        width: 200px;
                    }
                    .ant-input-number-wrapper{
                        width: 200px;
                        .ant-input-number-group-addon{
                            width: 40px;
                        }
                    }
                    .ant-form-item-control-input-content>.ant-input-number{
                        width: 200px;
                    }
                    .ant-select{
                        width: 200px;
                    }
                    .add_btn{
                        position: absolute;
                    }
                    .ant-form-item-label{
                        // text-align: left;
                        width: 200px;
                        label{
                            font-size: 18px;
                        }
                    }
                    .text_remark{
                        color: rgb(163, 0, 20);
                        font-weight: bolder;
                        font-size: 18px;
                        span{
                            color: blue;
                            &:hover{
                                cursor: pointer;
                            }
                        }
                    }
                }
            }
        }
    }
</style>
