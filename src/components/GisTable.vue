<template>
    <div class="gis_table">
        <div class="table_small">
            <div class="table_select">
                <a-radio-group v-model:value="state.type"  v-if="route.query.type=='ac_power_flow'">
					<a-radio-button v-for="(item) in sheetConfig1" :key="item.value" :value="item.value">{{ item.sheetName }}</a-radio-button>
                </a-radio-group>
				<a-radio-group v-model:value="state.type" v-if="route.query.type=='short_circuit'">
                    <a-radio-button v-for="(item) in sheetConfig2" :key="item.value" :value="item.value">{{ item.sheetName }}</a-radio-button>
                </a-radio-group>
				<a-button type="primary" @click="exportTable(0)">{{ $t('导出') }}</a-button>
            </div>
            <a-table
                :columns="getColumn()"
                :data-source="getTableData()"
                :loading="state.loading"
				:scroll="{y:428}"
				:rowClassName="rowClassName"
            >
			<template
                #customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }"
                >
                <div style="padding: 8px">
					<a-input
						ref="searchInput"
						:placeholder="$t('搜索名称')"
						:value="selectedKeys[0]"
						style="width: 188px; margin-bottom: 8px; display: block"
						@change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
						@pressEnter="handleSearch(selectedKeys, confirm, column.dataIndex)"
					/>
					<a-button
						type="primary"
						size="small"
						style="width: 90px; margin-right: 8px"
						@click="handleSearch(selectedKeys, confirm, column.dataIndex)"
					>
					<template v-if="column.dataIndex=='name'" #icon><SearchOutlined /></template>
						{{ $t('搜索') }}
					</a-button>
					<a-button size="small" style="width: 90px" @click="handleReset(clearFilters)">
						{{ $t('重置') }}
					</a-button>
                </div>
            </template>
			<template #customFilterIcon="{ filtered,column }">
                <search-outlined v-if="column.dataIndex=='name'" :style="{ color: filtered ? '#108ee9' : undefined }" />
                <FilterFilled v-else :style="{ color: filtered ? '#108ee9' : undefined }" />
            </template>
			<template #bodyCell="{ column,text,record }">
				<span v-if="state.searchText && state.searchedColumn === column.dataIndex">
                  <template
                    v-for="(fragment, i) in text
                    .toString()
                    .split(new RegExp(`(?<=${state.searchText})|(?=${state.searchText})`, 'i'))"
                  >
                    <mark
                      v-if="fragment.toLowerCase() === state.searchText.toLowerCase()"
                      :key="i"
                      class="highlight"
                    >
                      {{ fragment }}
                    </mark>
                    <template v-else>{{ fragment }}</template>
                  </template>
                </span>
				<template v-else-if="['name'].includes(column.dataIndex)">
					{{ text }}
				</template>
				<template v-else-if="['type','zone'].includes(column.dataIndex)">
					{{ column.dataIndex=='type'? record.type== 'line' ? $t('线路') : $t('主变')  :text }}
				</template>
				<template v-else-if="['detail_data'].includes(column.dataIndex)">
					<a-button type="primary" @click="openTable(record)">{{ $t('详情') }}</a-button>
				</template>
				<template v-else>
					{{ fixInteger(parseFloat(text),3) }}
				</template>
			</template>
            </a-table>
        </div>
    </div>
	<a-modal v-model:open="state.tableShow" wrapClassName="gis_table_modal" :afterClose="()=>{state.tableShow=false;state.tableData=[]}" :centered="true" :footer="null" :closable="false" :maskClosable="false">
		<screen-scale>
			<div class="modal_top">
				<p>{{ state.lineName }}</p>
				<close-outlined class="pointer" @click="()=>{state.tableShow=false;state.tableData=[]}" />
			</div>
			<div class="gis_table_content">
				<div class="table_button">
					<a-button type="primary" @click="exportTable(1)">{{ $t('导出') }}</a-button>
				</div>
				<a-table
					:columns="[
						{
							title: $t('负载率'),
							width:120,
							dataIndex: 'load_ratio'
						},
						{
							title: $t('故障原因'),
							dataIndex: 'caused_by_fault'
						},
						{
							title: $t('故障类型'),
							dataIndex: 'caused_by_fault_type',
							width:120,
						}
					]"
					:data-source="state.tableData"
					:scroll="{y:428}"
					:loading="state.tableLoading"
				>
					<template #bodyCell="{ column,text }">
						<template v-if="['load_ratio'].includes(column.dataIndex)">
							{{ fixInteger(parseFloat(text),3) }}
						</template>
						<template v-if="['type'].includes(column.dataIndex)">
							{{ route.query.type=='n_1'? $t('N-1开断') : $t('N-2开断') }}
						</template>
					</template>
				</a-table>
			</div>
		</screen-scale>
    </a-modal>
</template>
<script setup>
import { t } from '@/utils/common'
import { reactive, ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { GetLineJobTaskResult } from '@/api/gis'
import { fixInteger, exportExcel, exportExcels } from '@/utils/gis'
import dayjs from 'dayjs'
const emit = defineEmits(['cancel', 'loading'])
const route = useRoute()
const column1 = [
	{
		title: t('名称'),
		dataIndex: 'name',
		customFilterDropdown: true,
		onFilter: (value, record) => record.name.toString().toLowerCase().includes(value.toLowerCase()),
		onFilterDropdownOpenChange: visible => {
			if (visible) {
				setTimeout(() => {
					searchInput.value.focus()
				}, 100)
			}
		}
	},
	{
		title: t('最大负载率'),
		dataIndex: 'load_ratio_max'
	},
	{
		title: t('越限次数'),
		dataIndex: 'load_ratio_count'
	},
	{
		title: t('类型'),
		dataIndex: 'type'
	},
	{
		title: t('操作'),
		dataIndex: 'detail_data'
	}
]
const column2 = [
	{
		title: t('母线名'),
		dataIndex: 'name',
		customFilterDropdown: true,
		onFilter: (value, record) => record.name.toString().toLowerCase().includes(value.toLowerCase()),
		onFilterDropdownOpenChange: visible => {
			if (visible) {
				setTimeout(() => {
					searchInput.value.focus()
				}, 100)
			}
		}
	},
	{
		title: 'ik_ka',
		dataIndex: 'ik_ka'
	},
	{
		title: 'imax_ka',
		dataIndex: 'imax_ka'
	},
	{
		title: t('基电压'),
		dataIndex: 'vn_kv'
	},
	{
		title: t('分区'),
		dataIndex: 'zone'
	}
]
const sheetConfig1 = [
	{
		sheetName: t('节点'),
		value: 'res_bus',
		title: [],
		showColumn: [],
		column: [
			{
				title: t('名称'),
				dataIndex: 'name',
				customFilterDropdown: true,
				onFilter: (value, record) => record.name.toString().toLowerCase().includes(value.toLowerCase()),
				onFilterDropdownOpenChange: visible => {
					if (visible) {
						setTimeout(() => {
							searchInput.value.focus()
						}, 100)
					}
				}
			},
			{
				title: t('有功功率'),
				dataIndex: 'p_mw'
			},
			{
				title: t('无功功率'),
				dataIndex: 'q_mvar'
			},
			{
				title: t('相位角'),
				dataIndex: 'va_degree'
			},
			{
				title: t('电压'),
				dataIndex: 'rms_voltage'
			}
		]
	},
	{
		sheetName: t('机组'),
		value: 'res_gen',
		title: [],
		showColumn: [],
		column: [
			{
				title: t('名称'),
				dataIndex: 'name',
				customFilterDropdown: true,
				onFilter: (value, record) => record.name.toString().toLowerCase().includes(value.toLowerCase()),
				onFilterDropdownOpenChange: visible => {
					if (visible) {
						setTimeout(() => {
							searchInput.value.focus()
						}, 100)
					}
				}
			},
			{
				title: t('有功功率'),
				dataIndex: 'p_mw'
			},
			{
				title: t('无功功率'),
				dataIndex: 'q_mvar'
			},
			{
				title: t('相位角'),
				dataIndex: 'va_degree'
			},
			{
				title: t('电压'),
				dataIndex: 'rms_voltage'
			}
		]
	},
	{
		sheetName: t('线路'),
		value: 'res_line',
		title: [],
		showColumn: [],
		column: [
			{
				title: t('名称'),
				dataIndex: 'name',
				customFilterDropdown: true,
				onFilter: (value, record) => record.name.toString().toLowerCase().includes(value.toLowerCase()),
				onFilterDropdownOpenChange: visible => {
					if (visible) {
						setTimeout(() => {
							searchInput.value.focus()
						}, 100)
					}
				}
			},
			{
				title: t('电流'),
				dataIndex: 'i_from_ka'
			},
			{
				title: t('线路负载率'),
				dataIndex: 'loading_percent'
			},
			{
				title: t('有功功率'),
				dataIndex: 'p_from_mw'
			},
			{
				title: t('无功功率'),
				dataIndex: 'q_from_mvar'
			}
		]
	},
	{
		sheetName: t('主变'),
		value: 'res_trafo',
		title: [],
		showColumn: [],
		column: [
			{
				title: t('名称'),
				dataIndex: 'name',
				width: 250,
				customFilterDropdown: true,
				onFilter: (value, record) => record.name.toString().toLowerCase().includes(value.toLowerCase()),
				onFilterDropdownOpenChange: visible => {
					if (visible) {
						setTimeout(() => {
							searchInput.value.focus()
						}, 100)
					}
				}
			},
			{
				title: t('负载率'),
				dataIndex: 'loading_percent'
			},
			{
				title: t('高压侧电流'),
				dataIndex: 'i_hv_ka'
			},
			{
				title: t('低压侧电流'),
				dataIndex: 'i_lv_ka'
			},
			{
				title: t('高压侧有功功率'),
				dataIndex: 'p_hv_mw'
			},
			{
				title: t('低压侧有功功率'),
				dataIndex: 'p_lv_mw'
			},
			{
				title: t('高压侧无功功率'),
				dataIndex: 'q_hv_mvar'
			},
			{
				title: t('低压侧无功功率'),
				dataIndex: 'q_lv_mvar'
			},
			{
				title: t('高压侧电压'),
				dataIndex: 'hv_rms_voltage'
			},
			{
				title: t('低压侧电压'),
				dataIndex: 'lv_rms_voltage'
			}
		]
	},
	{
		sheetName: t('负荷'),
		value: 'res_load',
		title: [],
		showColumn: [],
		column: [
			{
				title: t('名称'),
				dataIndex: 'name',
				customFilterDropdown: true,
				onFilter: (value, record) => record.name.toString().toLowerCase().includes(value.toLowerCase()),
				onFilterDropdownOpenChange: visible => {
					if (visible) {
						setTimeout(() => {
							searchInput.value.focus()
						}, 100)
					}
				}
			},
			{
				title: t('有功功率'),
				dataIndex: 'p_mw'
			},
			{
				title: t('无功功率'),
				dataIndex: 'q_mvar'
			}
		]
	}
]
const sheetConfig2 = [
	{
		sheetName: t('单相短路'),
		value: 'data_1_ph',
		title: [],
		showColumn: []
	},
	{
		sheetName: t('三相短路'),
		value: 'data_3_ph',
		title: [],
		showColumn: []
	}
]
const rowClassName = (record) => {
	return record['Ik/kA'] > record['Imax/kA'] ? 'redRow' : ''
}
const getColumn = () => {
	if (route.query.type == 'ac_power_flow') {
		return state.columnObj[[state.type]]
	} else {
		return state.column
	}
}
const getTableData = () => {
	if (route.query.type == 'ac_power_flow' || route.query.type == 'short_circuit') {
		return state.gisTableDObj[state.type]
	} else {
		return state.gisTableData
	}
}
const state = reactive({
	column: [],
	tableData: [],
	tableShow: false,
	type: route.query.type == 'ac_power_flow' ? 'res_bus' : 'data_1_ph',
	lineName: undefined,
	searchText: '',
	searchedColumn: '',
	columnObj: {
		res_bus: sheetConfig1.find(item	=> item.value == 'res_bus').column,
		res_gen: sheetConfig1.find(item	=> item.value == 'res_gen').column,
		res_line: sheetConfig1.find(item	=> item.value == 'res_line').column,
		res_trafo: sheetConfig1.find(item	=> item.value == 'res_trafo').column,
		res_load: sheetConfig1.find(item	=> item.value == 'res_load').column
	},
	loading: false,
	tableLoading: false,
	gisTableData: [],
	gisTableDObj: {
		res_bus: [],
		res_gen: [],
		res_line: [],
		res_trafo: [],
		res_load: [],
		data_1_ph: [],
		data_3_ph: []
	}
})
const searchInput = ref()
onMounted(async() => {
	state.loading = true
	initData()
})
const handleSearch = (selectedKeys, confirm, dataIndex) => {
	confirm()
	state.searchText = selectedKeys[0]
	state.searchedColumn = dataIndex
}
const handleReset = clearFilters => {
	clearFilters({
		confirm: true
	})
	state.searchText = ''
}
const openTable = (data) => {
	state.lineName = data.name
	state.tableLoading = true
	state.tableShow = true
	GetLineJobTaskResult({

		result_file_path: route.query.filePath,
		filter_name: data.name,
		group: 'detail_output'
	}).then(res => {
		state.tableLoading = false
		state.tableData = res.data.map(item => {
			return Object.assign(item, {
				caused_by_fault_type: route.query.type == 'n_1' ? t('N-1开断') : t('N-2开断')
			})
		})
	}).catch(() => {
		state.tableLoading = false
	})
}
const initData = () => {
	GetLineJobTaskResult({

		result_file_path: route.query.filePath,
		group: 'output'
	}).then(res => {
		if (route.query.type == 'short_circuit') {
			state.column = column2
			state.gisTableDObj = res
		} else if (route.query.type == 'n_1' || route.query.type == 'n_2') {
			state.column = column1
			state.gisTableData = res.data
		} else {
			state.gisTableDObj = res.data
		}
		state.loading = false
		emit('cancel')
	}).catch(() => {
		state.tableLoading = false
	})
}
const exportTable = (val) => {
	if (val === 0) {
		if (route.query.type == 'n_1' || route.query.type == 'n_2') {
			exportExcel(state.gisTableData, {
				'name': t('名称'),
				'load_ratio_max': t('最大负载率'),
				'load_ratio_count': t('越限次数'),
				'type': t('类型')
			}, route.query.type == 'n_1' ? t('N-1故障') : t('N-2故障'), t('计算结果') + dayjs(new Date()).format('YYYYMMDD_HHmmss') + '.xlsx')
		} else if (route.query.type == 'short_circuit') {
			exportExcels(
				sheetConfig2.map(item => {
					return {
						'sheetName': item.sheetName,
						'title': column2.map(item => item.title),
						'data': state.gisTableDObj[item.value].map(item1 => column2.map(item2 => item2.dataIndex).map(key => item1[key]))
					}
				}),
				t('短路计算') + dayjs(new Date()).format('YYYYMMDD_HHmmss') + '.xlsx')
		} else if (route.query.type == 'ac_power_flow') {
			exportExcels(
				sheetConfig1.map(item => {
					return {
						'sheetName': item.sheetName,
						'title': item.column.map(item => item.title),
						'data': state.gisTableDObj[item.value].map(item1 => item.column.map(item2 => item2.dataIndex).map(key => item1[key]))
					}
				}),
				t('交流潮流') + dayjs(new Date()).format('YYYYMMDD_HHmmss') + '.xlsx')
		}
	} else {
		exportExcel(state.tableData, {
			'load_ratio': t('负载率'),
			'caused_by_fault': t('故障原因'),
			'caused_by_fault_type': t('故障类型')
		}, state.lineName, state.lineName + '.xlsx')
	}
}
</script>
<style lang="scss" scoped>
    .gis_table{
        &:deep(){
			.redRow{
				td{
					background:rgba(red, 0.35)!important;
				}
			}
            .ant-table-wrapper .ant-table{
                min-height: 465px;
                td,th{
                    padding: 10px 16px;
                }
				td{
					padding: 0 16px;
					height: 42.5px;
				}
            }
			.ant-table-filter-trigger {
				font-size: 20px;
				color: #3678bf;
			}
			.ant-table-placeholder{
				td{
					border: none!important;
				}
			}
        }
        .table_select{
            margin-bottom: 20px;
			position: relative;
			height: 43px;
			button{
				position: absolute;
				right: 0;
			}
        }
    }
</style>
<style lang="scss">
	.gis_table_modal{
		padding: 20px;
		.ant-modal{
			width: auto!important;
		}
		.ant-modal-body{
			padding:10px 30px 10px;
			// width: 700px;
		}
		.gis_table_content{
			width: 700px!important;
			.table_button{
				padding: 10px 0;
				display: flex;
				justify-content: flex-end;

			}
		}
		.ant-table-wrapper .ant-table{
			min-height: 465px;
			td,th{
				padding: 10px 16px;
			}
		}
		.ant-table-placeholder{
			td{
				border: none!important;
			}
		}
	}
</style>
