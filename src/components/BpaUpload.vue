<template>
  <a-modal
    wrapClassName="modal_bpaUpload"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    width="475px"
    :maskClosable="false"
  >
    <div class="user-select">
      <div class="modal_top">
        <p>{{ $t('上传') }}</p>
        <close-outlined class="pointer" @click="closeModal" />
      </div>
      <a-spin :spinning="state.loading" size="large" :tip="$t('文件上传中')">
        <div class="modal_content">

          <div class="bottom_upload">
            <a-upload
              v-model:file-list="state.fileList"
              :beforeUpload="()=>false"
              :showUploadList="false"
              accept=".dat,.tc"
              :multiple="state.multiple"
              name="file"
            >
              <div class="upload">
                <a-button class="upload_btn center">
                  <template #icon><folder-open-filled /></template>
                  {{ $t('选择') }}...
                </a-button>
              </div>
            </a-upload>
            {{ state.fileName }}
          </div>
          <div>
            <a-input v-model:value="state.version" :placeholder="$t('请输入版本名称')"/>
          </div>
          <div>
            <a-select
              v-model:value="state.pre_version"
              show-search
              :placeholder="$t('请选择前述版本')"
              :options="state.options"
              :filter-option="filterOption"
              :style="{width: '100%'}"
            >
            </a-select>
          </div>
          <div class="modal_btns_box">
            <a-button @click="handleOk" type="primary" :style="{color:'#fff'}" size="small">{{ $t('确认') }}</a-button>
            <a-button @click="closeModal" size="small" :style="{margin:'0 17px'}">{{ $t('取消') }}</a-button>
          </div>
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>
<script setup>
import { reactive, defineEmits, onMounted, watch } from 'vue'

import message from '@/utils/message'
import { uploadFileInit, uploadFileSlice, uploadFileMerge, getBpaVersionList, bpaAdd } from '@/api/index'
import { t } from '@/utils/common'

const emits = defineEmits(['close', 'confirm'])

const state = reactive({
	loading: false,
	visible: true,
	multiple: false,
	options: [],
	fileList: [],
	file_id: null,
	fileName: '',
	version: '',
	pre_version: null
})

const filterOption = (input, option) => {
	return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const handleOk = () => {
	if (!state.file_id || !state.version) return message.warning(t('请填写完整信息') + '！')
	bpaAdd({
		'file_id': state.file_id,
		'name': state.fileName,
		'version': state.version,
		'pre_version': state.pre_version ? state.pre_version : 0
	}).then(res => {
		if (res.code == 1) {
			message.success(t('新增成功') + '！')
			emits('confirm')
		}
	})
}

const closeModal = () => {
	emits('close')
}

watch(() => state.fileList, (fileList) => {
	if (fileList.length == 0) return
	state.loading = true
	const formdata = new FormData()
	formdata.append('file', fileList[0].originFileObj)
	uploadFileInit(formdata).then(res => {
		if (res.code == 1) {
			state.fileList = []
			state.file_id = res.data.file_id
			upload_file_slice(fileList[0].originFileObj, res.data.file_id)
		} else {
			state.fileList = []
		}
	})
})

const upload_file_slice = async(file, file_id) => {
	var name = file.name
	var size = file.size
	var shardSize = 1000 * 1000 * 1
	var shardCount = Math.ceil(size / shardSize) - 1
	var start
	var end
	let packet
	var i = 0

	var number_progress = {}
	for (let j = 0; j <= shardCount; j++) {
		number_progress[j] = false
	}
	while (i <= shardCount) {
		start = i * shardSize
		end = start + shardSize
		packet = file.slice(start, end)
		const data = {
			file_id: file_id,
			number: i,
			file: packet
		}

		await uploadFileSlice(data).then((res) => {
			number_progress[res.data.number] = true

			if (i >= shardCount) {
				const data = {
					name: name,
					file_id: file_id
				}
				uploadFileMerge(data).then((res) => {
					if (res.code == 1) {
						state.fileName = res.data.name
						message.success(t('上传成功') + '！')
						state.loading = false
					}
				}).catch((error) => {
					console.log(error)
					state.loading = false
				})
			}
		}).catch((error) => {
			console.log(error)
			state.loading = false
		})
		i += 1
	}
}

const getBpaVersions = () => {
	getBpaVersionList({
		'search': ''
	}).then(res => {
		if (res.code == 1) {
			state.options = [{
				value: 0,
				label: t('无前述版本')
			}].concat(res.data.bpas.map(item => {
				return {
					value: item.id,
					label: item.version
				}
			}))
		}
	})
}

onMounted(() => {
	getBpaVersions()
})

</script>
<style lang="scss" scoped>
  .modal_bpaUpload{
    .ant-modal{
      .ant-modal-body{
        >div{
          .modal_content{
            padding: 17px 35px;
            text-align: left;
            // .ant-input-number .ant-input-number-input {
            //   width: 100%;
            //   height: 35px;
            // }
            >div {
              margin: 20px;
            }
            .bottom_upload{
              position: relative;
              border: 1px solid #d9d9d9;
              border-radius: 6px;
              height: 35px;
              padding-left: 110px;
              line-height: 35px;
              text-align: left;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
            .upload{
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 35px;
              // border: 1px solid red;
              .upload_btn{
                height: 34px;
                font-size: 16px;
                line-height: 22px;
                width: 100px;
                background: var(--base-color);
                color: #fff;
                border: none;
              }
            }
          }

          .modal_btns_box{
            // margin-top: 30px;
            text-align: center;
            button{
              width: 120px;
              height: 35px;
              letter-spacing: 0;
            }
          }

        }
      }
    }
  }
</style>
