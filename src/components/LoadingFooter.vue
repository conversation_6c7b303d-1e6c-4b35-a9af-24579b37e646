<template>
  <div class="footer">
     <div class="loading_slider" v-if="state.show">
      <div class="lineStep">
          <div :style="{width:state.width+'%'}">
              <div></div>
          </div>
      </div>
      <p>{{ $t('等待中') }}......</p>
     </div>
   <div v-else></div>
     <div v-if="state.delayDownload&&!state.DialogShow1&&!state.updateShow" class="version_text" @click="downloadUpdate">
          {{ state.updateVersion+`${ $t('版本已经上线，点击下载更新') }` }}
     </div>
     <div v-if="state.delayUpdate&&!state.DialogShow1&&!state.updateShow" class="version_text" @click="installUpdate">
          {{ state.updateVersion+`${ $t('版本已经下载完成，点击安装更新') }` }}
     </div>
     <div class="update_slider" v-if="state.updateShow">
          <p>{{ $t('更新包下载中') }}</p>
          <div>
              <a-progress :percent="state.percent" />
          </div>
     </div>
   <div></div>
  </div>
<dialog-box :logStr="state.logStr" @close="closeDownload" @confirm="confirmDownload" :title="`${ $t('软件更新') }`" :type="2" v-if="state.DialogShow1" :text="state.text"></dialog-box>
<dialog-box @close="closeUpdate" @confirm="confirmUpdate" :title="`${ $t('软件更新') }`" :type="3" v-if="state.DialogShow2" :text="`${ $t('安装包下载完成，是否立即更新？') }`"></dialog-box>
<dialog-box :logStr="state.logStr" @close="state.DialogShow3=false" :title="`${ $t('软件更新') }`" :type="4" v-if="state.DialogShow3" :text="state.text"></dialog-box>
</template>
<script setup>
/* eslint-disable no-unused-vars */
import { loadingStore } from '@/store/loadingStore'
import { storeToRefs } from 'pinia'
import { onMounted, reactive, watch, onUnmounted } from 'vue'
import { SmileOutlined } from '@ant-design/icons-vue'
import { openModals } from '@/utils/teap'
import { t } from '@/utils/common'
import message from '@/utils/message'
const storeModal = loadingStore()
const { loading, loadingTime } = storeToRefs(storeModal)
const state = reactive({
	show: false,
	width: 0,
	timer: undefined,
	percent: 0,
	DialogShow1: false,
	DialogShow2: false,
	DialogShow3: false,
	text: '',
	updateShow: false,
	version: undefined,
	logStr: '',
	installPath: localStorage.getItem('installPath') ? localStorage.getItem('installPath') : undefined,
	updateVersion: localStorage.getItem('updateVersion') ? localStorage.getItem('updateVersion') : undefined,
	ignoreVersion: localStorage.getItem('ignoreVersion') ? localStorage.getItem('ignoreVersion') : undefined,
	delayUpdate: localStorage.getItem('delayUpdate') ? Boolean(localStorage.getItem('delayUpdate')) : undefined,
	delayDownload: localStorage.getItem('delayDownload') ? Boolean(localStorage.getItem('delayDownload')) : undefined
})
function compareVersion(version1, version2) {
	const v1Parts = version1.split('.').map(Number)
	const v2Parts = version2.split('.').map(Number)
	for (let i = 0; i < Math.max(v1Parts.length, v2Parts.length); i++) {
		const v1Part = v1Parts[i] || 0
		const v2Part = v2Parts[i] || 0
		if (v1Part > v2Part) {
			return 1
		}
		if (v1Part < v2Part) {
			return -1
		}
	}
	return 0
}
const downloadUpdate = () => {
	if (localStorage.getItem('delayDownload')) {
		window.electronApi.sendToMain('check_update', 'handle_update')
	} else {
		window.electronApi.sendToMain('download_update')
	}
	state.delayDownload = false
	state.updateShow = true
}
const installUpdate = () => {
	state.delayUpdate = false
	if (localStorage.getItem('delayUpdate')) {
		window.electronApi.sendToMain('install_update_later', state.installPath)
	} else {
		window.electronApi.sendToMain('install_update')
	}
}
const confirmDownload = (val) => {
	state.DialogShow1 = false
	state.delayDownload = false
	if (val) {
		window.electronApi.sendToMain('download_update')
		state.updateShow = true
	} else {
		state.ignoreVersion = state.updateVersion
	}
}
const confirmUpdate = () => {
	window.electronApi.sendToMain('install_update')
	state.delayUpdate = false
	state.DialogShow2 = false
}
const closeDownload = () => {
	state.DialogShow1 = false
}
const closeUpdate = () => {
	state.DialogShow2 = false
}
onMounted(() => {
	if (navigator.userAgent.includes('Electron')) {
		state.version = window.config ? window.config.version : localStorage.getItem('version')

		window.electronApi.receiveFromMain('update_find', (args, type) => {
			if (type == 'check_update') {
				if (state.ignoreVersion && state.ignoreVersion != args.version) {
					state.updateVersion = args.version
					state.installPath = args.path
				}
				state.text = t('检测到新版本') + args.version + `（${t('当前版本')}${state.version}）`
				state.logStr = args.releaseNote
				state.delayDownload = true
				state.DialogShow1 = true
				return
			}
			if (state.ignoreVersion && state.ignoreVersion == args.version) {
				return
			}
			if (state.updateVersion && state.updateVersion == args.version && (state.delayUpdate || state.delayDownload)) {
				return
			}
			state.updateVersion = args.version
			state.installPath = args.path
			state.text = t('检测到新版本') + args.version + `（${t('当前版本')}${state.version}）`
			state.logStr = args.releaseNote
			state.delayDownload = true
			state.DialogShow1 = true
		})
		window.electronApi.receiveFromMain('update_not_find', (args, type) => {
			if (state.delayDownload) {
				state.delayDownload = false
			}
			if (state.delayUpdate) {
				state.delayUpdate = false
			}
			if (type == 'check_update') {
				state.text = t('当前版本') + args.version + t('已是最新版本')
				state.logStr = args.releaseNote
				state.DialogShow3 = true
			}
		})
		window.electronApi.receiveFromMain('upadate_progress', (args) => {
			if (!state.updateShow) state.updateShow = true
			state.percent = args.percent == 100 ? 100 : +(args.percent).toFixed(2)
		})
		window.electronApi.receiveFromMain('update_error', () => {
			message.error(t('自动更新检测失败、请检查网络连接') + '！')
		})
		window.electronApi.receiveFromMain('install_error', (args) => {
			message.error(t('安装包已过期即将重新下载更新'))
			window.electronApi.sendToMain('check_update', 'auto_update')
		})
		window.electronApi.receiveFromMain('upadate_finish', (args) => {
			state.updateShow = false
			state.percent = 0
			state.DialogShow2 = true
			state.delayUpdate = true
		})
		window.electronApi.receiveFromMain('electron_error', (args) => {
			message.error(args)
		})
	}
})
const beforeDestroy = () => {
	if (navigator.userAgent.includes('Electron')) {
		if (state.version) localStorage.setItem('version', state.version)
		if (state.updateVersion) localStorage.setItem('updateVersion', state.updateVersion)
		if (state.ignoreVersion) localStorage.setItem('ignoreVersion', state.ignoreVersion)
		if (state.installPath) localStorage.setItem('installPath', state.installPath)
		if (state.delayUpdate) {
			localStorage.setItem('delayUpdate', state.delayUpdate)
		} else {
			localStorage.removeItem('delayUpdate')
		}
		if (state.delayDownload) {
			localStorage.setItem('delayDownload', state.delayDownload)
		} else {
			localStorage.removeItem('delayDownload')
		}
	}
}
window.addEventListener('beforeunload', beforeDestroy)
onUnmounted(() => {
	window.removeEventListener('beforeunload', beforeDestroy)
})
watch(() => loading.value, v => {
	if (v == true) {
		state.width = 0
		state.show = v
		state.timer = setInterval(() => {
			if (state.width >= 90) {
				return
			}
			if (state.width < 50) {
				state.width = (state.width + ((100 / loadingTime.value) - Math.random() * 0.2) * 2) >= 100 ? 90 : (state.width + ((100 / loadingTime.value) - Math.random() * 0.2) * 2)
			} else if (state.width < 80) {
				state.width = (state.width + ((100 / loadingTime.value) - Math.random() * 0.2) * 1) >= 100 ? 90 : (state.width + ((100 / loadingTime.value) - Math.random() * 0.2) * 1)
			} else if (state.width < 90) {
				state.width = (state.width + ((100 / loadingTime.value) - Math.random() * 0.2) * 0.22) >= 100 ? 90 : (state.width + ((100 / loadingTime.value) - Math.random() * 0.2) * 0.22)
			}
		}, 1000)
	} else {
		clearInterval(state.timer)
		state.width = 100
		setTimeout(() => {
			state.show = v
		}, 500)
	}
})
</script>
<style lang="scss" scoped>
  .footer{
      height: 30px;
      background: #ECF0F2;
      padding: 0 20px;
      display: flex;
      align-items: center;
  justify-content: space-between;
      position: relative;
      p{
          color:#9B9EA8;
      }
      >div{
          display: flex;
          align-items: center;
      }
      .lineStep{
          width: 130px;
          height: 15px;
          border: 1px solid rgba($color: #9B9EA8, $alpha: 0.6);
          background-color: #fff;
          border-radius: 6px;
          margin-right: 10px;
          overflow: hidden;
          >div{
              height: 100%;
              // animation: loading-step 10s linear;
              transition-property: width;
              transition-timing-function: linear;
              transition-duration: .5s;
              transition-delay:0s;
              background: linear-gradient(to left, #0081FF,#A8D5FF);
              >div{
                  height: 100%;
                  animation: loading-step 0.5s linear infinite;
                  background: rgba($color: #A8D5FF, $alpha: 0.3);
              }
          }
      }
      .update_slider{
          >div{
              width: 250px;
              margin-left: 10px;
              &:deep(){
                  .ant-progress-line{
                      margin-bottom: 0;
                  }
              }
          }
      }
      .version_text{
          color: var(--base-color);
          &:hover{
              cursor: pointer;
              text-decoration: underline;
          }
      }
      @keyframes loading-step{
          0%{
              width: 0%;
          }
          100%{
              width: 100%;
          }
      }
  }
</style>
