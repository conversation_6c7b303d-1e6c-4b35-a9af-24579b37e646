<template>
	<div class="table_view">
		<div class="table_view_header user-select">
			<div>
				<p :class="state.tableType=='task'?'active':''" @click="resultTabsClick('task')">{{ $t('计算任务列表') }}</p>
				<p :class="state.tableType=='result'?'active':''" @click="resultTabsClick('result')">{{ $t('结果查询列表') }}</p>
			</div>
      <div>
        <PushpinOutlined v-show="!state.PinShow" @click.stop="pinClick(true)"/>
        <PushpinFilled  v-show="state.PinShow" @click.stop="pinClick(false)"/>
			  <DoubleRightOutlined @click="closeTable" style="transform: rotate(90deg);"/>
      </div>
		</div>
		<div class="table_content">
			<div v-show="state.tableType=='task'">
				<table-list @initTable="initTable" :data="state.taskData" ref="child_table_task" :type="1"></table-list>
			</div>
			<div v-show="state.tableType=='result'">
				<table-list @pageChange="pageChange" @search="searchInput" :total="state.resultPageTotal" @initTable="initTable" :data="state.resultData" ref="child_table_result" :type="2"></table-list>
			</div>
		</div>
		<div class="">

		</div>
	</div>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { DoubleRightOutlined } from '@ant-design/icons-vue'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
import { cancelToken } from '@/request/index'
import { debounce } from '@/utils/gis'
import { onMounted, onUnmounted, reactive, ref, watch, nextTick } from 'vue'
import { getTaskTableApi } from '@/api/index'
const storeSetting = settingStore()
const { tableShow, tableType, isChromeHigh } = storeToRefs(storeSetting)

const state = reactive({
	tableType,
	Show: false,
	PinShow: false,
	taskData: [],
	resultData: [],
	taskNewData: [],
	finishNewData: [],
	timer: undefined,
	finished_data_last_update_datetime: undefined,
	unfinished_data_last_update_datetime: undefined,
	tableTimer: undefined,
	stopTimer: false,
	resultPageIndex: 1,
	resultPageTotal: 1,
	resultSearch: {},
	resultPageSize: 20
})
let cancel
const child_table_result = ref()
const child_table_task = ref()

const transformScale = ref(1)

const resultTabsClick = (type) => {
	state.tableType = type
	nextTick(() => {
		type == 'task' ? child_table_task.value.screenScale() : child_table_result.value.screenScale()
	})
}
const initTable = (type, res) => {
	state.stopTimer = true
	clearTimeout(state.tableTimer)
	if (res) {
		state.stopTimer = false
		handleNotify(res)
	} else {
		if (typeof cancel === 'function') cancel()
		getTaskTableApi({
			unfinished_data_last_update_datetime: state.unfinished_data_last_update_datetime,
			finished_data_last_update_datetime: state.finished_data_last_update_datetime,
			finished_data_page_size: state.resultPageSize,
			finished_data_page_current: state.resultPageIndex,
			finished_data_filter_string: state.resultSearch.searchText,
			finished_data_job_type_id_list: state.resultSearch.job_type_id
			// eslint-disable-next-line new-cap
		}, new cancelToken(function executor(c) {
			cancel = c
		})).then(res => {
			if (res.code == 1) {
				state.unfinished_data_last_update_datetime = res.unfinished_data_last_update_datetime
				state.finished_data_last_update_datetime = res.finished_data_last_update_datetime
				state.stopTimer = false
				handleNotify(res, type)
			}
		})
	}
}
const handleNotify = (res, type) => {
	if (res.unfinished_data_list) {
		state.taskNewData = res.unfinished_data_list
	}
	if (res.finished_data_list) {
		if (res.finished_data_list.length == 0 && state.resultPageIndex != 1) {
			state.resultPageIndex = state.resultPageIndex - 1
			initTable()
			return
		}
		state.finishNewData = res.finished_data_list
	}
	if (res.finished_data_count || res.finished_data_count === 0) state.resultPageTotal = res.finished_data_count
	if (res.unfinished_data_list) state.taskData = res.unfinished_data_list
	if (res.finished_data_list) {
		state.resultData = res.finished_data_list.map(item => {
			item.job_end_datetime = item.job_end_datetime.substring(5)
			return item
		})
	}
	refreshTable()
}
const refreshTable = () => {
	state.tableTimer = setTimeout(async() => {
		if (state.stopTimer) return
		const res = await getTaskTableApi({
			unfinished_data_last_update_datetime: state.unfinished_data_last_update_datetime,
			finished_data_last_update_datetime: state.finished_data_last_update_datetime,
			finished_data_page_size: state.resultPageSize,
			finished_data_page_current: state.resultPageIndex,
			finished_data_filter_string: state.resultSearch.searchText,
			finished_data_job_type_id_list: state.resultSearch.job_type_id
			// eslint-disable-next-line new-cap
		}, new cancelToken(function executor(c) {
			cancel = c
		}))
		if (res && res.code == 1 && state.stopTimer != true) {
			state.unfinished_data_last_update_datetime = res.unfinished_data_last_update_datetime
			state.finished_data_last_update_datetime = res.finished_data_last_update_datetime
			handleNotify(res)
		}
	}, 3500)
}
const pageChange = (page) => {
	state.resultPageIndex = page.current
	state.resultPageSize = page.pageSize
	initTable('resultPage')
}
const searchInput = (search) => {
	state.resultPageIndex = 1
	state.resultSearch = search
	initTable('resultSearch')
}
const closeTable = () => {
	child_table_task.value.closeWs()
	tableShow.value = false
}
const pinClick = (val) => {
	state.PinShow = val
	Mitt.emit('handlePin', val)
}

const screenScale = () => {
	transformScale.value = document.getElementsByClassName('home-body')[0].style.zoom || 1
}
const debouncedScreenScale = debounce(screenScale, 200)
onMounted(() => {
	if (isChromeHigh.value) {
		screenScale()
		window.addEventListener('resize', debouncedScreenScale)
	}
	getTaskTableApi({
		finished_data_page_size: state.resultPageSize
	}).then(res => {
		if (res.code == 1) {
			state.unfinished_data_last_update_datetime = res.unfinished_data_last_update_datetime
			state.finished_data_last_update_datetime = res.finished_data_last_update_datetime
			if (res.finished_data_count) {
				state.resultPageTotal = res.finished_data_count
				state.resultPageIndex = Math.ceil(res.finished_data_count / state.resultPageSize)
				child_table_result.value.changePageSize(state.resultPageIndex)
			}
			if (res.unfinished_data_list) state.taskData = res.unfinished_data_list
			if (res.finished_data_list) {
				state.resultData = res.finished_data_list.map(item => {
					item.job_end_datetime = item.job_end_datetime.substring(5)
					return item
				})
			}
			refreshTable()
		}
	})
})
const beforeDestroy = () => {
	if (typeof cancel === 'function') cancel()
	clearTimeout(state.tableTimer)
}
onUnmounted(() => {
	if (typeof cancel === 'function') cancel()
	clearTimeout(state.tableTimer)
	window.removeEventListener('beforeunload', beforeDestroy)
	if (isChromeHigh.value) window.removeEventListener('resize', debouncedScreenScale)
})
window.addEventListener('beforeunload', beforeDestroy)
watch(() => tableShow.value, v => {
	state.Show = v
}, { immediate: true })
</script>
<style lang="scss" scoped>
.table_view{
  zoom: v-bind(transformScale);
	position: fixed;
	z-index: 100;
	right: 0;
	bottom: 28px;
	height: calc(100% - 120px - 40px - 35px - 30px + 1px);
	width: 680px;
	// width: 675px;
	background-color: #fff;
	border: 1px solid #A2B5CC;
	border-top: none;
	border-left: none;
  box-shadow: -10px -10px 3px -10px #465da9;
  box-sizing: border-box;
  // overflow: hidden;
	.table_content{
		padding: 10px 10px 10px;
		height: calc(100% - 33px);
		>div{
			border: 1px solid #A2B5CC;
			border-radius: 8px;
			height: 100%;
			overflow: hidden;
		}
	}
	.table_view_header{
		height: 33px;
		padding-top: 2px;
		font-size: 16px;
		background-color: var(--theme-tabs-color);
		display: flex;
		justify-content: space-between;
		>div{
			display: flex;
			padding: 4px 10px 0px;
		}
		span{
			color: #fff;
			font-size: 24px;
			margin-right: 5px;
		}
		p{
			text-align: center;
			line-height: 25px;
			padding: 0 15px;
			letter-spacing: 2px;
			border: 1px solid transparent;
			font-weight: bolder;
			border-radius: 8px 8px 0 0;
			&:hover{
			cursor: pointer;
			}
		}
		.active{
			background-color: rgb(248, 248, 248);
		}
	}
	>div:last-child{
		position: absolute;
		height: calc(100% + 0px);
		width: 1px;
		background-color: #fff;
		// border-left: 1px solid #A2B5CC;
    box-shadow: -15px 0 10px -10px #ef0808;
		border-right: 1px solid #A2B5CC;
		bottom: 0px;
		left: -1px;
	}
}
</style>
