<template>
  <div class="itemized-main">
    <div class="itemized-left">
      <div class="line-title" @change="handleUnsaved">{{ $t('主要参数') }}</div>
      <AgGrid ref="agGridTopologyRef" :isEdit="'itemized'"></AgGrid>
    </div>
    <div class="itemized-right">

      <div  class="itemized-right-top">
        <div class="line-title">{{ $t('其他参数') }}</div>
        <AgGrid ref="agGridOtherRef" :isEdit="'itemized'"></AgGrid>
      </div>

      <div class="itemized-right-bottom">
        <div class="itemized-right-btn">
          <div class="line-title">{{ $t('关联时序') }}</div>
          <a-button type="primary" ghost @click.stop="add(record)" :disabled="!state.isHaveTimeseries">{{ $t('新增') }}</a-button>
          <a-button type="primary" ghost @click.stop="correlate(record)" :disabled="!state.isHaveTimeseries">{{ $t('关联') }}</a-button>
          <a-button type="primary" ghost @click.stop="remove(record)" :disabled="!state.isHaveTimeseries">{{ $t('删除') }}</a-button>
        </div>
        <AgGrid ref="agGridItemizecRef" :isEdit="'itemizedTimeseries'" :treeNode="state.treeNode" @handleCorrelate="handleCorrelate"></AgGrid>
      </div>
    </div>

  </div>

  <curve-preview
    v-if="state.curveShow"
    :index="state.timeseries"
    @close="state.curveShow=false"
  >
  </curve-preview>

  <confirm-modal v-if="state.confirmVisible" :confirmTitle="state.confirmTitle" :prompt="state.prompt" :okText="state.okText" :cancelText="state.cancelText" @confirm="handleConfirmModal" @cancel="handleCancelModal" @close="state.confirmVisible = false" ></confirm-modal>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import message from '@/utils/message'
import { routeStore } from '@/store/routeStore'
import { getReadOneRow, basicApi } from '@/api/exampleApi'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
import { t } from '@/utils/common'
const storeSetting = settingStore()
const { isChromeHigh } = storeToRefs(storeSetting)
const route = useRoute()
const store = routeStore()

const agGridItemizecRef = ref(null)
const agGridTopologyRef = ref(null)
const agGridOtherRef = ref(null)

const state = reactive({
	routePath: route.fullPath,
	agselectArr: [],

	treeNode: null,
	rowId: null,

	isHaveTimeseries: true,
	timeseries: [],
	confirm_flag: null,
	topologyData: [],
	otherData: [],
	isMultiple: 'multiple',
	curveShow: false,
	confirmVisible: false,
	confirmTitle: t('注意'),
	prompt: t('请确认操作！'),
	okText: t('直接删除'),
	cancelText: t('解除关联')
})

const openCurvePreview = () => {
	if (state.timeseries.length <= 0) return message.warning(t('暂无可预览的曲线，请先关联') + '！')

	state.curveShow = true
}

const add = () => {
	agGridItemizecRef.value.timeseriesAdd(true, state.treeNode, state.rowId)
}

const correlate = () => {
	agGridItemizecRef.value.handleCorrelateData(state.timeseries)
}

const handleUnsaved = () => {
	store.setTabs(route.fullPath, 'allSave')
}

const handleCorrelate = (data) => {
	let tempArr = []
	if (data.length > 0) {
		tempArr = data.map(item => {
			return item = `${state.rowId}_${item}`
		})
	}
	basicApi({
		'import_string_func': 'teapcase:update_element_timeseries_in_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_name': state.treeNode,
			'selected_ts_data': tempArr,
			'selected_row_id_list': [state.rowId]
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			message.success(res.func_result.message || t('关联成功') + '！')

			deviceRowClick(state.treeNode, state.rowId)
		}
	})
}

const remove = () => {
	state.agselectArr = agGridItemizecRef.value.getSelected()
	if (state.agselectArr.length <= 0) return
	state.confirmVisible = true
}

const handleConfirmModal = () => {
	state.confirmVisible = false
	state.confirm_flag = true
	handleItemizedDel(state.agselectArr)
}
const handleCancelModal = () => {
	state.confirmVisible = false
	state.confirm_flag = false
	handleItemizedDel(state.agselectArr)
}

const handleItemizedDel = (data) => {
	const tempArr = data.map(item => {
		return item.index
	})
	const tempData = state.timeseries.filter(item => {
		return !tempArr.includes(item)
	})
	basicApi({
		'import_string_func': 'teapcase:update_one_element_timeseries_in_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_name': state.treeNode,
			'selected_ts_data': tempData,
			'selected_row_id': Number(state.rowId),
			'confirm': state.confirm_flag
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			message.success(res.func_result.message || t('删除成功') + '！')
			state.confirm_flag = null
			deviceRowClick(state.treeNode, state.rowId)
		} else if (res.func_result.code !== 1) {
			message.error(res.func_result.message || t('保存失败') + '！')
		}
	})
}

const saveItemized = (args) => {
	if (state.routePath !== route.fullPath) return
	state.topologyData = agGridTopologyRef.value.getAgData()
	state.otherData = agGridOtherRef.value.getAgData()

	handleItemizedSave(args)
}
Mitt.on('saveItemized', saveItemized)

const handleItemizedSave = (args) => {
	if (state.routePath !== route.fullPath) return

	const tempArr = [...state.topologyData, ...state.otherData]
	if (args.type && args.type == 'treeChange') {
		const tempArr = args.treeNode_old.split('-')
		state.treeNode = tempArr[0]
		state.rowId = tempArr[1]
	}

	basicApi({
		'import_string_func': 'teapcase:write_one_row_to_tc',
		'func_arg_dict': {

			'file_name': route.query.filePath,
			'sheet_name': state.treeNode,
			'row_id': Number(state.rowId),
			'data_list': tempArr,
			'ignore_temp_save': args == 'ignoreTempSave'
		}

	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			agGridItemizecRef.value.saveTsimeseriestable(state.treeNode, state.rowId, args)
			if (args == 'ignoreTempSave') {
				message.success(res.func_result.message || t('保存成功') + '！')
			}
		} else {
			message.error(res.func_result.message || t('保存失败') + '！')
		}
	})
}

const getDeviceRowClick = () => {
	deviceRowClick(state.treeNode, state.rowId)
}
Mitt.on('getDeviceRowClick', getDeviceRowClick)

const deviceRowClick = (treeNode, id) => {
	if (state.routePath !== route.fullPath) return

	state.treeNode = treeNode
	state.rowId = id
	getReadOneRow({
		'import_string_func': 'teapcase:read_one_row_from_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,

			'sheet_name': treeNode,
			'row_id': id
		}
	}).then(res => {
		if (res.code == 1) {
			const { data } = res.func_result
			if (data.timeseries) {
				agGridItemizecRef.value.getAgTableData('timeseries', data.timeseries)
				state.timeseries = data.timeseries
				state.isHaveTimeseries = true
			} else {
				agGridItemizecRef.value.getAgTableData('timeseries', [])
				state.timeseries = []
				state.isHaveTimeseries = false
			}

			data.topology.data.forEach(item => {
			  for (const key in item) {
					if (item[key] === '') {
						item[key] = '—'
					}
				}
			})
			data.other.data.forEach(item => {
			  for (const key in item) {
					if (item[key] === '') {
						item[key] = '—'
					}
				}
			})

			state.topologyData = data.topology
			state.otherData = data.other
			agGridTopologyRef.value.getReadNameData(res.func_result)
			agGridOtherRef.value.getReadNameData(res.func_result)
			agGridItemizecRef.value.getReadNameData(res.func_result)

			agGridTopologyRef.value.updateData(state.topologyData)
			agGridOtherRef.value.updateData(state.otherData)
		}
	})
}

defineExpose({ deviceRowClick, saveItemized, openCurvePreview })

const screenScale = () => {
	document.querySelector('.itemized-main').style.zoom = window.innerWidth <= 1200 ? 1920 / 1200 : 1920 / window.innerWidth
}

onMounted(() => {
	if (isChromeHigh.value) {
		screenScale()
		window.addEventListener('resize', screenScale)
	}
})

onUnmounted(() => {
	if (isChromeHigh.value) {
		window.removeEventListener('resize', screenScale)
	}
})

</script>
<style lang="scss" scoped>
.itemized-main {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  // display: grid;
  // grid-template-columns: 1fr 2fr;
  // grid-column-gap: 18px;
  display: flex;
  justify-content: space-between;
  padding-top: 4px;

  .itemized-left {
    width: 500px;
    height: 100%;
    box-sizing: border-box;
    position: relative;
    // border: 1px solid #A2ADB8;
    .itemized-right-top {
      position: relative;
    }
    .line-title {
      position: absolute;
      width: 180px;
      height: 36px;
      padding: 0 15px;
      border-radius: 6px;
      background: #ECECEC;
      box-sizing: border-box;
      border: 1px solid #A2ADB8;
      line-height: 36px;
      display: flex;
      justify-content: space-between;
    }
  }

  .itemized-right {
    width: calc(100% - 518px);
    height: 100%;
    // background: #F6F6F6;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    > div {
      width: 100%;
      height: 50%;
    }

    .itemized-right-top {
      .line-title {
        position: absolute;
        width: 280px;
        height: 36px;
        padding: 0 15px;
        border-radius: 6px;
        background: #ECECEC;
        box-sizing: border-box;
        border: 1px solid #A2ADB8;
        line-height: 36px;
        display: flex;
        justify-content: space-between;
      }
    }

    .itemized-right-bottom {
      position: relative;
      .itemized-right-btn {
        width: 600px;
        position: absolute;
        top: 3px;
        z-index: 33;
        display: flex;
        justify-content: space-between;
        .line-title {
          width: 280px;
          height: 36px;
          padding: 0 15px;
          border-radius: 6px;
          background: #ECECEC;
          box-sizing: border-box;
          border: 1px solid #A2ADB8;
          line-height: 36px;
          display: flex;
          justify-content: space-between;
        }
        button {
          width: 100px;
          height: 36px;
        }
      }
    }

  }

}
</style>
