<template>
  <a-modal
    wrapClassName="modal_bpa_download"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    width="675px"
    :maskClosable="false"
  >
    <screen-scale>
      <a-spin :spinning="state.loading">
        <div class="modal_top">
          <p>{{ state.description }}</p>
          <close-outlined class="pointer" @click="closeModal" />
        </div>
        <div class="modal_content relative">
          <div>
            <p>{{ $t('实际电容电抗器文件') }}</p>
            <div>
              <a-upload
                v-model:file-list="state.fileList"
                name="file"
                @change="changeFile"
                accept=".xlsx,.csv"
                :multiple="false"
                :beforeUpload="()=>false"
                :maxCount="1"
              >
                <a-button type="primary">{{ $t('上传文件') }}</a-button>
              </a-upload>
            </div>
          </div>
          <div v-for="item in state.formData" :key="item.value">
            <p>{{ item.name }}</p>
            <div v-if="item.type=='bool'">
              <a-radio-group v-model:value="formObj[item.value]">
                <a-radio :value="true">{{ $t('是') }}</a-radio>
                <a-radio :value="false">{{ $t('否') }}</a-radio>
              </a-radio-group>
            </div>
            <div v-else-if="item.type=='float'">
              <a-input-number :controls="false" v-model:value="formObj[item.value]"></a-input-number>
            </div>
          </div>
        </div>
        <div class="modal_btn">
          <a-button @click="confirm" type="primary">{{ $t('确认') }}</a-button>
          <a-button @click="emit('close')">{{ $t('取消') }}</a-button>
        </div>
      </a-spin>
    </screen-scale>
  </a-modal>

</template>
<script setup>
import { reactive, onMounted } from 'vue'

import { useRoute } from 'vue-router'
import { DownloadV3BPAApi, UploadTempFile, DownloadBpaFileApi } from '@/api/index'
import { downloadApiFile } from '@/utils/common.js'

import { getBpaAnaConfig } from '@/api/index'

const route = useRoute()

const emit = defineEmits(['close'])

const props = defineProps({
	timeNo: {
		type: Array
	}
})

const state = reactive({
	visible: true,
	description: '',
	formData: [],
	loading: false,
	fileList: [],
	filePath: undefined
})
const formObj = reactive({

})
const changeFile = async({ file, fileList }) => {
	if (fileList.length > 0) {
		const formdata = new FormData()
		formdata.append('file', file)
		state.loading = true
		UploadTempFile({}, formdata).then(res => {
			state.loading = false
			if (res.code == 1) {
				state.filePath = res.file_path
			}
		})
	}
}

const closeModal = () => {
	emit('close')
}
const confirm = () => {
	state.loading = true
	DownloadV3BPAApi(Object.assign({
		result_file_path: route.query.filePath,
		iclk_no: props.timeNo[0],
		iclk_no_2: props.timeNo[1],
		shunt_real_file: state.filePath,
		cfg_dict: {
			'global_paras': formObj
		}
	}, {})).then(res => {
		if (res.code == 1) {
			res.dat_file_names.forEach((item) => {
				DownloadBpaFileApi({
					file_name: item
				}).then(res => {
					downloadApiFile(res)
					emit('close')
				})
			})
		} else {
			state.loading = false
		}
	}).catch(() => {
		state.loading = false
	})
}

const get_bpa_ana_config = () => {
	getBpaAnaConfig().then(res => {
		if (res.code == 1) {
			const { create_bpa_dat_config } = res.bpa_ana_config
			state.description = create_bpa_dat_config.description
			state.formData = Object.keys(create_bpa_dat_config.global_paras.field_keys).map(item => {
				formObj[item] = create_bpa_dat_config.global_paras.field_keys[item].default
				return {
					name: create_bpa_dat_config.global_paras.field_keys[item].name,
					value: item,
					type: create_bpa_dat_config.global_paras.field_keys[item].type
				}
			}).sort((a, b) => b.type.length - a.type.length)
		}
	})
}

onMounted(() => {
	get_bpa_ana_config()
})

</script>
<style lang="scss">
.modal_bpa_download{
    .ant-modal{
      width: auto!important;
        .ant-modal-body{
            .modal_content{
                width: 700px;
                padding: 20px 30px 80px;
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-column-gap: 20px;
                >div{
                    >p{
                        font-size: 14px;
                        line-height: 36px;
                        font-weight: bolder;
                    }
                    >div{
                        height: 50px;
                    }
                }
                .ant-upload-wrapper{
                    button{
                        padding: 0 10px;
                        height: 26px;
                    }
                    .ant-upload-list-item{
                        margin-top: 0;
                    }
                }
            }
        }
    }
}
</style>
