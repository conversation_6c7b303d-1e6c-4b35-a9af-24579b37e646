<template>
  <div class="count_box">
    <div class="case_info_box">
      <p>{{ $t('算例信息') }}</p>
      <div v-for="item in state.case_info" :key="item.key">{{ item.label }}：{{ item.value }}</div>
      <div>{{ $t('计算类型') }}：{{ state.sim_job_config[state.sim_mode] ? state.sim_job_config[state.sim_mode].long_name : '' }}</div>
      <div v-if="state.common_info.job_exec_cost_time">{{ $t('算例计算总耗时') }}：{{ state.common_info.job_exec_cost_time}} {{ $t('秒') }}</div>
      <div v-if="state.common_info.job_end_datetime">{{ $t('算例计算完成时间') }}：{{ state.common_info.job_end_datetime}}</div>
      <div v-if="state.common_info.app_version">{{ $t('计算平台版本号') }}：{{ state.common_info.app_version}}</div>
    </div>
    <div class="general_box">
      <p>{{ $t('通用设置') }}</p>
      <div v-for="item in state.general_parameters" :key="item.key">{{ item.label }}：{{ item.value }}</div>
    </div>

    <div class="mid_term_box" v-show="state.sim_mode == 'mid_term'">
      <div>
        <p>{{ $t('仿真设置') }}</p>
        <div v-for="item in state.mid_term.simulation" :key="item.key">{{ item.label }}：{{ item.value }}</div>
      </div>
      <div>
        <p>{{ $t('负荷持续时间曲线分段法设置') }}</p>
        <div v-for="item in state.mid_term.partial" :key="item.key">{{ item.label }}：{{ item.value }}</div>
      </div>
    </div>
    <div class="long_term_box" v-show="state.sim_mode == 'long_term'">
      <div>
        <p>{{ $t('仿真设置') }}</p>
        <div v-for="item in state.long_term.simulation" :key="item.key">{{ item.label }}：{{ item.value }}</div>
      </div>
      <div>
        <p>{{ $t('负荷持续时间曲线分段法设置') }}</p>
        <div v-for="item in state.long_term.partial" :key="item.key">{{ item.label }}：{{ item.value }}</div>
      </div>
    </div>
    <div class="short_term_box" v-show="state.sim_mode == 'short_term'">
      <div>
        <p>{{ $t('仿真设置') }}</p>
        <div v-for="item in state.short_term.simulation" :key="item.key">{{ item.label }}：{{ item.value }}</div>
      </div>
      <div>
        <p>{{ $t('滚动模拟设置') }}</p>
        <div v-for="item in state.short_term.scuc" :key="item.key">{{ item.label }}：{{ item.value }}</div>
      </div>
    </div>
    <div class="textarea" v-if="state.log_value">
      <p>{{ $t('仿真日志') }}</p>
      <div class="textarea_content" @mouseenter="onmouseenter" @mousewheel="onmousewheel" @mousemove="onmousemove" @mouseleave="onmouseleave" ref="textarea" v-html="state.log_value">
    </div>
    </div>
  </div>
</template>

<script setup>

import { reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'

import { GetMidTermTaskResult } from '@/api/exampleApi'
import { getLogList } from '@/utils/teap'
import { t } from '@/utils/common.js'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const store = settingStore()
const { wsAcceptType, sim_job_config } = storeToRefs(store)

const route = useRoute()
const emits = defineEmits(['hideLoading', 'showLoading'])

const state = reactive({
	sim_mode: '',
	'case_info': [],
	'general_parameters': [],
	'long_term': {
		'simulation': [],
		'partial': [],
		'fitted': []
	},

	'mid_term': {
		'simulation': [],
		'partial': [],
		'fitted': []
	},
	'short_term': {
		'scuc': [],
		'simulation': []
	},
	common_info: {},
	log_value: '',
	sim_job_config
})

const getMidTermResult = () => {
	Promise.all([GetMidTermTaskResult({
		'group': 'parameter',
		'result_file_path': route.query.filePath
	}), GetMidTermTaskResult({
		'group': '_result.sim_log',
		'result_file_path': route.query.filePath
	})]).then(([res, res2]) => {
		emits('hideLoading')
		if (res.code == 1) {
			const { data } = res
			const { parameter_config } = res.data

			state.sim_mode = data.case_info.sim_mode
			for (const key in data.case_info) {
				if (parameter_config.case_info.items[key]) {
					state.case_info.push({
						label: parameter_config.case_info.items[key].description,

						value: key == 'case_name' ? route.query.name.substring(5) : data.case_info[key],
						key: key
					})
				}
			}

			for (const key in data.general_parameters) {
				if (parameter_config.general_parameters.items[key]) {
					state.general_parameters.push({
						label: parameter_config.general_parameters.items[key].description,
						value: data.general_parameters[key],
						key: key
					})
				}
			}

			for (const key in data.mid_term.simulation) {
				if (parameter_config.mid_term.items.simulation.items[key]) {
					state.mid_term.simulation.push({
						label: key == 'emer_reserve_mode' ? t('停备需求计算模式') : key == 'storage_reserve_mode' ? t('储能提供备用的模式') : parameter_config.mid_term.items.simulation.items[key].description,
						value: data.mid_term.simulation[key] == 'no_reserve' ? t('储能不提供备用') : data.mid_term.simulation[key] == 'upper_limit_soc' ? t('以时刻SOC为上限提供备用') : data.mid_term.simulation[key] == 'upper_limit_min_weekly_soc' ? t('以周内最低SOC为上限提供备用') : data.mid_term.simulation[key] == 'period_max_load' ? t('基于周期内最大负荷') : data.mid_term.simulation[key] == 'current_load' ? t('基于当前时刻负荷') : data.mid_term.simulation[key],
						key: key
					})
				}
			}
			for (const key in data.mid_term.partial) {
				if (parameter_config.mid_term.items.partial.items[key]) {
					state.mid_term.partial.push({
						label: parameter_config.mid_term.items.partial.items[key].description,
						value: data.mid_term.partial[key],
						key: key
					})
				}
			}
			for (const key in data.mid_term.fitted) {
				if (parameter_config.mid_term.items.fitted.items[key]) {
					state.mid_term.fitted.push({
						label: parameter_config.mid_term.items.fitted.items[key].description,
						value: data.mid_term.fitted[key],
						key: key
					})
				}
			}

			for (const key in data.long_term.simulation) {
				if (parameter_config.long_term.items.simulation.items[key]) {
					state.long_term.simulation.push({
						label: key == 'emer_reserve_mode' ? t('停备需求计算模式') : key == 'storage_reserve_mode' ? t('储能提供备用的模式') : key == 'plan_min_install_duration' ? t('待规划设备最短投产时间') : parameter_config.long_term.items.simulation.items[key].description,
						value: data.long_term.simulation[key] == 'no_reserve' ? t('储能不提供备用') : data.long_term.simulation[key] == 'upper_limit_soc' ? t('以时刻SOC为上限提供备用') : data.long_term.simulation[key] == 'upper_limit_min_weekly_soc' ? t('以周内最低SOC为上限提供备用') : data.long_term.simulation[key] == 'period_max_load' ? t('基于周期内最大负荷') : data.long_term.simulation[key] == 'current_load' ? t('基于当前时刻负荷') : data.long_term.simulation[key] == 'period' ? t('周期颗粒度') : data.long_term.simulation[key] == 'quarter' ? t('季度') : data.long_term.simulation[key] == 'halfyear' ? t('半年') : data.long_term.simulation[key] == 'year' ? t('年') : data.long_term.simulation[key],
						key: key
					})
				}
			}
			for (const key in data.long_term.partial) {
				if (parameter_config.long_term.items.partial.items[key]) {
					state.long_term.partial.push({
						label: parameter_config.long_term.items.partial.items[key].description,
						value: data.long_term.partial[key],
						key: key
					})
				}
			}
			for (const key in data.long_term.fitted) {
				if (parameter_config.long_term.items.fitted.items[key]) {
					state.long_term.fitted.push({
						label: parameter_config.long_term.items.fitted.items[key].description,
						value: data.long_term.fitted[key],
						key: key
					})
				}
			}

			for (const key in data.short_term.simulation) {
				if (parameter_config.short_term.items.simulation.items[key]) {
					state.short_term.simulation.push({
						label: parameter_config.short_term.items.simulation.items[key].description,
						value: data.short_term.simulation[key],
						key: key
					})
				}
			}
			for (const key in data.short_term.scuc) {
				if (parameter_config.short_term.items.scuc.items[key]) {
					state.short_term.scuc.push({
						label: parameter_config.short_term.items.scuc.items[key].description,
						value: data.short_term.scuc[key],
						key: key
					})
				}
			}
		}
		if (res2.code == 1) {
			state.common_info = res2.common_info
			state.log_value = getLogList(res2.sim_log, wsAcceptType.value.show_error_log, wsAcceptType.value.show_info_log, wsAcceptType.value.show_warning_log)
		}
	}).catch(() => {
		emits('hideLoading')
	})
}

onMounted(() => {
	emits('showLoading')
	getMidTermResult()
})
</script>
<style lang="scss" scoped>
.count_box {
  width: 100%;
  height: calc(100% - 15px);
  box-sizing: border-box;
  display: grid;
  grid-template-columns: 3fr 2fr 2fr 4fr;
  grid-column-gap: 15px;
  > div {
    height: 100%;
    // border: 1px solid #999;
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
    padding: 0px 35px 10px;
    overflow: auto;
    > p {
      text-align: center;
      line-height: 38px;
    }
  }
  .case_info_box {
    line-height: 50px;

  }
  .general_box {
    line-height: 50px;
  }
  .mid_term_box {
    line-height: 45px;
    p {
        text-align: center!important;
      }

  }
  .long_term_box {
    line-height: 45px;
    p {
        text-align: center!important;
      }
  }
  .short_term_box {
    line-height: 45px;
    p {
        text-align: center!important;
      }
  }
    .textarea{
      width: 100%;
      height: calc(100%);
      padding: 0!important;
      // font-size: 16px;
      // background-color: rgb(242, 242, 242);
      user-select: text;
      // background: #fff!important;
      .textarea_header{
        text-align: center;
        color: var(--base-color);
        position: relative;
        p{
          font-weight: bolder;
          font-size: 18px;
          letter-spacing: 2px;
          line-height: 40px;
        }
        >span{
          position: absolute;
          right: 10px;
          top: 8px;
          font-size: 24px;
          color: #ccc;
          &:hover{
            cursor: pointer;
            color: var(--base-color);
          }
        }
      }
      .textarea_content{
        height: calc(100% - 40px);
        overflow: auto;
        // background-color: #fff;
        padding: 0 0 5px 15px;
        // font-family: monospace;
        // font-family: 'SiYuanHWSC Regular';
        font-family: 'Roboto Mono', monospace;
        // overflow-x: hidden;
        font-size: 13px;
      }
    }

}
</style>
