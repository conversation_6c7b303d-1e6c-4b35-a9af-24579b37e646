<!-- 可能存在问题 -->
<template>
  <div class="cost_box">
    <div class="cost_box_header">
      <div class="title">
        {{ $t('成本统计表') }}
        <div v-if="!state.isAgTable" class="unit">
          <a-select
            ref="select"
            size="small"
            :bordered="false"
            v-model:value="state.unitValue"
            style="width: 120px"
            :options="state.unitOptions"
          ></a-select>
        </div>
      </div>
    </div>
    <div v-if="state.isAgTable" :class="['cost_box_content', state.separate_zone ? 'twoTr' : 'oneTr']">
      <div class="ag-box">
        <div class="unit">
          {{ $t('单位') }}：
          <a-select
            ref="select"
            size="small"
            :bordered="false"
            v-model:value="state.unitValue1"
            style="width: 120px"
            :options="state.unitOptions"
            @change="changeCostUnit"
          ></a-select>
        </div>
        <div class="ag-title">{{ $t('分区成本与惩罚') }}</div>
        <AgGrid ref="agGridRef" :isEdit="'costSheet'" :istree="true"></AgGrid>
      </div>
      <div class="ag-box" v-show="state.separate_zone">
        <div class="unit">
          {{ $t('单位') }}：
          <a-select
            ref="select"
            size="small"
            :bordered="false"
            v-model:value="state.unitValue2"
            style="width: 120px"
            :options="state.unitOptions"
            @change="changeLineUnit"
          ></a-select>
        </div>
        <div class="ag-title">{{ $t('联络线成本与惩罚') }}</div>
        <AgGrid ref="agGridLineRef" :isEdit="'costLineSheet'"></AgGrid>
      </div>
    </div>
    <div v-else :class="['cost_box_content', 'threeTr']">
      <div  class="cost_box_left">
        <div class="operation">
          <div class="operation-title">

            {{ $t('建设成本') }}
          </div>
          <div class="operation-text">
            <a-table
              :columns="state.columns"
              :data-source="state.construction_cost_data"
              :scroll="{x:500,y:180}"
               size="small"
               bordered
              :pagination="false"
            >
              <template #bodyCell="{ column,text}">
                <template v-if="column.dataIndex !== 'label'">
                 <div class="table-cell-text">{{ formatCloud(text) }}</div>
                </template>
              </template>
            </a-table>
          </div>
        </div>
        <div class="construction">
          <div class="operation-title">
            <!-- <span>（规划）</span> -->
            {{ $t('运行成本') }}
          </div>
          <div class="operation-text">
            <a-table
              :columns="state.columns"
              :data-source="state.operation_cost_data"
              :scroll="{x:500,y:150}"
              size="small"
              bordered
              :pagination="false"
            >
              <template #bodyCell="{ column,text}">
                <template v-if="column.dataIndex !== 'label'">
                 <div class="table-cell-text"> {{ formatCloud(text) }}</div>
                </template>
              </template>
            </a-table>
          </div>
        </div>
      </div>
      <div class="penalty">
        <div class="operation-title">
          <!-- <span>（规划）</span> -->
          {{ $t('惩罚') }}
        </div>
        <div class="penalty-text">
          <a-table
            :columns="state.columns"
            :data-source="state.penalty_data"
            :scroll="{x:500,y:380}"
            size="small"
            bordered
            :pagination="false"
          >
            <template #bodyCell="{ column,text}">
              <template v-if="column.dataIndex !== 'label'">
                <div class="table-cell-text"> {{ formatCloud(text) }}</div>
              </template>
            </template>
          </a-table>
        </div>
      </div>

      <div class="pie-box">
        <div>{{ $t('成本分析') }}</div>
        <div ref="pie1" class="pie" :style="{transform:`scale(${state.scale})`,zoom:`${state.zoom}`}"></div>
      </div>
    </div>

  </div>
</template>

<script setup>

import { t } from '@/utils/common'
import { ref, reactive, onMounted, onUnmounted, toRefs, markRaw, inject, computed, nextTick } from 'vue'
import { useRoute } from 'vue-router'

import { GetMidTermTaskResult } from '@/api/exampleApi'
import { getPieCost } from '@/utils/resultEchartsOptions.js'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const storeSetting = settingStore()
const { isChromeHigh } = storeToRefs(storeSetting)
const route = useRoute()
const emit = defineEmits(['hideLoading', 'showLoading'])

const agGridRef = ref(null)
const agGridLineRef = ref(null)

const echarts = inject('ec')

const state = reactive({
	isAgTable: true,
	separate_zone: true,
	costData: {},
	costLineData: {},
	construction_cost_data: [],
	operation_cost_data: [],
	penalty_data: [],
	construction_cost_sum: 0,
	operation_cost_sum: 0,
	penalty_sum: 0,
	scale: 1,
	zoom: 1,
	columns: [
		{
			title: t('成本类型'),
			dataIndex: 'label',
			key: 'label',
			width: 200
		},
		{
			title: t('全省'),
			dataIndex: 'value',
			key: 'value',
			width: 200,
			sorter: (a, b) => a.value - b.value
		}
	],
	unitValue: 10000,
	unitValue1: 10000,
	unitValue2: 10000,
	unitOptions: [
		{
			label: t('元'),
			value: 1
		},
		{
			label: t('万元'),
			value: 10000
		},
		{
			label: t('亿元'),
			value: 100000000
		}
	]
})

const allEcharts = reactive({
	pieChart1: undefined,
	pie1: undefined
})

const { pie1 } = toRefs(allEcharts)

const initEchartsPie = (data1) => {
	allEcharts.pieChart1.Chart_level = 1
	const option1 = getPieCost(data1, t('元'), allEcharts.pieChart1, 1, false, '')
	allEcharts.pieChart1.hideLoading()
	allEcharts.pieChart1.setOption(option1, true)
}

const getMidTermResult = () => {
	GetMidTermTaskResult({
		'group': '_result.cost_and_penalty',
		'result_file_path': route.query.filePath
	}).then((res) => {
		emit('hideLoading')
		if (res.code == 1) {
			const { columns, construction_cost_data, operation_cost_data, penalty_data } = res
			state.separate_zone = res.separate_zone
			if (res.cost_and_penalty_table) {
				state.isAgTable = true
				const tableData = {
					columns: res.cost_and_penalty_table.columns,
					data: res.cost_and_penalty_table.data
				}
				const lineTableData = {
					columns: res.lines_cost_and_penalty_table.columns,
					data: res.lines_cost_and_penalty_table.data
				}

				lineTableData.columns.forEach((item, index) => {
					if (item.field == '类型' || item.field == '类别') {
						item.width = 110
					} else {
						item.width = 132
					}
				})

				state.costData = tableData
				state.costLineData = lineTableData

				changeCostUnit(state.unitValue1)

				if (res.separate_zone) {
					changeLineUnit(state.unitValue2)
				}
			} else {
				state.isAgTable = false

				const operationTotal = {}
				columns.forEach(item => {
					if (item.key == 'label') {
						item.width = 180
					} else {
						item.width = 120
					}
					operationTotal[item.dataIndex] = item.key == 'label' ? t('总计') : operation_cost_data.map(val => val[item.dataIndex]).reduce((acc, cur) => acc + cur, 0)
				})
				const constructionTotal = {}
				columns.forEach(item => {
					constructionTotal[item.dataIndex] = item.key == 'label' ? t('总计') : construction_cost_data.map(val => val[item.dataIndex]).reduce((acc, cur) => acc + cur, 0)
				})
				const penaltyTotal = {}
				columns.forEach(item => {
					penaltyTotal[item.dataIndex] = item.key == 'label' ? t('总计') : penalty_data.map(val => val[item.dataIndex]).reduce((acc, cur) => acc + cur, 0)
				})
				state.columns = columns
				state.columns.forEach(item => item.ellipsis = true)

				state.operation_cost_data = operation_cost_data
				state.construction_cost_data = construction_cost_data
				state.penalty_data = penalty_data
				state.operation_cost_data.push(operationTotal)
				state.construction_cost_data.push(constructionTotal)
				state.penalty_data.push(penaltyTotal)

				state.operation_cost_sum = operation_cost_data.map(item => item['全系统']).reduce((acc, cur) => acc + cur, 0)
				state.construction_cost_sum = construction_cost_data.map(item => item['全系统']).reduce((acc, cur) => acc + cur, 0)
				state.penalty_sum = penalty_data.map(item => item['全系统']).reduce((acc, cur) => acc + cur, 0)
				const data = [
					{
						'name': t('建设成本'),
						'value': state.construction_cost_sum,
						'itemStyle': { color: '#5ea1de' }

					},
					{
						'name': t('运行成本'),
						'value': state.operation_cost_sum,
						'itemStyle': { color: '#78c7c1' }
					},
					{
						'name': t('惩罚'),
						'value': state.penalty_sum,
						'itemStyle': { color: '#edbd78' }
					}
				]
				nextTick(() => {
					allEcharts.pieChart1 = markRaw(echarts.init(allEcharts.pie1))
					initEchartsPie(data)
				})
			}
		}
	}).catch(() => {
		emit('hideLoading')
	})
}

const changeCostUnit = (val) => {
	const tableData = JSON.parse(JSON.stringify(state.costData))
	tableData.data.forEach(element => {
		for (const key in element) {
			if (key !== 'index' && element[key] !== '') {
				if (!isNaN(element[key])) {
					element[key] = parseFloat(element[key] / val).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
				}
			}
		}
	})
	nextTick(() => {
		agGridRef.value.setBlanceData(tableData)
	})
}

const changeLineUnit = (val) => {
	const lineTableData = JSON.parse(JSON.stringify(state.costLineData))
	lineTableData.data.forEach(element => {
		for (const key in element) {
			if (key !== 'index' && element[key] !== '' && element[key] !== null) {
				if (!isNaN(element[key])) {
					element[key] = parseFloat(element[key] / val).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
				}
			}
		}
	})
	nextTick(() => {
		agGridLineRef.value.setBlanceData(lineTableData)
	})
}

const formatCloud = computed(() => {
	return function(index) {
		if (index == null) {
			return '-'
		} else {
			return parseFloat(index / state.unitValue).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
		}
	}
})

const screenScale = (val) => {
	let root
	if (isChromeHigh.value) {
		root = document.getElementsByClassName('home-body')[0].style.zoom || 1
	} else {
		root = document.body.style.zoom
	}
	state.zoom = 1 / root
	state.scale = root
	if (val != 1) {
		return
	}
}

onUnmounted(() => {
	window.removeEventListener('resize', screenScale)
})

window.addEventListener('resize', screenScale)

onMounted(() => {
	emit('showLoading')
	screenScale(1)
	getMidTermResult()
})
</script>
<style lang="scss" scoped>
.cost_box {
  width: 100%;
  height: calc(100% - 15px);
  box-sizing: border-box;
  overflow: auto;
  user-select: text;
  // background-color: #91939e;
  .cost_box_header {
    position: relative;
    width: 100%;
    height: 65px;
    text-align: center;
    // background-color: rgb(255, 127, 240);
    .title {
      position: relative;
      width: 800px;
      height: 48px;
      margin: 0 auto;
      border: 1px solid #91939e;
      border-radius: 4px;
      line-height: 48px;
      font-size: 16px;
      font-weight: 500;
      .unit {
        position: absolute;
        right: 10px;
        top: 0px;
        font-size: 12px;
        font-weight: 400;
        color: #91939e;
      }
    }
  }

  .cost_box_content {
    width: 100%;
    height: calc(100% - 65px);
    text-align: center;
    grid-column-gap: 15px;
    // background-color: #49699c;
    box-sizing: border-box;
    >div {
      // background-color: aquamarine;
      box-sizing: border-box;
    }
    .cost_box_left {
      height: calc(100%);
      // background-color: #91939e;
    }
    .operation {
      height: 50%;
      background-image: url('@/assets/bg-img/constructionCost.jpg');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      padding: 0px 25px;
      // background-color: aqua;
      box-sizing: border-box;
    }
    .construction {
      // height: 300px;
      height: 50%;
      background-image: url('@/assets/bg-img/operatingCost.jpg');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      padding: 5px 25px;
      // background-color: aqua;
    }
    .penalty {
      height: calc(100%);
      // display: flex;
      // flex-grow: 1;
      background-image: url('@/assets/bg-img/penaltyCost.jpg');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      padding: 5px 25px;
      // background-color: aqua;
      >div:first-child {
        color: #49699c;
        font-weight: 500;
      }
    }
    .pie-box {
      height: calc(100%);
      padding: 15px;
      overflow: hidden;
      font-weight: 700;
      // background-color: blueviolet;
    }
    .operation-title {
      width: 100%;
      height: 30px;
      line-height: 30px;
      font-size: 16px;
      font-weight: 500;
      color: #49699c ;
      >span {
        color: #91939e;
      }
    }
    .operation-text {
      width: 680px;
      padding:0 15px;
      text-align: left;
      >div:first-child {
        color: #49699c;
        font-weight: 500;
      }
    }
    .penalty-text {
      width: 680px;
      padding: 10px 15px;
      text-align: left;
      >div:first-child {
        color: #49699c;
        font-weight: 500;
      }
    }
    .operation-line {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      grid-column-gap: 15px;
      margin: 10px 45px;
      text-align: left;
      font-size: 14px;
      color: #91939e;
      >div {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    .pie {
      width: 100%;
      height: 100%;
      transform-origin: 0 0;
    }
  }

  .oneTr {
    display: grid;
    // grid-template-columns: 1fr;
    width: 80%;
    padding: 0 25px;
    margin: 0 auto;
  }

  .twoTr {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 25px;
  }
  .threeTr {
    display: grid;
    grid-template-columns: 2fr 2fr 1fr;
  }

  .ag-box {
    position: relative;
    .ag-title {
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #49699c;
      font-weight: 500;
    }
    .unit {
      position: absolute;
      top: 10px;
      left: 0;
      z-index: 30;
      // transform: translate(-50%, -50%);

      font-weight: 400;
      color: #91939e;
    }
  }

  .table-cell-text {
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .table-footer-text {
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  // 修改antv组件样式
  :deep(.ant-table) {
    color: #6b6d76;
    background-color: transparent;
    // min-height: 280px;
    font-weight: normal;
    font-size: 13px;
    .ant-table-thead > tr > th {
      color: #5b5c62;
      background-color: transparent;
      font-size: 13px;
      // padding: 3px 8px;
      // overflow-wrap: break-word;
      // border: 0;
    }
  }
}
</style>
