<!-- eslint-disable no-unused-vars -->
<template>
  <a-modal
    wrapClassName="statistic_modal"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.ifShow"
    :footer="null"
    :closable="false"
    :maskClosable="false"
  >
    <div class="user-select" :style="{zoom: state.zoom}">
      <div class="modal_top">
          <p>{{ props.type == 'zhuangjiStatistic' ? $t('装机统计') : $t('检修容量统计') }}</p>
          <close-outlined class="pointer" @click="emit('close')" />
      </div>
      <div class="modal_content">
        <div class="select_partition">
          <a-select
            ref="select"
            v-model:value="state.partitionValue"
            style="width: 150px"
            :options="state.options"
            @change="handleChange"
          ></a-select>
          <a-select
            v-if="props.type == 'capacityStatistic'"
            ref="select"
            v-model:value="state.scenario"
            style="width: 150px;margin-left: 10px"
            :options="state.scenarioOptions"
            @change="handleConfirm"
          ></a-select>

            <a-radio-group v-if="props.type == 'zhuangjiStatistic'" v-model:value="state.dateType" button-style="solid" style="margin-left: 10px">
              <a-radio-button value="range">{{ $t('范围选择') }}</a-radio-button>
              <a-radio-button value="custom">{{ $t('自定义') }}</a-radio-button>
            </a-radio-group>
            <div v-if="props.type == 'zhuangjiStatistic'">
              <div v-if="state.dateType == 'custom'" class="customBox">
                <a-date-picker v-model:value="state.customDate" ref="customDateRef" :open="state.isOpen" @focus="state.isOpen = true"  @blur="state.isOpen = false" @change="customDateChange" valueFormat="YYYY-MM-DD" style="width: 150px; margin-left: 10px"/>
                <div class="dateTag" @click="dateTagClick()">
                  <template  v-for="(tag) in state.tags" :key="tag">
                    <a-tag closable @close="handleClose(tag)">
                      {{ tag }}
                    </a-tag>
                  </template>
                </div>
              </div>
              <div v-else>
                <a-range-picker v-model:value="state.rangeDate" valueFormat="YYYY" picker="year"  style="width: 180px; margin-left: 10px"/>
                <a-cascader
                  v-model:value="state.rangeDay"
                    style="width: 150px; margin-left: 10px"
                  :options="state.dayOptions"
                  :placeholder=" $t('请选择日期')"
                >
                  <template #suffixIcon><CalendarOutlined /></template>
                </a-cascader>
              </div>
            </div>

          <a-button v-if="props.type == 'zhuangjiStatistic'" type="primary" style="margin-left: 10px" @click="handleConfirm">{{ $t('确认') }}</a-button>
          <a-radio-group v-model:value="state.statisticType" button-style="solid" v-if="props.type == 'capacityStatistic'" style="margin-left: 10px" @change="handleDisplayWay">
            <a-radio-button value="chart">{{ $t('统计图') }}</a-radio-button>
            <a-radio-button value="table">{{ $t('统计表') }}</a-radio-button>
          </a-radio-group>
          <span class="unit">{{ $t('单位') }}：{{ $t('万千瓦') }}</span>
          <DownloadOutlined @click="downLoadClick"  v-if="props.type == 'capacityStatistic' && state.statisticType == 'table'" style="margin-left: 20px"/>
        </div>
        <a-spin :spinning="state.spinning" size="large">
        <div class="agClass">
          <div v-show="props.type == 'capacityStatistic' && state.statisticType == 'chart'" class="line" ref="line" :style="`zoom:${state.zooms};transform:scale(${state.scales});transform-origin:0 0;height:${state.height}px;`"></div>
          <AgGrid v-if="props.type == 'zhuangjiStatistic' || state.statisticType == 'table'" ref="agGridRef" :isEdit="'balanceSheet'"></AgGrid>
        </div>
        </a-spin>
      </div>
      <div class="modal_btn">
        <!-- <a-button v-show="state.type==1" @click="confirm(1)" type="primary">{{props.treeValue.includes('stogen')?'关联时序':'确定'}}</a-button>
        <a-button v-show="state.type==2" @click="confirm(2)" type="primary">{{props.treeValue=='stogen.pump'?'关联水库':'关联储能'}}</a-button>
        <a-button @click="emit('close')">取消</a-button> -->
      </div>
    </div>
  </a-modal>
</template>
<script setup>

import { t } from '@/utils/common'
import { onMounted, reactive, ref, onUnmounted, nextTick, inject, defineProps, markRaw } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { getStatisticSeries } from '@/utils/teap'
import { getBaseDataApi, globalParameters } from '@/api/exampleApi'
import { useRoute } from 'vue-router'

import { getDayOption } from '@/utils/common'
import { debounce } from '@/utils/gis'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const storeSetting = settingStore()
const { isChromeHigh } = storeToRefs(storeSetting)
const route = useRoute()

const emit = defineEmits(['close', 'refresh'])

const props = defineProps({
	type: {
		type: String,
		default: ''
	}
})

const agGridRef = ref(null)
const customDateRef = ref(null)
const line = ref()
const lineChart = ref()
const echarts = inject('ec')

const state = reactive({
	ifShow: true,
	spinning: false,
	statisticType: 'chart',
	partitionValue: '',
	options: [],
	columns: [],
	tableData: {},
	dateType: 'range',
	rangeDate: [],
	rangeDay: ['12', '12-31'],
	dayOptions: [],
	tags: [],
	isOpen: false,
	scenarioOptions: [],
	scenario: t('（空）'),
	type: 1,
	scales: 1,
	zoom: 1,
	zooms: 1,
	height: 380

})

const downLoadClick = () => {
	if (state.statisticType == 'chart') return
	nextTick(() => {
		agGridRef.value.onBtExport(t('检修容量统计') + `.xlsx`)
	})
}

const initLine = (data, columns) => {
	const option = getStatisticSeries(data, columns)
	lineChart.value.setOption(option)
}

const handleChange = val => {
	if (props.type == 'capacityStatistic' && state.statisticType == 'chart') {
		const tempObj = {}
		state.tableData[val].data.forEach(item => {
			for (const key in item) {
				if (key != 'index') {
					tempObj[key] = state.tableData[val].data.map(item1 => item1[key])
				}
			}
		})
		initLine(tempObj, state.tableData[val].columns)
	} else {
		nextTick(() => {
			agGridRef.value.setTimeDeatilData(state.tableData[val])
		})
	}
}

const dateTagClick = (val) => {
	customDateRef.value.focus()
	state.isOpen = true
}
const customDateChange = (val) => {
	if (state.tags.includes(val)) return
	state.tags.push(val)
}

const handleClose = removedTag => {
	const tags = state.tags.filter(tag => tag !== removedTag)
	state.tags = tags
}

const closeModal = () => {
	emit('close')
}

const handleConfirm = () => {
	let tempQuery
	if (props.type == 'zhuangjiStatistic') {
		let cal_time_list = []
		if (state.dateType == 'range') {
			const years = []
			for (let i = state.rangeDate[0]; i <= state.rangeDate[1]; i++) {
				years.push(i)
			}
			cal_time_list = years.map(item => item = item + '-' + state.rangeDay[1] + ' 00:00:00')
		} else {
			cal_time_list = state.tags.map(item => item + ' 00:00:00')
		}
		tempQuery = {
			'import_string_func': 'teapcase:gen_capacity_info',
			'func_arg_dict': {
				'file_name': route.query.filePath,
				'cal_time_list': cal_time_list
			}
		}
	} else {
		tempQuery = {
			'import_string_func': 'teapcase:gen_maintenance_capacity_info',
			'func_arg_dict': {
				'file_name': route.query.filePath,
				'scenario': state.scenario
			}
		}
	}

	getInitData(tempQuery)
}
const getInitData = (tempQuery) => {
	getBaseDataApi(tempQuery).then(res => {
		const { func_result } = res
		state.spinning = false
		state.options = Object.keys(func_result).map(item => {
			return {
				value: item,
				label: item == 'all' ? t('全系统') : item
			}
		})

		if (!state.partitionValue) {
			state.partitionValue = 'all'
		}
		state.tableData = func_result

		for (const key in state.tableData) {
			state.tableData[key].columns.forEach((item, index) => {
				item.width = 110
			})
		}
		if (props.type == 'capacityStatistic' && state.statisticType == 'chart') {
			const tempObj = {}
			state.tableData[state.partitionValue].data.forEach(item => {
				for (const key in item) {
					if (key != 'index') {
						tempObj[key] = state.tableData[state.partitionValue].data.map(item1 => item1[key])
					}
				}
			})
			initLine(tempObj, state.tableData[state.partitionValue].columns)
		} else {
			nextTick(() => {
				agGridRef.value.setTimeDeatilData(state.tableData[state.partitionValue])
			})
		}
	}).catch(() => {
		state.spinning = false
	})
}

const handleDisplayWay = e => {
	if (e.target.value == 'table') {
		nextTick(() => {
			agGridRef.value.setTimeDeatilData(state.tableData[state.partitionValue])
		})
	}
}

const getGlobalParameters = () => {
	globalParameters(
		{
			'import_string_func': 'teapcase:list_all_scenario',
			'func_arg_dict': {
				'file_name': route.query.filePath
			}
		}
	).then(res => {
		if (res.code == 1) {
			const { scenario } = res.func_result
			state.scenarioOptions = scenario.data.map(item => {
				return {
					value: item,
					label: item
				}
			})
		}
	})
}
const screenScale = () => {
	let root
	if (isChromeHigh.value) {
		root = document.getElementsByClassName('home-body')[0].style.zoom || 1
		state.zoom = root
	} else {
		root = document.body.style.zoom
	}
	state.zooms = 1 / root
	state.scales = root
	state.height = 380 * root
}
const debouncedScreenScale = debounce(screenScale, 200)
onUnmounted(() => {
	window.removeEventListener('resize', debouncedScreenScale)
})
onMounted(async() => {
	state.spinning = true
	state.dayOptions = getDayOption()
	screenScale()
	window.addEventListener('resize', debouncedScreenScale)
	lineChart.value = markRaw(echarts.init(line.value))

	globalParameters(
		{
			'import_string_func': 'teapcase:read_from_tc',
			'func_arg_dict': {
				'file_name': route.query.filePath,
				'sheet_name': 'parameter'
			}
		}
	).then(res => {
		const { parameter } = res.func_result
		state.rangeDate[0] = parameter.data.case_info.start_datetime.split('-')[0]
		state.rangeDate[1] = parameter.data.case_info.end_datetime.split('-')[0]
		handleConfirm()
	})
	if (props.type == 'zhuangjiStatistic') return
	getGlobalParameters()
})
</script>
<style lang="scss">
  .statistic_modal{
    .ant-modal{
      width: auto!important;
      .ant-modal-body{
        >div{
            .modal_content{
                // display: flex;
                width: 1300px;
                padding: 20px 20px 30px;

                .select_partition {
                  position: absolute;
                  top: 70px;
                  left: 20px;
                  z-index: 30;
                  display: flex;
                  justify-content: left;
                  // > div {
                  //   width: 400px;
                  // }
                  .customBox {
                    width: 350px;
                    position: relative;
                    display: flex;
                    justify-content: left;
                    // line-height: 30px;
                    .dateTag {
                      position: absolute;
                      top: 0;
                      left: 0;
                      width: 280px;
                      height: 32px;
                      margin-left: 10px;

                      display: flex;
                      flex-wrap: nowrap;
                      align-items: center;
                      padding: 1px 4px;

                      border: 1px solid #d9d9d9;
                      border-radius: 6px;

                      // align-items: center;
                      background: #ffffff;
                      // border: 1px solid #d9d9d9;
                      // border-radius: 6px;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      // flex-wrap: nowrap | wrap | wrap-reverse;
                    }
                  }
                  .unit {
                    display: inline-block;
                    line-height: 32px;
                    margin-left: 20px;
                  }
                }
                .line{
                    transform-origin: 0 0;
                    margin-top: 45px;
                }
                .agClass {
                  width: 100%;
                  height: 380px;
                }
            }
        }
      }
    }
  }
</style>

