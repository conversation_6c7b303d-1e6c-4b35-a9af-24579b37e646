<!-- eslint-disable no-unused-vars -->
<template>
	<a-modal wrapClassName="time_match" :afterClose="closeModal" :centered="true" v-model:open="state.ifShow"
		:footer="null" :closable="false" :maskClosable="false">
		<screen-scale>
			<div class="modal_top">
				<p>{{ $t('Smart Association') }}</p>
				<close-outlined class="pointer" @click="emit('close')" />
			</div>
			<div class="modal_content">
				<div>
					<div class="select_input">
						<a-input v-model:value="state.search" :placeholder="$t('Search Device')"
							style="width: 188px; margin-right: 30px;">
							<template #prefix>
								<SearchOutlined />
							</template>
						</a-input>
						<a-radio-group v-model:value="state.name_maybe_only_flag" @change="changeNameMaybeOnlyFlag">
							<a-radio :value="false">{{ $t('Match by name and notes') }}</a-radio>
							<a-radio :value="true">{{ $t('Match by notes') }}</a-radio>
						</a-radio-group>

					</div>
					<div v-show="state.type == 1">
						<a-table :columns="columns1" :scroll="{ x: 100, y: 520 }" ref="scroll_table1" rowKey="index"
							:pagination="false" :data-source="state.filterTable1" :customRow="customRow"
							:rowClassName="rowClassName1" :loading="state.tableLoading1"
							@resizeColumn="handleResizeColumn" />
					</div>
					<div v-show="state.type == 2">
						<a-table :columns="columns2" :scroll="{ x: 100, y: 520 }" ref="scroll_table2" rowKey="index"
							:pagination="false" :data-source="state.filterTable2" :customRow="customRow"
							:rowClassName="rowClassName2" :loading="state.tableLoading2"
							@resizeColumn="handleResizeColumn" />
					</div>
				</div>
				<div>
					<div class="select_top">
						<div class="select_input">
							<a-input v-model:value="state.searchCurve" :placeholder="$t('Search Curve')">
								<template #prefix>
									<SearchOutlined />
								</template>
							</a-input>
						</div>
						<div v-if="props.treeValue.includes('stogen')">
							<div :class="state.type == 1 ? 'active' : ''" @click="changeType(1)">{{ $t('时序') }}</div>
							<div :class="state.type == 2 ? 'active' : ''" @click="changeType(2)">{{ props.treeValue ==
								'stogen.pump' ?
								$t('水库') : $t('储能') }}</div>
						</div>
					</div>
					<div v-show="state.type == 1">
						<a-table :columns="columns3" :scroll="{ y: 520 }" @change="handleChange1"
							:pagination="(state.showAll1 && state.selectId1 != undefined) ? pageSizeOptions1 : false"
							:data-source="state.tableData1" :loading="state.loading1" rowKey="loop_index"
							:row-selection="rowSelection1" @resizeColumn="handleResizeColumn1">
							<template
								#customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
								<div style="padding: 8px">
									<a-input ref="searchInput1" :placeholder="$t('Search Curve')"
										:value="selectedKeys[0]"
										style="width: 188px; margin-bottom: 8px; display: block"
										@change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
										@pressEnter="handleSearch1(selectedKeys, confirm, column.dataIndex)" />
									<a-button type="primary" size="small" style="width: 90px; margin-right: 8px"
										@click="handleSearch1(selectedKeys, confirm, column.dataIndex)">
										<template v-if="column.dataIndex == 'name'" #icon>
											<SearchOutlined />
										</template>
										{{ $t('搜索') }}
									</a-button>
									<a-button size="small" style="width: 90px" @click="handleReset1(clearFilters)">
										{{ $t('重置') }}
									</a-button>
								</div>
							</template>
							<template #customFilterIcon="{ filtered, column }">
								<search-outlined v-if="column.dataIndex == 'name'"
									:style="{ color: filtered ? '#108ee9' : undefined }" />
								<FilterFilled v-else :style="{ color: filtered ? '#108ee9' : undefined }" />
							</template>
							<template #bodyCell="{ column, record, text }">
								<template v-if="column.key === 'type'">
									{{ state.ts_type_name_map[record.type] }}
								</template>
								<template v-if="['option'].includes(column.dataIndex)">
									<div class="line_btn" @click="openLine(record.name, record.index)">{{ $t('Curve') }}
									</div>
								</template>
								<span v-if="state.searchText1 && state.searchedColumn1 === column.dataIndex">
									<template v-for="(fragment, i) in text
										.toString()
										.split(new RegExp(`(?<=${state.searchText1})|(?=${state.searchText1})`, 'i'))">
										<mark v-if="fragment.toLowerCase() === state.searchText1.toLowerCase()" :key="i"
											class="highlight">
											{{ fragment }}
										</mark>
										<template v-else>{{ fragment }}</template>
									</template>
								</span>
							</template>
						</a-table>
						<div class="show_all" v-if="state.selectId1 != undefined && !state.showAll1">
							<p @click="showAllTable(1)">
								<CaretDownOutlined />{{ $t('显示所有') }}
							</p>
						</div>
					</div>
					<div v-show="state.type == 2">
						<a-table :columns="columns4" :scroll="{ y: 520 }" @change="handleChange2"
							:pagination="(state.showAll2 && state.selectId2 != undefined) ? pageSizeOptions2 : false"
							:data-source="state.tableData2" :loading="state.loading2" rowKey="loop_index"
							:row-selection="rowSelection2" @resizeColumn="handleResizeColumn1">
							<template
								#customFilterDropdown="{ setSelectedKeys, selectedKeys, confirm, clearFilters, column }">
								<div style="padding: 8px">
									<a-input ref="searchInput2" :placeholder="$t('Search Curve')"
										:value="selectedKeys[0]"
										style="width: 188px; margin-bottom: 8px; display: block"
										@change="e => setSelectedKeys(e.target.value ? [e.target.value] : [])"
										@pressEnter="handleSearch2(selectedKeys, confirm, column.dataIndex)" />
									<a-button type="primary" size="small" style="width: 90px; margin-right: 8px"
										@click="handleSearch2(selectedKeys, confirm, column.dataIndex)">
										<template v-if="column.dataIndex == 'name'" #icon>
											<SearchOutlined />
										</template>
										{{ $t('搜索') }}
									</a-button>
									<a-button size="small" style="width: 90px" @click="handleReset2(clearFilters)">
										{{ $t('重置') }}
									</a-button>
								</div>
							</template>
							<template #customFilterIcon="{ filtered, column }">
								<search-outlined v-if="column.dataIndex == 'name'"
									:style="{ color: filtered ? '#108ee9' : undefined }" />
								<FilterFilled v-else :style="{ color: filtered ? '#108ee9' : undefined }" />
							</template>
							<template #bodyCell="{ column, record, text }">
								<template v-if="column.key === 'type'">
									{{ state.ts_type_name_map[record.type] }}
								</template>
								<template v-if="['option'].includes(column.dataIndex)">
									<div class="line_btn" @click="openLine(record.name, record.index)">{{ $t('Curve') }}
									</div>
								</template>
								<span v-if="state.searchText2 && state.searchedColumn2 === column.dataIndex">
									<template v-for="(fragment, i) in text
										.toString()
										.split(new RegExp(`(?<=${state.searchText2})|(?=${state.searchText2})`, 'i'))">
										<mark v-if="fragment.toLowerCase() === state.searchText2.toLowerCase()" :key="i"
											class="highlight">
											{{ fragment }}
										</mark>
										<template v-else>{{ fragment }}</template>
									</template>
								</span>
							</template>
						</a-table>
						<div class="show_all" v-if="state.selectId2 != undefined && !state.showAll2">
							<p @click="showAllTable(2)">
								<CaretDownOutlined />{{ $t('显示所有') }}
							</p>
						</div>
					</div>
				</div>
			</div>
			<div class="modal_btn">
				<a-button v-show="state.type == 1" @click="confirm(1)" type="primary">{{
					props.treeValue.includes('stogen') ?
						$t('关联时序') : $t('确认') }}</a-button>
				<a-button v-show="state.type == 2" @click="confirm(2)" type="primary">{{ props.treeValue ==
					'stogen.pump'
					? $t('关联水库')
					: $t('关联储能') }}</a-button>
				<a-button @click="emit('close')">{{ $t('取消') }}</a-button>
			</div>
			<line-chart :index="state.index" :name="state.name" v-if="state.lineShow"
				@close="state.lineShow = false"></line-chart>
		</screen-scale>
	</a-modal>
</template>
<script setup>

import { t } from '@/utils/common'
import { onMounted, reactive, ref, watch, nextTick, computed, unref } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { getBaseDataApi } from '@/api/exampleApi'
import { useRoute } from 'vue-router'
import message from '@/utils/message'
import { Table } from 'ant-design-vue'
const route = useRoute()
const state = reactive({
	ifShow: true,
	name_maybe_only_flag: false,
	search: undefined,
	searchCurve: undefined,
	selectId1: undefined,
	selectId2: undefined,
	showAll1: false,
	showAll2: false,
	type: 1,
	tableData1: [],
	tableData2: [],
	tableCurveData1: [],
	tableCurveData2: [],
	baseTableData1: [],
	baseTableData2: [],
	filterTable1: [],
	filterTable2: [],
	tableLoading1: false,
	tableLoading2: false,
	loading1: false,
	loading2: false,
	index: undefined,
	name: undefined,
	searchText1: '',
	searchText2: '',
	searchedColumn1: '',
	searchedColumn2: '',
	lockIndex1: [],
	lockIndex2: [],
	filters1: [],
	filters2: [],
	filters3: [],
	lineShow: false,
	selectTableData1: {},
	selectTableData2: {},
	recordRow: {},
	recordIndex: undefined,
	ts_type_name_map: {}
})
const pageSizeOptions1 = ref({
	pageSize: 14,
	showSizeChanger: false,
	current: 1
})
const pageSizeOptions2 = ref({
	pageSize: 14,
	showSizeChanger: false,
	current: 1
})
const props = defineProps({
	data: {
		type: Array,
		default: () => []
	},
	treeValue: {
		type: String,
		default: ''
	}
})
const scroll_table1 = ref()
const scroll_table2 = ref()
const emit = defineEmits(['close', 'refresh'])
const filteredInfo1 = ref([])
const filteredInfo2 = ref([])
const columns1 = ref()
const columns2 = ref()
const searchInput1 = ref()
const searchInput2 = ref()

const columns3 = ref([
	{
		title: t('曲线名称'),
		dataIndex: 'name',
		key: 'name',
		width: 200,
		ellipsis: true,
		resizable: true,
		align: 'center',
		customFilterDropdown: true,
		onFilter: (value, record) => record.name.toString().toLowerCase().includes(value.toLowerCase()),
		onFilterDropdownOpenChange: visible => {
			if (visible) {
				setTimeout(() => {
					searchInput1.value.focus()
				}, 100)
			}
		}
	},
	{
		title: t('曲线类型'),
		dataIndex: 'type',
		key: 'type',
		width: 100,
		ellipsis: true,
		resizable: true,
		align: 'center',
		filters: [],
		onFilter: (value, record) => record.type == (value)
	},
	{
		title: t('计算场景'),
		dataIndex: 'scenario',
		key: 'scenario',
		width: 100,
		ellipsis: true,
		resizable: true,
		align: 'center',
		filters: [],
		onFilter: (value, record) => record.scenario == (value)
	},
	{
		title: t('最大值'),
		dataIndex: 'max_value',
		key: 'max_value',
		width: 90,
		ellipsis: true,
		resizable: true,
		align: 'center'
	},
	{
		title: t('最小值'),
		dataIndex: 'min_value',
		key: 'min_value',
		width: 90,
		ellipsis: true,
		resizable: true,
		align: 'center'
	},
	{
		title: t('总和'),
		dataIndex: 'sum_value',
		key: 'sum_value',
		width: 90,
		ellipsis: true,
		resizable: true,
		align: 'center'
	},
	{
		title: t('操作'),
		dataIndex: 'option',
		key: 'option',
		width: 80,

		align: 'center'
	}
])
const columns4 = ref([
	{
		title: t('名称'),
		dataIndex: 'name',
		key: 'name',
		width: 200,
		ellipsis: true,
		resizable: true,
		align: 'center',
		customFilterDropdown: true,
		onFilter: (value, record) => record.name.toString().toLowerCase().includes(value.toLowerCase()),
		onFilterDropdownOpenChange: visible => {
			if (visible) {
				setTimeout(() => {
					searchInput2.value.focus()
				}, 100)
			}
		}
	},
	{
		title: t('类型'),
		dataIndex: 'type',
		key: 'type',
		width: 100,
		resizable: true,
		ellipsis: true,
		align: 'center',
		filters: [],
		onFilter: (value, record) => record.type == (value)
	},
	{
		title: t('存储电量上限'),
		dataIndex: 'max_e_mwh',
		key: 'max_e_mwh',
		width: 90,
		ellipsis: true,
		resizable: true,
		align: 'center'
	},
	{
		title: t('存储电量下限'),
		dataIndex: 'min_e_mwh',
		key: 'min_e_mwh',
		width: 90,
		ellipsis: true,
		resizable: true,
		align: 'center'
	}

])

const selectedRowKeys1 = ref([])
const selectedRowKeys2 = ref([])
const rowSelection1 = computed(() => {
	return {
		selectedRowKeys: unref(selectedRowKeys1),
		onChange: onSelectChange1,
		getCheckboxProps: record => ({
			disabled: state.lockIndex1.includes(record.loop_index)
		}),
		columnWidth: 36,
		hideDefaultSelections: true,
		selections: [
			Table.SELECTION_ALL,
			Table.SELECTION_INVERT,
			Table.SELECTION_NONE
		]
	}
})
const rowSelection2 = computed(() => {
	return {
		selectedRowKeys: unref(selectedRowKeys2),
		onChange: onSelectChange2,
		getCheckboxProps: record => ({
			disabled: state.lockIndex2.includes(record.loop_index)
		}),
		columnWidth: 36,
		hideDefaultSelections: true,
		selections: [
			Table.SELECTION_ALL,
			Table.SELECTION_INVERT,
			Table.SELECTION_NONE
		]
	}
})

const handleResizeColumn = (w, col) => {
	col.width = w
}

const handleResizeColumn1 = (w, col) => {
	col.width = w
}

const changeType = (val) => {
	if (val == state.type) return
	state.type = val
}
const handleSearch1 = (selectedKeys, confirm, dataIndex) => {
	confirm()
	state.searchText1 = selectedKeys[0]
	state.searchedColumn1 = dataIndex
}
const handleSearch2 = (selectedKeys, confirm, dataIndex) => {
	confirm()
	state.searchText2 = selectedKeys[0]
	state.searchedColumn2 = dataIndex
}
const handleReset1 = clearFilters => {
	clearFilters({ confirm: true })
	state.searchText1 = ''
}
const handleReset2 = clearFilters => {
	clearFilters({ confirm: true })
	state.searchText2 = ''
}
const handleChange1 = (pagination, filters, sorter) => {
	filteredInfo1.value = filters
	pageSizeOptions1.value = pagination
}
const handleChange2 = (pagination, filters, sorter) => {
	filteredInfo2.value = filters
	pageSizeOptions2.value = pagination
}
const openLine = (name, index) => {
	state.index = index
	state.name = name
	state.lineShow = true
}
const showAllTable = async (val) => {
	state['showAll' + val] = true
	state['loading' + val] = true
	state['tableData' + val] = []
	const regex = new RegExp(state.searchCurve, 'i')
	let data
	try {
		if (val == 2) {
			data = await getBaseDataApi({
				'import_string_func': 'teapcase:match_element_reference_in_tc',
				'func_arg_dict': {
					'file_name': route.query.filePath,
					'sheet_name': props.treeValue,
					'row_id_list': [state.selectId2],
					'ref_table': props.treeValue.includes('_plan') ? 'storage_plan' : 'storage',
					'threshold': 0,
					'name_maybe_only_flag': state.name_maybe_only_flag
				}
			})
		} else {
			data = await getBaseDataApi({
				'import_string_func': 'teapcase:match_element_timeseries_in_tc',
				'func_arg_dict': {
					'file_name': route.query.filePath,
					'sheet_name': props.treeValue,
					'row_id_list': [state.selectId1],
					'similarity': true,
					'threshold': 0,
					'name_maybe_only_flag': state.name_maybe_only_flag
				}
			})
		}
	} catch (error) {
		state['loading' + val] = false
	}
	if (data.code == 1 && data.func_result.code == 1) {
		if (val == 2) {
			state.filters3 = data.func_result.all_type.map(item => {
				return {
					text: item,
					value: item
				}
			})
			columns4.value[1].filters = state.filters3
			state.tableCurveData2 = data.func_result.ref_data[state.selectId2]

			state.tableData2 = state.searchCurve ? data.func_result.ref_data[state.selectId2].filter(item => regex.test(item.name)) : state.tableCurveData2
		} else {
			state.filters1 = data.func_result.all_type.map(item => {
				return {
					text: item,
					value: item
				}
			})
			state.filters2 = data.func_result.all_scenario.map(item => {
				return {
					text: item,
					value: item == t('(空白)') ? '' : item
				}
			})
			columns3.value[1].filters = state.filters1
			columns3.value[2].filters = state.filters2
			state.tableCurveData1 = data.func_result.ts_data[state.selectId1]
			state.tableData1 = state.searchCurve ? data.func_result.ts_data[state.selectId1].filter(item => regex.test(item.name)) : state.tableCurveData1
		}
	} else if (data.func_result.code == 0) {
		message.error(data.func_result.message)
	}
	state['loading' + val] = false
}
const onSelectChange1 = (selectedRowKey, selectedRows) => {
	state.baseTableData1.find(item => item.index == state.selectId1).ts_len = selectedRowKey.length
	if (state.search) {
		state.filterTable1 = state.baseTableData1.filter(item => new RegExp(state.search, 'i').test(item.name))
	}
	selectedRowKeys1.value = selectedRowKeys1.value.filter(item => !state.tableData1.find(items => item == items.loop_index)).concat(selectedRowKey)
}
const onSelectChange2 = (selectedRowKey, selectedRows) => {
	selectedRowKeys2.value = selectedRowKeys2.value.filter(item => !state.tableData2.find(items => item == items.loop_index)).concat(selectedRowKey)
}
const rowClassName1 = (record) => {
	return record.index === state.selectId1 ? 'clickRowStyl' : ''
}
const rowClassName2 = (record) => {
	return record.index === state.selectId2 ? 'clickRowStyl' : ''
}
const customRow = (record, index) => {
	return {
		onClick: () => {
			if (state['selectId' + state.type] == record.index) {
				state['selectId' + state.type] = undefined
				state['tableData' + state.type] = []
				state['loading' + state.type] = false
				return
			}
			state.recordRow = record
			state.recordIndex = index
			customRowFun(record, index)
		}
	}
}

const customRowFun = (record, index) => {
	state['loading' + state.type] = true
	const regex = new RegExp(state.searchCurve, 'i')

	if (state.type == 1) {
		pageSizeOptions1.value.current = 1
	} else {
		pageSizeOptions2.value.current = 1
	}

	state['selectId' + state.type] = record.index
	nextTick(() => {
		if (state.type == 1) {
			scroll_table1.value.$el.querySelector('.ant-table-body').scrollTop = (36 + 1 / (window.innerWidth / 1920) * (1 / window.devicePixelRatio)) * index
		} else {
			scroll_table2.value.$el.querySelector('.ant-table-body').scrollTop = (36 + 1 / (window.innerWidth / 1920) * (1 / window.devicePixelRatio)) * index
		}
	})
	if (state.showAll1 && state.type == 1) {
		showAllTable(1)
	} else if (state.showAll2 && state.type == 2) {
		showAllTable(2)
	} else {
		if (state['selectTableData' + state.type][record.index].length <= 0) {
			state.type == 1 ? showAllTable(1) : showAllTable(2)
		} else {
			state['tableData' + state.type] = state.searchCurve ? state['selectTableData' + state.type][record.index].filter(item => regex.test(item.name)) : state['tableData' + state.type] = state['selectTableData' + state.type][record.index]
			state['tableCurveData' + state.type] = state['selectTableData' + state.type][record.index]
			state['loading' + state.type] = false
		}
	}
}
const confirm = async (val) => {
	let res
	try {
		if (val == 2) {
			res = await getBaseDataApi({
				'import_string_func': 'teapcase:update_element_reference_in_tc',
				'func_arg_dict': {
					'file_name': route.query.filePath,
					'sheet_name': props.treeValue,
					'selected_reference_data': selectedRowKeys2.value,
					'selected_row_id_list': props.data,
					'ref_table': props.treeValue.includes('_plan') ? 'storage_plan' : 'storage'
				}
			})
		} else {
			res = await getBaseDataApi({
				'import_string_func': 'teapcase:update_element_timeseries_in_tc',
				'func_arg_dict': {
					'file_name': route.query.filePath,
					'sheet_name': props.treeValue,
					'selected_ts_data': selectedRowKeys1.value,
					'selected_row_id_list': props.data
				}
			})
		}
	} catch (error) {
		// logger.error(error)
	}
	if (res.code == 1 && res.func_result.code == 1) {
		message.success(res.func_result.message)
		emit('refresh')
	} else if (res.code == 1 && res.func_result.code !== 1) {
		message.error(res.func_result.message)
	}
}
const closeModal = () => {
	emit('close')
}
watch(() => state.search, v => {
	state['tableLoading' + state.type] = true
	const regex = new RegExp(v, 'i')
	state.filterTable1 = v ? state.baseTableData1.filter(item => regex.test(item.name)) : state.baseTableData1
	state.filterTable2 = v ? state.baseTableData2.filter(item => regex.test(item.name)) : state.baseTableData2
	state['tableLoading' + state.type] = false
})

watch(() => state.searchCurve, v => {
	state['loading' + state.type] = true
	const regex = new RegExp(v, 'i')
	state.tableData1 = v ? state.tableCurveData1.filter(item => regex.test(item.name)) : state.tableCurveData1
	state.tableData2 = v ? state.tableCurveData2.filter(item => regex.test(item.name)) : state.tableCurveData2
	state['loading' + state.type] = false
})
const changeNameMaybeOnlyFlag = (val) => {
	getInit(0)
}
const getInit = (threshold) => {
	state['tableLoading' + state.type] = true
	Promise.all([getBaseDataApi({
		'import_string_func': 'teapcase:match_element_timeseries_in_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_name': props.treeValue,
			'row_id_list': props.data,
			'similarity': true,
			'threshold': threshold,
			'name_maybe_only_flag': state.name_maybe_only_flag
		}
	})].concat(props.treeValue.includes('stogen') ? [getBaseDataApi({
		'import_string_func': 'teapcase:match_element_reference_in_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'sheet_name': props.treeValue,
			'row_id_list': props.data,
			'ref_table': props.treeValue.includes('_plan') ? 'storage_plan' : 'storage',
			'threshold': threshold,
			'name_maybe_only_flag': state.name_maybe_only_flag
		}
	})] : [])).then(([res1, res2]) => {
		state.lockIndex1 = res1.func_result.default_loop_index ? res1.func_result.default_loop_index : []
		columns1.value = res1.func_result.columns.filter(item => !item.hide).map(item => {
			return Object.assign({
				title: item.headerName,
				dataIndex: item.field,
				key: item.field,
				width: item.field == 'name_maybe' ? 100
					: item.field == 'name' ? 180
						: (item.headerName.length * 15) > 70 ? (item.headerName.length * 15) : 70,
				align: 'center',
				ellipsis: true,
				resizable: item.field != 'ts_len'
			})
		})
		state.filters1 = res1.func_result.all_type.map(item => {
			return {
				text: item,
				value: item
			}
		})
		state.filters2 = res1.func_result.all_scenario.map(item => {
			return {
				text: item,
				value: item == t('(空白)') ? '' : item
			}
		})
		columns3.value[1].filters = state.filters1
		columns3.value[2].filters = state.filters2
		selectedRowKeys1.value = res1.func_result.match_loop_index
		state.baseTableData1 = res1.func_result.data
		state.filterTable1 = res1.func_result.data
		state.selectTableData1 = res1.func_result.ts_data
		state.ts_type_name_map = res1.func_result.ts_type.ts_type_name_map

		if (props.treeValue.includes('stogen')) {
			state.lockIndex2 = res2.func_result.default_loop_index ? res2.func_result.default_loop_index : []
			columns2.value = res2.func_result.columns.filter(item => !item.hide).map(item => {
				return Object.assign({
					title: item.headerName,
					dataIndex: item.field,
					key: item.field,
					width: item.field == 'name_maybe' ? 120
						: item.field == 'name' ? 120
							: (item.headerName.length * 15) > 70 ? (item.headerName.length * 15) : 70,
					align: 'center',
					ellipsis: true,
					resizable: item.field != 'ts_len'
				}, item.field == 'name' ? {
					fixed: 'right'
				} : {})
			})
			state.filters3 = res2.func_result.all_type.map(item => {
				return {
					text: item,
					value: item
				}
			})
			columns4.value[1].filters = state.filters3
			selectedRowKeys2.value = res2.func_result.match_loop_index
			state.baseTableData2 = res2.func_result.data
			state.filterTable2 = res2.func_result.data
			state.selectTableData2 = res2.func_result.ref_data
		}

		state['tableLoading' + state.type] = false
		if (threshold === 0 && state.recordIndex !== undefined) {
			customRowFun(state.recordRow, state.recordIndex)
		}
	})
}
onMounted(async () => {
	getInit(75)
})
</script>
<style lang="scss">
.time_match {
	.ant-modal {
		width: auto !important;

		.ant-modal-body {
			>div {
				.modal_content {
					display: flex;
					padding: 20px 20px 90px;

					>div {
						height: 630px;

						>div:first-child {
							margin-bottom: 10px;
						}

						.ant-empty-normal {
							margin-block: 219px;
						}

						.show_all {
							text-align: center;
							line-height: 28px;
							border-bottom: 1px solid #f0f0f0;
							display: flex;
							justify-content: center;

							&:hover {
								background: #fafafa;
							}

							p {
								width: auto;
								padding: 0 20px;

								&:hover {
									cursor: pointer;
									color: var(--base-color);
								}
							}
						}
					}

					>div:first-child {
						width: 510px;
						margin-right: 20px;
					}

					.ant-table {
						.ant-table-body {
							// min-height: 510px;
							scroll-behavior: smooth;
						}

						.ant-table-cell {
							padding: 5px 10px !important;
							line-height: 26px;
						}

						.ant-table-cell-fix-right {
							// right: 0px!important;
						}

						.ant-table-cell-scrollbar {
							// display: none;
						}
					}

					.clickRowStyl {
						td {
							background: rgba(var(--base-color-rgb), 1) !important;
						}
					}

					.line_btn {
						color: var(--base-color);

						&:hover {
							cursor: pointer;
						}
					}

					>div:last-child {
						width: 800px;

						.select_top {
							height: 32px;
							display: flex;
							justify-content: space-between;

							>div {
								display: flex;
								line-height: 32px;

								>div {
									width: 100px;
									text-align: center;
									// letter-spacing: 5px;
									background-color: #f0f0f0;

									&:hover {
										cursor: pointer;
									}
								}

								.active {
									background-color: var(--base-color);
									color: #f0f0f0;
								}
							}
						}
					}
				}
			}
		}
	}
}
</style>
