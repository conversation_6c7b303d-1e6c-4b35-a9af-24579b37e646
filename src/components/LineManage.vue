<template>
  <a-modal wrapClassName="line_manage" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <div class="user-select">
      <a-spin :spinning="state.loading">
          <div>
              <div class="modal_top">
                  <p>{{ $t('线路型号管理') }}</p>
                  <close-outlined class="pointer" @click="emit('close')" />
              </div>
              <div class="modal-content">
                  <div>
                      <div>
                          <a-button :disabled="state.isAdd||state.isEdit" type="primary" @click="addLine">{{ $t('新增') }}</a-button>
                          <a-button :disabled="state.isAdd||state.isEdit" @click="reset">{{ $t('重置') }}</a-button>
                      </div>
                      <div>
                          <a-input v-model:value="state.lineName" :disabled="state.isAdd" :placeholder="$t('请输入线路型号')" @change="searchLine">
                              <template #prefix>
                                  <SearchOutlined />
                              </template>
                          </a-input>
                      </div>
                      <div ref="scroll">
                          <div v-for="(item,index) in state.lineData" :class="state.activeItem.id==item.id?'active':''"  :key="index" @click="selectLine(item)">
                              <a-tooltip :mouseLeaveDelay="0" v-if='state.activeItem.id==item.id'>
                                  <template #title>{{ formState.model_name}}</template>
                                  <p class="ellipsis">{{ formState.model_name }}</p>
                              </a-tooltip>
                              <a-tooltip :mouseLeaveDelay="0" v-else>
                                  <template #title>{{ item.model_name }}</template>
                                  <p class="ellipsis">{{ item.model_name }}</p>
                              </a-tooltip>
                              <div class="center" v-if="state.activeItem.id==item.id&&!state.isAdd">
                                  <a-tooltip  :mouseLeaveDelay="0">
                                      <template #title>{{$t('编辑')}}</template>
                                      <div class="wrap_span" @click.stop="editLine(item)">
                                          <form-outlined />
                                      </div>
                                  </a-tooltip>
                                  <a-tooltip  :mouseLeaveDelay="0">
                                      <template #title>{{$t('删除')}}</template>
                                      <div class="wrap_span">
                                          <delete-outlined @click.stop="deleteLine(item.id)"/>
                                      </div>
                                  </a-tooltip>
                              </div>
                          </div>
                      </div>
                  </div>
                  <div>
                      <div class="mask" v-show="!(state.isAdd||state.isEdit)">

                      </div>
                      <div class="icon_list" v-if="state.isAdd||state.isEdit">
                          <a-button shape="circle" type="primary" :icon="h(CheckOutlined)" @click="saveEdit"/>
                  <a-button shape="circle" :icon="h(CloseOutlined)" @click="cancelEdit" />
                      </div>
                      <a-form
                          :model="formState"
                          ref="formRef"
                      >
                          <a-form-item
                              :label="$t('型号')"
                              name="model_name"
                              :rules="[{ required:true, message:$t('请输入线路型号')+ '! ' }]"
                          >
                              <a-input :disabled="formState.built_in" v-model:value="formState.model_name" />
                          </a-form-item>
                          <a-form-item
                              label="R1"
                              name="R1"
                              :rules="[{ required:true, message:$t('请输入')+ 'R1! ' }]"
                          >
                              <a-input-number :min="0" :controls="false"  v-model:value="formState.R1" >
                                  <template #addonAfter>
                                      <span>Ω/km</span>
                                  </template>
                              </a-input-number>
                          </a-form-item>
                          <a-form-item
                              label="X1"
                              name="X1"
                              :rules="[{ required:true, message: $t('请输入')+'X1! ' }]"
                          >
                              <a-input-number :min="0" :controls="false"  v-model:value="formState.X1" >
                                  <template #addonAfter>
                                      <span>Ω/km</span>
                                  </template>
                              </a-input-number>
                          </a-form-item>
                          <a-form-item
                              label="B1"
                              name="B1"
                              :rules="[{ required:true, message: $t('请输入')+'B1! ' }]"
                          >
                              <a-input-number :min="0" :controls="false"  v-model:value="formState.B1" >
                                  <template #addonAfter>
                                      <span>10<sup>-6</sup>S</span>
                                  </template>
                              </a-input-number>
                          </a-form-item>
                          <a-form-item
                              label="R0"
                              name="R0"
                              :rules="[{ required:true, message: $t('请输入')+'R0! ' }]"
                          >
                              <a-input-number :min="0" :controls="false"  v-model:value="formState.R0" >
                                  <template #addonAfter>
                                      <span>Ω/km</span>
                                  </template>
                              </a-input-number>
                          </a-form-item>
                          <a-form-item
                              label="X0"
                              name="X0"
                              :rules="[{ required:true, message: $t('请输入')+'X0! ' }]"
                          >
                              <a-input-number :min="0" :controls="false"  v-model:value="formState.X0" >
                                  <template #addonAfter>
                                      <span>Ω/km</span>
                                  </template>
                              </a-input-number>
                          </a-form-item>
                          <a-form-item
                              label="B0"
                              name="B0"
                              :rules="[{ required:true, message: $t('请输入')+'B0! ' }]"
                          >
                              <a-input-number :min="0" :controls="false"  v-model:value="formState.B0" >
                                  <template #addonAfter>
                                      <span>10<sup>-6</sup>S</span>
                                  </template>
                              </a-input-number>
                          </a-form-item>
                          <a-form-item
                              :label="$t('导流截面')"
                          >
                              <div class="form_item">
                                  <a-input-number :min="0" :controls="false"  v-model:value="formState.wire_a" >
                                  </a-input-number>
                                  <p>X</p>
                                  <a-input-number :min="0" :controls="false"  v-model:value="formState.wire_b" >
                                  </a-input-number>
                              </div>
                          </a-form-item>
                          <a-form-item
                              :label="$t('额定电流')"
                              name="rated_amps"
                          >
                              <a-input-number :min="0" :controls="false"  v-model:value="formState.rated_amps" >
                                  <template #addonAfter>
                                      <span>kA</span>
                                  </template>
                              </a-input-number>
                          </a-form-item>
                      </a-form>
                  </div>
              </div>
          </div>
      </a-spin>
    </div>
  </a-modal>
</template>
<script setup>
/* eslint-disable no-unused-vars */
import { onMounted, reactive, ref, computed, h, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { basicApi } from '@/api/exampleApi'
import message from '@/utils/message'
import { openModal } from '@/utils/gis'
import { t } from '@/utils/common'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons-vue'
const route = useRoute()
const state = reactive({
	ifShow: true,
	loading: false,
	lineName: undefined,
	activeId: undefined,
	isAdd: false,
	isEdit: false,
	activeItem: {},
	lineData: [],
	baseData: []
})
const emit = defineEmits(['close'])
const scroll = ref()
const formRef = ref()
const formState = ref({
	model_name: undefined,
	X1: undefined,
	R1: undefined,
	B1: undefined,
	R0: undefined,
	X0: undefined,
	B0: undefined,
	wire_a: undefined,
	wire_b: undefined,
	rated_amps: undefined
})
const baseForm = {
	model_name: undefined,
	X1: undefined,
	R1: undefined,
	B1: undefined,
	R0: undefined,
	X0: undefined,
	B0: undefined,
	wire_a: undefined,
	wire_b: undefined,
	rated_amps: undefined
}
const reset = () => {
	state.lineName = undefined
	state.loading = true
	basicApi({
		'import_string_func': 'teapgis:restore_all_line_model',
		'func_arg_dict': {
			'tc_file_path': route.query.filePath
		}
	}).then(res => {
		state.loading = false
		if (res.code == 1 && res.func_result.code == 1) {
			message.success(res.func_result.message)
			state.activeItem = {}
			formState.value = baseForm
			state.baseData = res.func_result.data
			state.lineData = Object.keys(res.func_result.data).map(item => {
				return Object.assign({}, res.func_result.data[item], {
					id: item
				})
			})
		}
	}).catch(() => {
		state.loading = false
	})
}
const addLine = () => {
	state.isAdd = true
	state.activeItem = {
		id: 'edit',
		model_name: t('未命名'),
		X1: undefined,
		R1: undefined,
		B1: undefined,
		R0: undefined,
		X0: undefined,
		B0: undefined,
		wire_a: undefined,
		wire_b: undefined,
		rated_amps: undefined
	}
	formState.value = state.activeItem
	state.lineData.push(state.activeItem)
	nextTick(() => {
		scroll.value.scrollTop = scroll.value.scrollHeight
	})
}
const selectLine = (item) => {
	if (state.isAdd) return
	if (state.activeItem.id == item.id) {
		return
	}
	state.activeItem = item
	formState.value = item
}
const saveEdit = () => {
	formRef.value.validate()
		.then(() => {
			state.loading = true
			if (state.isAdd) {
				basicApi({
					'import_string_func': 'teapgis:add_one_line_model',
					'func_arg_dict': {
						'tc_file_path': route.query.filePath,
						'line_model_data': {
							'model_name': formState.value.model_name,
							'X1': formState.value.X1,
							'R1': formState.value.R1,
							'B1': formState.value.B1,
							'R0': formState.value.R0,
							'X0': formState.value.X0,
							'B0': formState.value.B0,
							'wire_a': formState.value.wire_a,
							'wire_b': formState.value.wire_b,
							'rated_amps': formState.value.rated_amps
						}
					}
				}).then(res => {
					state.loading = false
					if (res.code == 1 && res.func_result.code == 1) {
						message.success(res.func_result.message)
						state.activeItem = {}
						formState.value = baseForm
						state.isAdd = false
						state.baseData = res.func_result.data
						state.lineData = Object.keys(res.func_result.data).map(item => {
							return Object.assign({}, res.func_result.data[item], {
								id: item
							})
						}).filter(item => item.model_name.indexOf(state.lineName || '') > -1)
					}
				}).catch(() => {
					state.loading = false
				})
			} else {
				basicApi({
					'import_string_func': 'teapgis:update_one_line_model',
					'func_arg_dict': {
						'tc_file_path': route.query.filePath,
						'line_model_id': state.activeItem.id,
						'update_data': {
							'model_name': formState.value.model_name,
							'X1': formState.value.X1,
							'R1': formState.value.R1,
							'B1': formState.value.B1,
							'R0': formState.value.R0,
							'X0': formState.value.X0,
							'B0': formState.value.B0,
							'wire_a': formState.value.wire_a,
							'wire_b': formState.value.wire_b,
							'rated_amps': formState.value.rated_amps
						}
					}
				}).then(res => {
					state.loading = false
					if (res.code == 1 && res.func_result.code == 1) {
						message.success(res.func_result.message)
						state.activeItem = {}
						formState.value = baseForm
						state.isEdit = false
						state.baseData = res.func_result.data
						state.lineData = Object.keys(res.func_result.data).map(item => {
							return Object.assign({}, res.func_result.data[item], {
								id: item
							})
						}).filter(item => item.model_name.indexOf(state.lineName || '') > -1)
					}
				}).catch(() => {
					state.loading = false
				})
			}
		})
		.catch(error => {
			console.log('error', error)
		})
}
const cancelEdit = () => {
	if (state.isAdd) {
		state.activeItem = {}
		state.isAdd = false
		state.lineData.pop()
		formRef.value.resetFields()
	} else {
		state.isEdit = false
		formRef.value.resetFields()
	}
	formState.value = state.activeItem
}
const editLine = (item) => {
	state.isEdit = true
}
const closeModal = () => {
	emit('close')
}
const searchLine = () => {
	state.lineData = Object.keys(state.baseData).map(item => {
		return Object.assign({}, state.baseData[item], {
			id: item
		})
	}).filter(item => item.model_name.indexOf(state.lineName) > -1)
}
const deleteLine = (id) => {
	openModal(t('确定删除吗？'), t('删除后无法恢复')).then(() => {
		state.loading = true
		basicApi({
			'import_string_func': 'teapgis:delete_one_line_model',
			'func_arg_dict': {
				'line_model_id': id,
				'tc_file_path': route.query.filePath
			}
		}).then(res => {
			state.loading = false
			if (res.code == 1 && res.func_result.code == 1) {
				message.success(res.func_result.message)
				state.baseData = res.func_result.data
				state.lineData = Object.keys(res.func_result.data).map(item => {
					return Object.assign({}, res.func_result.data[item], {
						id: item
					})
				}).filter(item => item.model_name.indexOf(state.lineName || '') > -1)
			} else {
				message.error(res.func_result.message)
			}
		}).catch(() => {
			state.loading = false
		})
	})
}
const initData = () => {
	state.loading = true
	basicApi({
		'import_string_func': 'teapgis:list_all_line_model',
		'func_arg_dict': {
			'tc_file_path': route.query.filePath
		}
	}).then(res => {
		state.loading = false
		if (res.code == 1 && res.func_result.code == 1) {
			state.baseData = res.func_result.data
			state.lineData = Object.keys(res.func_result.data).map(item => {
				return Object.assign({}, res.func_result.data[item], {
					id: item
				})
			})
		}
	}).catch(() => {
		state.loading = false
	})
}
onMounted(async() => {
	initData()
})
</script>
<style lang="scss">
    .line_manage {
        .ant-modal{
            width: auto!important;
            .modal-content{
                width: 800px;
                padding: 30px 40px;
                display: flex;
                justify-content: space-between;
                >div:first-child{
                    width: 320px;
                    box-sizing: border-box;
                    border: 1px solid rgb(162, 173, 184);
                    border-radius: 4px;
                    background: rgb(255, 255, 255);
                    >div:first-child{
                        border-bottom: 1px solid rgb(33, 98, 168);
                        padding: 5px;
                        display: flex;
                        button{
                            margin-right: 10px;
                            padding: 0;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 26px;
                            width: 52px;
                        }
                    }
                    >div:nth-child(2){
                        padding: 10px;
                    }
                    >div:last-child{
                        height: 500px;
                        padding: 0 5px;
                        overflow-y: auto;
                        .active{
                            border-bottom: 1px dashed rgb(108, 156, 207);
                            border-top: 1px dashed rgb(108, 156, 207);
                            background: rgb(220, 236, 251);
                        }
                        >div{
                            margin-bottom: 5px;
                            font-size: 14px;
                            line-height: 26px;
                            height: 26px;
                            display: grid;
                            grid-template-columns: 4fr 1fr;
                            padding: 0 10px;
                            border: 1px solid transparent;
                            &:hover{
                                cursor: pointer;
                                border-bottom: 1px dashed rgb(108, 156, 207);
                                border-top: 1px dashed rgb(108, 156, 207);
                                background: rgb(220, 236, 251);
                            }
                        }
                        .wrap_span{
                            width: 22px;
                            height: 22px;
                            border-radius: 5px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            &:hover{
                                background-color: rgb(232, 233, 233);
                                cursor: pointer;
                                color: var(--base-color);
                            }
                        }
                    }
                }
                >div:last-child{
                    width: 370px;
                    padding:50px 20px 0;
                    box-sizing: border-box;
                    border: 1px solid rgb(162, 173, 184);
                    border-radius: 4px;
                    background: rgb(250, 250, 250);
                    position: relative;
                    .icon_list{
                        right: 0;
                        top: 10px;
                        position: absolute;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        button{
                           min-width: unset;
                           height: 26px;
                           width: 26px;
                           padding: 0;
                           display: flex;
                           justify-content: center;
                           align-items: center;
                           margin-right: 10px;
                        }
                    }
                    .mask{
                        position: absolute;
                        z-index: 5;
                        width: 100%;
                        height: 100%;
                        left: 0;
                        background-color: #fff;
                        opacity: 0.5;
                        top: 0;
                        &:hover{
                            cursor: not-allowed;
                        }
                    }
                    .form_item{
                        display: flex;
                        align-items: center;
                        p{
                            margin: 0 10px!important;
                        }
                    }
                }
            }
        }
    }
</style>
