<template>
  <a-modal wrapClassName="modal_line" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <div class="user-select" :style="{zoom: state.zoom}">
      <div class="modal_top">
        <p>{{ $t('曲线预览') }}</p>
        <close-outlined class="pointer" @click="emit('close')" />
    </div>
    <a-spin :spinning="state.loading" size="large" :tip="$t('接口请求中')">
      <div class="modal_content relative">
        <div class="line" ref="line" :style="{transform:`scale(${state.scales})`,zoom:`${state.zooms}`,height:`${state.height}px`}"></div>

      </div>
    </a-spin>
    </div>
  </a-modal>
</template>
<script setup>
import { onMounted, reactive, ref, inject, markRaw, onUnmounted } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'

import { getCurveSeries } from '@/utils/teap'
import { getBaseDataApi } from '@/api/exampleApi'
import { useRoute } from 'vue-router'
import { debounce } from '@/utils/gis'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const storeSetting = settingStore()
const { isChromeHigh } = storeToRefs(storeSetting)
const route = useRoute()
const props = defineProps({
	index: {
		type: Number
	}
})
const echarts = inject('ec')
const curveChart = ref()
const line = ref()
const state = reactive({
	ifShow: true,
	loading: false,
	curveData: {},
	scales: 1,
	zoom: 1,
	zooms: 1,
	height: 600,
	lineData: []
})

const emit = defineEmits(['close'])
const closeModal = () => {
	emit('close')
}
const initLine = (timeArr) => {
	const option = getCurveSeries(state.curveData, state.lineConfig, curveChart.value, timeArr, state.startIndex, state.endIndex, state.startZoom, state.endZoom, state.unitValue, state.unitName)
	curveChart.value.setOption(option, true)
	curveChart.value.on('datazoom', (event) => {
		state.startZoom = event.start
		state.endZoom = event.end
	})
}
const screenScale = () => {
	let root
	if (isChromeHigh.value) {
		root = document.getElementsByClassName('home-body')[0].style.zoom || 1
		state.zoom = root
	} else {
		root = document.body.style.zoom
	}
	state.zooms = 1 / root
	state.scales = root
	state.height = 600 * root
}
const debouncedScreenScale = debounce(screenScale, 200)
onUnmounted(() => {
	window.removeEventListener('resize', debouncedScreenScale)
})
onMounted(() => {
	screenScale()
	window.addEventListener('resize', debouncedScreenScale)
	curveChart.value = markRaw(echarts.init(line.value))
	state.loading = true
	getBaseDataApi(
		{
			'import_string_func': 'teapcase:read_many_ts_value_from_tc',
			'func_arg_dict': {
				'file_name': route.query.filePath,
				'row_id_list': props.index
			}
		}
	).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			res.func_result.data.forEach(item => {
				state.curveData[item.name + '-' + item.index] = item.value
			})
			state.lineConfig = res.func_result.data.map(item => {
				return {

					lineName: item.name + '-' + item.index,
					name: item.name,
					sequence: 0,
					type: 'line'
				}
			})

			initLine(res.func_result.time)
		}
		state.loading = false
	}).catch(() => {
		state.loading = false
	})
})
</script>
<style lang="scss">
.modal_line{
  .ant-modal{
    width: auto!important;
    // height: 80vh;
    .ant-modal-body{
      >div{
        .modal_content{
          height: 680px;
          padding: 30px 30px 30px;
		  width: 1200px;
          .line{
              transform-origin: 0 0;
          }
        }
      }
    }
  }
}
</style>

