<template>
  <div :class="'chart_box'">
      <div class="chart_pie_box">
        <div>
          <p>{{ $t('发电装机组成') }}</p>
          <div ref="pie1" class="pie" :style="{transform:`scale(${state.scale})`,zoom:`${state.zoom}`}">
          </div>
        </div>
        <div>
          <p>{{ $t('发电量组成') }}</p>
          <div ref="pie2" class="pie" :style="{transform:`scale(${state.scale})`,zoom:`${state.zoom}`}">
          </div>
        </div>
      </div>
      <div class="chart_table_box">
        <div class="chart_newable_box">
          <div class="littleTitle">{{ $t('新能源消纳情况') }}</div>
          <div class="unit_box">
            {{ $t('单位') }}：
            <a-select
              ref="select"
              size="small"
              :bordered="false"
              v-model:value="state.unitValue"
              style="width: 100px"
              :options="state.unitOptions"
            ></a-select>
          </div>
          <div class="result_table">
            <div>
              <p></p>
              <p>{{ $t('理论发电量') }}</p>
              <p>{{ $t('实际发电量') }}</p>
              <p>{{ $t('弃电量') }}</p>
              <p>{{ $t('消纳率') }}</p>
            </div>
            <div>
              <p>{{ $t('风电') }}</p>
              <p>{{ formatCloud(state.renewable_electricity.wind_total) }}</p>
              <p>{{ formatCloud(state.renewable_electricity.wind_acc) }}</p>
              <p>{{ formatCloud(state.renewable_electricity.wind_curt) }}</p>
              <p>{{ formatCloudRate(state.renewable_util_rate.wind) }}%</p>
            </div>
            <div>
              <p>{{ $t('光伏') }}</p>
              <p>{{ formatCloud(state.renewable_electricity.solar_total) }}</p>
              <p>{{ formatCloud(state.renewable_electricity.solar_acc) }}</p>
              <p>{{ formatCloud(state.renewable_electricity.solar_curt) }}</p>
              <p>{{ formatCloudRate(state.renewable_util_rate.solar) }}%</p>
            </div>
            <div>
              <p>{{ $t('新能源') }}</p>
              <p>{{ formatCloud(state.renewable_electricity.renewable_total) }}</p>
              <p>{{ formatCloud(state.renewable_electricity.renewable_acc) }}</p>
              <p>{{ formatCloud(state.renewable_electricity.renewable_curt) }}</p>
              <p>{{ formatCloudRate(state.renewable_util_rate.renewable) }}%</p>
            </div>
          </div>
        </div>
        <div class="chart_hour_box">
          <div class="littleTitle">{{ $t('发电利用小时数') }}</div>
          <div :class="state.gen_util_hour_plan.length > 0 ? 'result_table_plan' : 'result_table'">
            <div>
              <p>{{ $t('电源类型') }}</p>
              <p>{{ $t('边界机组利用小时') }}</p>
              <p v-if="state.gen_util_hour_plan.length > 0">{{ $t('规划机组利用小时') }}</p>
            </div>

            <div v-for="(item,index) in state.gen_util_hour" :key="index">
              <p>{{ item.name }}</p>
              <p>{{ formatCloudToFixed(item.value) }}</p>
              <p v-if="state.gen_util_hour_plan.length > 0">{{ formatCloudToFixed(item.value_plan) }}</p>
            </div>

          </div>
        </div>
      </div>

      <div class="sceneBox">
        <a-select
          v-show="state.sim_mode !== 'long_term' && state.countMode == 'security'"
          style="width: 160px"
          v-model:value="state.scene"
          :options="state.sceneList"
          :placeholder="$t('请选择场景')"
          @change="handleSceneChange"
        >
        </a-select>
        <a-select
          :style="{width: '160px',marginLeft: '5px'}"
          v-model:value="state.partitionValue"
          :options="state.partitionOptions"
          :placeholder="$t('请选择分区')"
          @change="handlePartitionChange"
        >
        </a-select>
      </div>

  </div>
</template>
<script setup>

import { reactive, inject, onMounted, markRaw, onUnmounted, toRefs, computed, onActivated } from 'vue'
import { useRoute } from 'vue-router'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
import { getPieSeries } from '@/utils/resultEchartsOptions.js'
import { debounce } from '@/utils/gis.js'
import { GetSimulationTaskResult } from '@/api/index'
import { t } from '@/utils/common'

const route = useRoute()
const store = settingStore()
const { add_watermark, watermark_text, isChromeHigh } = storeToRefs(store)
const echarts = inject('ec')
const emit = defineEmits(['showLoading', 'refresh', 'hideLoading'])

const state = reactive({
	sim_mode: '',
	countType: 'plan',
	statisticsData: {},
	statisticsStructure: {},
	gen_cap_total: [],
	gen_power_total: [],
	gen_util_hour: [],
	gen_util_hour_plan: [],
	renewable_electricity: {},
	renewable_util_rate: {},
	scale: '',
	zoom: '',
	isScale: false,
	routePath: route.fullPath,
	unitOptions: [],
	unitValue: undefined,
	countMode: '',
	planData: [
		{
			key: 'acline_plan_cap_total',
			name: t('规划交流线路'),
			children: []
		},
		{
			key: 'dcline_plan_cap_total',
			name: t('规划直流线路'),
			children: []
		},
		{
			key: 'stogen_plan_cap_total',
			name: t('规划储能最大放电功率'),
			children: []
		},
		{
			key: 'storage_plan_cap_total',
			name: t('规划交流线路'),
			children: []
		}
	],
	scene: 2,
	sceneList: [
		{
			value: 2,
			label: t('保供模式')
		}
	],
	partitionValue: '',
	partitionOptions: [{
		value: '',
		label: t('全系统')
	}]
})

const allEcharts = reactive({
	pieChart1: undefined,
	pieChart2: undefined,
	pie1: undefined,
	pie2: undefined
})

const { pie1, pie2 } = toRefs(allEcharts)

const initEchartsPie = (data1, data2, unit1, unit2) => {
	allEcharts.pieChart1.Chart_level = 1
	allEcharts.pieChart2.Chart_level = 1
	const option1 = getPieSeries(data1, unit1, allEcharts.pieChart1, 1, add_watermark.value, watermark_text.value)
	const option2 = getPieSeries(data2, unit2, allEcharts.pieChart2, 2, add_watermark.value, watermark_text.value)
	allEcharts.pieChart1.hideLoading()
	allEcharts.pieChart2.hideLoading()
	allEcharts.pieChart1.setOption(option1, true)
	allEcharts.pieChart2.setOption(option2, true)
}

const handleSceneChange = (val) => {
	state.gen_cap_total = []
	state.gen_power_total = []
	state.gen_util_hour = []
	state.gen_util_hour_plan = []
	state.unitOptions = []

	getMidTermResult()
}

const handlePartitionChange = (val) => {
	state.gen_cap_total = []
	state.gen_power_total = []
	state.gen_util_hour = []
	state.gen_util_hour_plan = []
	state.unitOptions = []
	let tempData = {}
	if (val == '') {
		tempData = state.statisticsData
	} else {
		tempData = state.statisticsData.zone_summaries[val]
	}
	handleData(tempData, state.statisticsStructure)
}

const getMidTermResult = () => {
	emit('showLoading')
	const url = route.query.type == 'mid_term' ? '/backend/teap_api_v3/get_mid_term_task_result/' : '/backend/teap_api_v3/get_long_term_task_result/'
	const tempQuery = {
		'group': (state.sim_mode == 'mid_term' && state.scene == 2) ? '_result.scenario_key_summaries' : '_result.key_summaries'
	}
	tempQuery.result_file_path = route.query.filePath
	GetSimulationTaskResult(url, tempQuery).then(res => {
		if (res.code == 1) {
			const { data, structure } = res
			state.statisticsData = data
			state.statisticsStructure = structure

			if (sessionStorage.getItem('unitValue')) {
				state.unitValue = +sessionStorage.getItem('unitValue')
				sessionStorage.removeItem('unitValue')
			}

			if (data.zone_summaries) {
				state.partitionOptions = [{
					value: '',
					label: t('全系统')
				}].concat(
					Object.keys(data.zone_summaries).map(item => {
						return {
							value: item,
							label: item
						}
					})
				)
			}

			if (state.partitionValue == '') {
				handleData(data, structure)
			} else {
				const tempData = state.statisticsData.zone_summaries[state.partitionValue]
				handleData(tempData, structure)
			}
		}
		emit('hideLoading')
	}).catch(() => {
		emit('hideLoading')
	})
}
const handleData = (data, structure) => {
	for (const key in data.gen_cap_total) {
		state.gen_cap_total.push({
			name: structure['gen_cap_total']['keys'][key],
			value: data.gen_cap_total[key] * Object.values(structure['gen_cap_total'].unit)[0]
		})
	}

	for (const key in data.gen_power_total) {
		state.gen_power_total.push({
			name: structure['gen_power_total']['keys'][key],
			value: data.gen_power_total[key] * Object.values(structure['gen_power_total'].unit)[0]
		})
	}

	state.gen_util_hour_plan = data.gen_util_hour_plan ? Object.keys(data.gen_util_hour_plan) : []
	if (state.gen_util_hour_plan.length > 0) {
		for (const key in structure.gen_util_hour.keys) {
			state.gen_util_hour.push({
				name: structure['gen_util_hour']['keys'][key],
				value: data.gen_util_hour[key] ? data.gen_util_hour[key] : '/',
				value_plan: data.gen_util_hour_plan[key] ? data.gen_util_hour_plan[key] : '/'
			})
		}
	} else {
		for (const key in structure.gen_util_hour.keys) {
			state.gen_util_hour.push({
				name: structure['gen_util_hour']['keys'][key],
				value: data.gen_util_hour[key] ? data.gen_util_hour[key] : '/'
			})
		}
	}

	for (const key in structure['renewable_electricity'].unit) {
		state.unitOptions.push({
			value: structure['renewable_electricity'].unit[key],
			label: key
		})
	}
	state.unitValue = state.unitOptions[0].value
	state.renewable_electricity = data.renewable_electricity
	state.renewable_util_rate = data.renewable_util_rate

	for (const key in structure['acline_plan_cap_total'].keys) {
		state.planData[0].children.push({
			key: key,
			name: structure['acline_plan_cap_total'].keys[key],
			value: data['acline_plan_cap_total'][key]
		})
	}
	for (const key in structure['dcline_plan_cap_total'].keys) {
		state.planData[1].children.push({
			key: key,
			name: structure['dcline_plan_cap_total'].keys[key],
			value: data['dcline_plan_cap_total'][key]
		})
	}
	for (const key in structure['stogen_plan_cap_total'].keys) {
		state.planData[2].children.push({
			key: key,
			name: structure['stogen_plan_cap_total'].keys[key],
			value: data['stogen_plan_cap_total'][key]
		})
	}
	for (const key in structure['storage_plan_cap_total'].keys) {
		state.planData[3].children.push({
			key: key,
			name: structure['storage_plan_cap_total'].keys[key],
			value: data['storage_plan_cap_total'][key]
		})
	}
	const unit1 = Object.keys(state.statisticsStructure.gen_cap_total.unit)[0]
	const unit2 = Object.keys(state.statisticsStructure.gen_power_total.unit)[0]
	initEchartsPie(state.gen_cap_total, state.gen_power_total, unit1, unit2)
	emit('hideLoading')
}

const getPartition = async() => {
	const url = route.query.type == 'mid_term' ? '/backend/teap_api_v3/get_mid_term_task_result/' : '/backend/teap_api_v3/get_long_term_task_result/'
	const tempQuery = {
		'group': `_result`,
		result_file_path: route.query.filePath
	}
	GetSimulationTaskResult(url, tempQuery).then(res => {
		state.sim_mode = res.sim_mode
		if (res.sim_mode == 'mid_term' && res.security_run) {
			res.accommodation_run ? state.sceneList = [
				{
					value: 2,
					label: t('保供模式')
				},
				{
					value: 1,
					label: t('促消纳模式')
				}

			] : state.sceneList = [
				{
					value: 2,
					label: t('保供模式')
				}
			]
			state.scene = 2
			state.countMode = 'security'
		} else {
			state.sceneList = [
				{
					value: 1,
					label: t('促消纳模式')
				}
			]
			state.scene = 1
			state.countMode = 'accommodation'
		}

		getMidTermResult()
	})
}

const formatCloud = computed(() => {
	return function(index) {
		return parseFloat(index * parseFloat(state.unitValue)).toFixed(2)
	}
})
const formatCloudToFixed = computed(() => {
	return function(index) {
		if (typeof index === 'number') {
			return parseFloat(index).toFixed(2)
		} else {
			return index
		}
	}
})
const formatCloudRate = computed(() => {
	return function(index) {
		return parseFloat(index * 100).toFixed(2)
	}
})
const screenScale = (val) => {
	if (val != 1) {
		if (state.routePath == route.fullPath) {
			emit('refresh', 4)
		} else {
			state.isScale = true
		}
	} else {
		let root
		if (isChromeHigh.value) {
			root = document.getElementsByClassName('home-body')[0].style.zoom || 1
		} else {
			root = document.body.style.zoom
		}
		state.zoom = 1 / root
		state.scale = root
		if (sessionStorage.getItem(state.filePath + 'partitionValue4')) {
			state.partitionValue = sessionStorage.getItem(state.filePath + 'partitionValue4')
			sessionStorage.removeItem(state.filePath + 'partitionValue4')
		}
	}
}
const debouncedScreenScale = debounce(screenScale, 200)
onUnmounted(() => {
	if (state.partitionValue) sessionStorage.setItem(state.filePath + 'partitionValue4', state.partitionValue)
	window.removeEventListener('resize', debouncedScreenScale)
})
onActivated(() => {
	if (state.isScale) {
		emit('refresh', 4)
		state.isScale = false
	}
})
onMounted(() => {
	screenScale(1)
	window.addEventListener('resize', debouncedScreenScale)
	allEcharts.pieChart1 = markRaw(echarts.init(allEcharts.pie1))
	allEcharts.pieChart2 = markRaw(echarts.init(allEcharts.pie2))
	getPartition()
})
</script>
<style lang="scss" scoped>
    .chart_box {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        position: relative;
        display: flex;
        justify-content: space-between;
        .sceneBox {
            position: absolute;
            top: 0;
            left: 0;
        }
    }

    .chart_pie_box {
        width: 40%;
        height: 100%;
        // border: 1px solid rgb(228, 80, 6);
        display: flex;
        flex-direction: column;
        >div {
            position: relative;
            height: 50%;
            width: 100%;
            font-size: 14px;
            text-align: center;
            p {
                position: absolute;
                left: 50px;
                top: 50px;
                line-height: 30px;
            }
            .pie {
                width: 100%;
                height: 100%;
                transform-origin: 0 0;
            }
        }
    }
    .chart_table_box {
        width: 50%;
        height: 100%;
        .littleTitle {
            margin-top: 0;
            text-align: center;
            font-size: 14px;
            line-height: 14px;
        }
        .chart_newable_box {
            position: relative;
            .littleTitle {
                margin-bottom: 5px;
                text-align: center;
                font-size: 14px;
            }
            .unit_box {
                position: absolute;
                top: -5px;
                right: 60px;
            }
            .result_table{
                border: 2px solid rgb(195, 222, 232);
                >div:first-child{
                    height: 40px;
                    background-color: #e8f3f8;
                }
                >div:nth-child(2n + 2){
                    background-color: #fff;
                }
                >div:nth-child(2n + 3){
                    background-color: #e8f3f8;
                }
                >div{
                    display: grid;
                    height: 50px;
                    grid-template-columns: 3fr 6fr 6fr 6fr 6fr;
                    >p{
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        text-align: center;
                        font-weight: bolder;
                        border-right: 1px solid rgba($color: #ccc, $alpha: 0.6);
                    }
                    >p:last-child{
                        border-right: none;
                    }
                }
            }
        }
        .chart_hour_box {
            margin-top: 5px;
            .result_table,.result_table_plan{
              border: 2px solid rgb(195, 222, 232);
              >div:first-child{
                    height: 40px;
                    background-color: #e8f3f8;
                }
                >div:nth-child(2n + 2){
                    background-color: #fff;
                }
                >div:nth-child(2n + 3){
                    background-color: #e8f3f8;
                }
                >div{
                    display: grid;
                    height: 30px;
                    grid-template-columns: 2fr 4fr;
                    >p{
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        text-align: center;
                        font-weight: bolder;
                        border-right: 1px solid rgba($color: #ccc, $alpha: 0.6);
                    }
                    >p:last-child{
                        border-right: none;
                    }
                }
            }
            .result_table{
                margin:2px 10px 0;
            }
            .result_table_plan{
                // height: 250px;
                // overflow: auto;
                >div{
                    grid-template-columns: 2fr 4fr 4fr;
                }
            }
        }
    }

    .chart_table_tree {
        width: 500px;
        .littleTitle {
            margin: 15px 0 20px 0;
            text-align: center;
            font-size: 14px;
            color: #424246;
            font-weight: 500;
            // line-height: 30px;
        }
        .ant-table-striped :deep(.table-striped) td {
            background-color: #b81d1d;
        }
    }

</style>
