<!-- eslint-disable no-unused-vars -->
<template>
  <a-modal wrapClassName="bpaVersion_modal" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <div class="user-select">
      <div class="modal_top">
          <p>{{ $t('数据管理') }}</p>
          <close-outlined class="pointer" @click="emit('close')" />
      </div>
      <a-spin :spinning="state.loading" size="large" :tip="$t('文件上传中')">
        <div class="modal_content">
          <a-table
            class="ant-table-striped"
            :columns="state.columns"
            :scroll="{y:520}"
            :pagination="false"
            :data-source="state.tableData"
            :loading="state.tableLoading"
            :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <div class="actionBox">
                  <div><img src="@/assets/icon/download.png" alt="" @click="handleDownload(column, record)"></div>
                  <div>
                    <img v-if="record.is_standard == 1" src="@/assets/icon/protect.png" alt="" @click="handleStandard(column, record)">
                    <img v-if="record.is_standard == 2" src="@/assets/icon/protect_success.png" alt="" @click="handleStandard(column, record)">
                  </div>
                  <div><img src="@/assets/icon/delete.png" alt="" @click="handleDelete(column, record)"></div>
                </div>
              </template>
            </template>
          </a-table>
          <!-- <AgGrid ref="agGridRef"></AgGrid> -->
        </div>
        <div class="modal_btn">
          <div>
            <a-button @click="state.uploadShow=true" >{{ $t('上传') }}</a-button>
          </div>
          <div>
            <a-button type="primary" @click="emit('close')">{{ $t('完成') }}</a-button>
            <a-button @click="emit('close')">{{ $t('关闭') }}</a-button>
          </div>
        </div>
      </a-spin>
		  <bpa-upload v-if="state.uploadShow" @confirm="handleConfirm" @close="state.uploadShow=false"></bpa-upload>
		  <bpa-login v-if="state.loginShow" :record="state.rowRecord" :actionType="state.actionType" @confirm="handleConfirm" @close="state.loginShow=false"></bpa-login>
    </div>
  </a-modal>
</template>
<script setup>
// eslint-disable-next-line no-unused-vars
import { onMounted, reactive, ref, watch, nextTick, computed, unref } from 'vue'
import { CloseOutlined } from '@ant-design/icons-vue'
import { getBpaList, bpaDownload } from '@/api/index'
// import { useRoute } from 'vue-router'
import { Modal } from 'ant-design-vue'
import { fileBlobFun, t } from '@/utils/common.js'

const emit = defineEmits(['close', 'refresh'])

// const agGridRef = ref(null)

const state = reactive({
	ifShow: true,
	loading: false,
	tableLoading: false,
	partitionValue: 'all',
	options: [],
	columns: [{
		title: t('版本名称'),
		dataIndex: 'version',
		key: 'version'
	},
	{
		title: t('文件名称'),
		dataIndex: 'file_name',
		key: 'file_name'
	},
	{
		title: t('更新时间'),
		dataIndex: 'update_time',
		key: 'update_time'
	},
	{
		title: t('前述版本'),
		key: 'pre_version',
		dataIndex: 'pre_version'
	},
	{
		title: t('操作'),
		key: 'action',
		width: 120,
		align: 'center'
	}],
	tableData: [],
	uploadShow: false,
	loginShow: false,
	actionType: undefined
})

const handleDownload = (column, record) => {
	state.loading = true
	bpaDownload(record.id).then(res => {
		const fileName = record.file_name
		fileBlobFun(res.data, fileName)
		state.loading = false
	}).catch(() => {
		state.loading = false
	})
}
const handleStandard = (column, record) => {
	state.rowRecord = record
	state.actionType = 'standard'
	const msg = record.is_standard == 1 ? t('请确认是否要将') + `${record.version}` + t('设为标准版本') : t('请确认是否要取消') + `${record.version}` + t('的标准版本状态')
	Modal.confirm({
		title: t('提示'),
		content: msg,
		okText: t('确定'),
		cancelText: t('取消'),
		onOk() {
			state.loginShow = true
		},
		onCancel() {
			// message.success('edit failed')
		}
	})
}

const handleDelete = (column, record) => {
	state.rowRecord = record
	state.actionType = 'delete'
	Modal.confirm({
		title: t('提示'),
		content: t('请确认是否要将') + `${record.version}` + t('删除'),
		okText: t('确定'),
		cancelText: t('取消'),
		onOk() {
			state.loginShow = true
		},
		onCancel() {
			// message.success('failed')
		}
	})
}

const handleConfirm = () => {
	state.uploadShow = false
	state.loginShow = false
	getInitData()
}
const closeModal = () => {
	emit('close')
}

const getInitData = () => {
	getBpaList({}).then(res => {
		state.tableData = res.data.bpas
	}).catch(err => {
		console.log(err)
	})
}

onMounted(async() => {
	getInitData()
})
</script>
<style lang="scss">
  .bpaVersion_modal{
    .ant-modal{
      width: 60%!important;
      .ant-modal-body{
        >div{
          .modal_content{
              // display: flex;
              width: 100%;
              height: 380px;
              padding: 20px 20px 30px;

              [data-doc-theme='light'] .ant-table-striped :deep(.table-striped) td {
                background-color: #fafafa;
              }
              [data-doc-theme='dark'] .ant-table-striped :deep(.table-striped) td {
                background-color: rgb(29, 29, 29);
              }
              .actionBox {
                display: flex;
                justify-content: space-between;
                font-size: 20px;
                >div {
                  width: 20px;
                  height: 20px;
                  img{
                    width: 20px;
                    height: 20px;
                  }
                }
              }

          }
          .modal_btn {
            width: 100%;
            padding-left: 20px;
            display: flex;
            justify-content: space-between;
          }
        }
      }
    }
  }
</style>

