<template>
    <a-modal v-model:open="state.ifShow" wrapClassName="gis_download_modal" :afterClose="closeModal" :centered="true" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <div class="gis_download_content">
                <img src="/favicon.ico" alt="">
                <p>{{$t('Please click the pop-up window')}}<strong>{{$t('Open tscan')}}</strong></p>
                <p>{{$t('If no pop-up window appears, click')}}<strong>{{$t('Start')}}</strong></p>
                <div>
                    <a-button type="primary" @click="startTask">{{ isTypeZero?$t('Start'):$t('Start BPA scan') }}</a-button>
                    <p>{{ isTypeZero?$t('Note: check BPA scan is installed first'):$t('Note: check BPA scan is installed first') }}</p>
                </div>
                <div class="downlod">
                    <span>{{$t('Not installed')}}？</span>
                </div>
                <div>
                    <a-button @click="closeModal">{{$t('Close window')}}</a-button>
                </div>
            </div>
        </screen-scale>
    </a-modal>
</template>
<script setup>
import { reactive, computed, watch } from 'vue'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'
const store = settingStore()
const { tscanShow, taskUrl, startingCase, taskName, waitingCaseList } = storeToRefs(store)
const state = reactive({
	ifShow: true
})
const props = defineProps({
	type: {
		type: Number,
		default: 0
	}
})
const emit = defineEmits(['close'])
const closeModal = () => {
	if (isTypeZero.value) {
		tscanShow.value = false
	} else {
		emit('close')
	}
}
const startTask = () => {
	const iframe = document.createElement('iframe')
	iframe.style.display = 'none'
	iframe.src = (isTypeZero.value ? taskUrl.value : 'tscan://')
	document.body.appendChild(iframe)
	setTimeout(function() {
		document.body.removeChild(iframe)
	}, 500)
}
const isTypeZero = computed(() => {
	return props.type === 0
})
watch(() => [startingCase.value, waitingCaseList.value], ([v1, v2]) => {
	if (isTypeZero.value) {
		if (v1 === taskName.value || v2.includes(taskName.value)) {
			tscanShow.value = false
		}
	}
})
</script>
<style lang="scss">
    .gis_download_modal{
        .ant-modal{
            width: auto!important;
        }
        .gis_download_content{
            padding:80px 50px 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            img{
                width: 116px;
                margin-bottom: 50px;
            }
            >div{
                width: 100%;
                >p{
                    color: rgb(163, 0, 20);
                    font-weight: bolder;
                    font-size: 14px;
                }
            }
            >p{
                margin-bottom: 10px!important;
            }
            .downlod{
                text-align: center;
                position: relative;
                margin-top: 80px;
                >span{
                    background-color: #fff;
                    z-index: 1;
                    padding: 0 12px;
                    font-size: 14px;
                    color: #7b818f;
                    position: relative;
                }
                &::after{
                    position: absolute;
                    content: " ";
                    width: 100%;
                    height: 1px;
                    left: 0;
                    top: calc(50% - 1px);
                    background-color: #d3d6db;
                }
            }
            button{
                width: 100%;
                margin: 10px 0 12px;
                height: 40px;
            }
            >div:last-child{
                button{
                    border-color: #0055e8;
                    color: #0055e8;
                    margin: 12px 0 0;
                }
            }
        }
    }
</style>
