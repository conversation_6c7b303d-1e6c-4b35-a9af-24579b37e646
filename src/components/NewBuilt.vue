<template>
    <a-modal wrapClassName="modal_newBuilt" :afterClose="closeModal" :centered="true"  v-model:open="state.visible" :footer="null" :closable="false" :maskClosable="false">
        <screen-scale>
            <div class="modal_top">
                <p>{{ $t('保存算例') }}</p>
                <close-outlined class="pointer" @click="emit('close')" />
            </div>
            <a-spin :spinning="state.loading" size="large">
                <div class="modal_content relative">
                    <p>{{ $t('文件名称') }}</p>
                    <div class="modal_setting">
                        <a-input v-model:value="state.fileName" style="width: 360px" :placeholder="$t('请输入')" />
                    </div>
                    <div class="modal_btn">
                        <a-button @click="emit('close')">{{ $t('取消') }}</a-button>
                        <a-button @click="handleConfirm" type="primary" :style="{color: '#fff'}">{{ $t('确认') }}</a-button>
                    </div>
                </div>
            </a-spin>
        </screen-scale>
    </a-modal>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { onMounted, reactive } from 'vue'

import { useRoute } from 'vue-router'

import { CloseOutlined } from '@ant-design/icons-vue'
import { saveBaseDataApi } from '@/api/exampleApi'

const route = useRoute()

const props = defineProps({
	caseName: {
		type: String,
		default: ''
	},
	type: {
		type: String,
		default: ''
	}
})

const state = reactive({
	routePath: route.fullPath,
	visible: true,
	loading: false,
	fileName: props.caseName + Date.now()
})
const emit = defineEmits(['close', 'confirm'])
const closeModal = () => {
	emit('close')
}

const handleConfirm = () => {
	if (state.routePath !== route.fullPath) return
	if (state.fileName == '') return

	Mitt.emit('toDetailSave', props.type)
}

const saveNewFile = (val) => {
	if (state.routePath !== route.fullPath) return
	state.loading = true
	saveBaseDataApi({
		'import_string_func': 'teapcase:rename_tc_file',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'new_file_name': state.fileName
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			if (val == 'newBuilt') {
				emit('confirm', res.func_result.tc_file_name, res.func_result.tc_file_path)
			} else {
				Mitt.emit('handleUploadCase', res.func_result.tc_file_path)
				setTimeout(() => {
					emit('confirm', res.func_result.tc_file_name, res.func_result.tc_file_path)
				}, 1000)
			}
		}
		state.loading = false
	}).catch(() => {
		state.loading = false
	})
}
Mitt.on('saveNewFile', saveNewFile)

onMounted(() => {

})
</script>
<style lang="scss">
.modal_newBuilt{
    .ant-modal{
        width: auto!important;
        .ant-modal-body{
            >div{
                .modal_content{
                    padding: 10px 30px 90px;
                    >p{
                        font-size: 15px;
                    }
                    .modal_setting{
                        padding: 10px 0;
                        height: 55px;
                    }
                    .modal_upload{
                        padding: 10px 0;
                        height: 55px;
                    }
                }
            }
        }
    }
}
</style>

