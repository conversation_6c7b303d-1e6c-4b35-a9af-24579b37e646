<template>
  <a-modal wrapClassName="modal_time_series" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
      <screen-scale>
            <div class="modal_top">
                <p>{{ $t('时序导入') }}</p>
                <close-outlined class="pointer" @click="emit('close')" />
            </div>
            <a-spin :spinning="state.loading" size="large">
                <div class="modal_content relative">
                    <div>
                        <p>{{ $t('导入文件') }}：</p>
                        <div class="modal_time_upload">
                            <a-upload
                                v-model:file-list="state.fileList"
                                v-if="state.fileList.length==0"
                                name="file"
                                @change="handleChange"
                                accept=".xlsx"
                                :multiple="false"
                                :beforeUpload="()=>false"
                                :showUploadList="false"
                                >
                                <a-button>
                                <upload-outlined></upload-outlined>
                                {{ $t('点击上传时序曲线') }}
                                </a-button>
                            </a-upload>
                            <div class="modal_time_upload_name" v-else>
                                <p class="ellipsis">{{ state.fileList[0].name }}</p>
                                <close-outlined @click="removeFile" class="pointer" />
                            </div>
                            <a-popover overlayClassName="time_popover" :getPopupContainer="triggerNode=>{return triggerNode.parentNode}">
                                <template #content>
                                    <p>{{ $t('请在模板文件内修改') }}</p>
                                </template>
                                <a-button class="download_btn" v-if="state.isUpload" @click="downloadCsv" type="primary"><DownloadOutlined />{{ $t('下载摸板') }}</a-button>
                            </a-popover>
                        </div>
                    </div>
                    <div class="table_content" v-if="!state.isUpload">
                        <a-table
                            :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange,columnWidth:36 }"
                            :columns="columns"
                            :scroll="{y:300}"
                            rowKey="index"
                            :pagination="false"
                            :data-source="state.tableData"
                        />
                    </div>
                    <p class="total" v-if="!state.isUpload">{{ $t('已选') }} {{state.selectedRowKeys.length}} {{ $t('项') }}&nbsp;&nbsp;{{ $t('共') }}&nbsp;{{ state.tableData.length }}&nbsp;{{ $t('项') }}</p>
                </div>
                <div :class="state.isUpload?'modal_btn modal_btn_sp':'modal_btn'">
                    <a-button :disabled="state.fileList.length==0" v-if="state.isUpload" @click="confirm(1)" type="primary">{{ $t('确定') }}</a-button>
                    <a-button v-else :disabled="state.selectedRowKeys.length==0" @click="confirm(0)" type="primary">{{ $t('导入') }}</a-button>
                    <a-button @click="emit('close')">{{ $t('取消') }}</a-button>
                </div>
                <DialogBox :text="state.text" :type="1" @close="close" v-if="state.DialogShow" :data="state"></DialogBox>
            </a-spin>
      </screen-scale>
  </a-modal>
</template>
<script setup>
import { onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import message from '@/utils/message'
import { CloseOutlined } from '@ant-design/icons-vue'

import { DownloadTsUploadTemplateFile, UploadTsFile, InsertTsFile } from '@/api/index'
import { downloadApiFile, t } from '@/utils/common'
const route = useRoute()

const props = defineProps({
	isItemizedView: {
		type: Boolean,
		default: false
	},
	treeNode: {
		type: String,
		default: ''
	},
	treeNodeId: {
		type: String,
		default: ''
	}
})

const state = reactive({
	ifShow: true,
	loading: false,
	fileList: [],
	isUpload: true,
	selectedRowKeys: [],
	tableData: [],
	case_file_name: undefined,
	file_path: undefined,
	DialogShow: false
})

const columns = [
	{
		title: t('曲线名称'),
		dataIndex: 'name',
		key: 'name',
		width: 100,
		align: 'center'
	},
	{
		title: t('曲线类型'),
		dataIndex: 'type',
		key: 'type',
		width: 80,
		align: 'center'
	},
	{
		title: t('数据类型'),
		dataIndex: 'value_type',
		key: 'value_type',
		width: 80,
		align: 'center'
	},
	{
		title: t('计算场景'),
		dataIndex: 'scenario',
		key: 'scenario',
		width: 80,
		align: 'center'
	}
]

const emit = defineEmits(['close', 'refresh'])
const closeModal = () => {
	emit('close')
}
const close = (val) => {
	state.DialogShow = false
	if (val) {
		emit('refresh')
	}
}
const confirm = (val) => {
	state.loading = true
	if (val) {
		const formdata = new FormData()
		if (navigator.userAgent.includes('Electron')) {
			formdata.append('file_path', state.fileList[0].originFileObj.path)
		} else {
			formdata.append('file', state.fileList[0].originFileObj)
		}
		formdata.append('case_file_name', state.case_file_name)
		UploadTsFile(formdata).then(res => {
			message.success(res.message)
			state.tableData = res.data
			state.file_path = res.file_path
			state.isUpload = false
			state.loading = false
		}).catch(() => {
			state.loading = false
		})
	} else {
		InsertTsFile({
			case_file_name: state.case_file_name,
			index_list: state.selectedRowKeys,
			file_path: state.file_path,
			sheet_name: props.isItemizedView ? props.treeNode : null,
			row_id: props.isItemizedView ? Number(props.treeNodeId) : null
		}).then(res => {
			state.loading = false
			if (res.code == 1) {
				message.success(res.message)
				emit('refresh')
			} else {
				state.text = res.message
				state.DialogShow = true
			}
		}).catch(() => {
			state.loading = false
		})
	}
}
const onSelectChange = (selectedRowKeys, selectedRows) => {
	state.selectedRowKeys = selectedRowKeys
}
const removeFile = () => {
	state.fileList = []
	state.isUpload = true
}
const handleChange = () => {

}
const downloadCsv = () => {
	state.loading = true
	DownloadTsUploadTemplateFile({
		case_file_name: route.query.filePath
	}).then(res => {
		if (res.headers.warning_msg) {
			const warning_msg = decodeURIComponent(
				res.headers.warning_msg
			)
			message.warning(warning_msg, 3)
		}

		downloadApiFile(res)
		state.loading = false
	})
}
onMounted(() => {
	state.case_file_name = route.query.filePath
})
</script>
<style lang="scss">
    .modal_time_series{
        .ant-modal{
            width: auto!important;
            .ant-modal-body{
                >div{
                .modal_content{
                    padding: 20px 30px 90px;
                    >div:first-child{
                        display: flex;
                        align-items: center;
                        width: 550px;
                    }
                    .modal_time_upload{
                        display: flex;
                        align-items: center;
                        .modal_time_upload_name{
                            width: 280px;
                            display: flex;
                            justify-content: space-between;
                            border: 1px solid rgb(244, 244, 244);
                            border-radius: 5px;
                            padding: 5px;
                            p{
                                color: var(--base-color);
                                font-size: 15px;
                                line-height: 20px;
                            }
                            span{
                                font-size: 15px;
                                background-color: rgb(244, 244, 244);
                                padding: 3px;
                                border-radius: 5px;
                                &:hover{
                                    cursor: pointer;
                                }
                            }
                        }
                    }
                    .download_btn{
                    margin-left: 10px;
                    }
                    .table_content{
                        margin-top: 20px;
                        width: 700px;
                    }
                    .ant-table{
                        .ant-table-cell{
                            padding:5px 10px!important;
                            line-height: 26px;
                        }
                    }
                    .total{
                        position: absolute;
                        bottom: 35px;
                    }

                }
                .modal_btn_sp{
                    // width: 100%;
                    // display: flex;
                    // justify-content: center;
                    }
                }
            }
        }
    }
    .time_popover{
        .ant-popover-arrow{
            display: none;
        }
        .ant-popover-inner{
            background-color: rgb(254, 251, 234);
            color: #000;
            position: relative;
            bottom: -15px;
            border: 1px solid rgb(250, 173, 20);
        }
    }
    .custom-class{
        .ant-message-custom-content{
            >span:last-child{
                white-space:pre;
            }
        }
    }
</style>

