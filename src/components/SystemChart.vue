<template>
  <a-modal
    wrapClassName="system-chart-modal"
    :afterClose="closeModal"
    :centered="true"
    v-model:open="state.visible"
    :footer="null"
    :closable="false"
    width="70%"
    :maskClosable="false"
  >
    <div class="user-select"  :style="{zoom: state.zoom}">
      <div class="modal_top">
        <p>{{ $t('工作位置图设置') }}</p>
        <close-outlined class="pointer" @click="closeModal" />
      </div>

      <div class="modal_content">
        <div class="modal_content_table">
          <div class="area_table">
            <p>{{ $t('面积图') }}<i>（{{ $t('可拖拽调整位置）') }}</i></p>
            <a-table
              ref="scroll_area_table"
              class="ant-table-striped"
              size="middle"
              :columns="state.columns"
              :data-source="state.tableAreaData"
              :customRow="customRow"
              :row-selection="{ selectedRowKeys: state.selectedAreaRowKeys, onChange: onSelectAreaChange }"
              :scroll="{ y: 350 }"
              :row-class-name="(_record, index) => (index == state.areaIndex ? 'table-striped' : null)"
              :pagination="false"
              bordered
            >
              <template #bodyCell="{ column,record }">
                <template v-if="column.dataIndex === 'color'">
                  <input type="color" v-model="state.editableData[record.lineName]" @change="changeColor">
                </template>
                <template v-if="column.dataIndex === 'type'">
                  <a-select
                    v-model:value="state.editableTypeData[record.lineName]"
                    style="width: 120px"
                    @change="changeType(record.lineName,'area')"
                  >
                    <a-select-option value="solid_line">{{ $t('实线图') }}</a-select-option>
                    <a-select-option value="dashed_line">{{ $t('虚线图') }}</a-select-option>
                    <a-select-option value="dotted_line">{{ $t('点线图') }}</a-select-option>
                    <a-select-option value="area">{{ $t('面积图') }}</a-select-option>
                  </a-select>
                </template>
              </template>
            </a-table>
          </div>
          <div>
            <p>{{ $t('曲线图')}}</p>
            <a-table
              ref="scroll_line_table"
              class="ant-table-striped"
              size="middle"
              :columns="state.columns"
              :data-source="state.tableLineData"
              :row-selection="{ selectedRowKeys: state.selectedlineRowKeys, onChange: onSelectLineChange }"
              :scroll="{ y: 350 }"
              :row-class-name="(_record, index) => (index == state.lineIndex ? 'table-striped' : null)"
              :pagination="false"
              bordered
            >
              <template #bodyCell="{ column,record }">
                <template v-if="column.dataIndex === 'color'">
                  <input type="color" v-model="state.editableData[record.lineName]" @change="changeColor">
                </template>
                <template v-if="column.dataIndex === 'type'">
                  <a-select
                    v-model:value="state.editableTypeData[record.lineName]"
                    style="width: 120px"
                    @change="changeType(record.lineName,'line')"
                  >
                    <a-select-option value="solid_line">{{ $t('实线图') }}</a-select-option>
                    <a-select-option value="dashed_line">{{ $t('虚线图') }}</a-select-option>
                    <a-select-option value="dotted_line">{{ $t('点线图') }}</a-select-option>
                    <a-select-option value="area">{{ $t('面积图') }}</a-select-option>
                  </a-select>
                </template>
              </template>
            </a-table>
          </div>
        </div>

        <div class="modal_content_charts">
          <p>{{ $t('示例图') }}</p>
          <div ref="exampleChartRef" class="exampleChart" :style="`zoom:${state.zooms};transform:scale(${state.scales});transform-origin:0 0;width:${state.chartWidth};height:${state.height}px`"></div>
        </div>

      </div>
      <div class="modal_btns_box">
        <a-button @click="confirm()" type="primary">{{ $t('确认') }}</a-button>
        <a-button @click="closeModal()">{{ $t('取消') }}</a-button>
        <a-button @click="restoreDefault()" type="primary" ghost>{{ $t('恢复默认') }}</a-button>
        <a-button @click="download()" type="primary">{{ $t('下载模版') }}</a-button>
        <a-upload
          v-model:fileList="state.fileList"
          name="file"
          accept=".json"
          @change="changeJson"
          :multiple="false"
          :beforeUpload="()=>false"
          :showUploadList="false"
          :maxCount="1"
        >
          <a-button type="primary" ghost>{{ $t('上传模版') }}</a-button>
        </a-upload>
      </div>

    </div>
  </a-modal>
</template>
<script setup>
import { ref, reactive, defineEmits, onMounted, inject, markRaw, nextTick, onUnmounted } from 'vue'
import { getExampleSeries } from '@/utils/teap.js'
import { debounce } from '@/utils/gis.js'
import { t } from '@/utils/common.js'
import { settingStore } from '@/store/settingStore'
import { storeToRefs } from 'pinia'

const store = settingStore()
const { add_watermark, watermark_text, isChromeHigh } = storeToRefs(store)

const echarts = inject('ec')
const exampleChartRef = ref()
const exampleChart = ref()

const scroll_area_table = ref()
const scroll_line_table = ref()

const props = defineProps({
	data: {
		type: Array,
		default: () => []
	},
	default_data: {
		type: Array,
		default: () => []
	},
	exampleData: {
		type: Object,
		default: () => {}
	}
})

const emits = defineEmits(['cancel', 'confirm', 'download'])

const state = reactive({
	visible: true,
	isMultiple: 'multiple',
	columns: [

		{
			title: t('名称'),
			dataIndex: 'name',
			key: 'name',
			align: 'center',
			ellipsis: true
		},
		{
			title: t('线型'),
			dataIndex: 'type',
			key: 'type',
			align: 'center',
			ellipsis: true
		},
		{
			title: t('颜色'),
			dataIndex: 'color',
			key: 'color',
			align: 'center',
			ellipsis: true
		}
	],
	default_data: [],
	tableData: [],
	tableLineData: [],
	tableAreaData: [],
	selectedRowKeys: [],
	selectedAreaRowKeys: [],
	selectedlineRowKeys: [],
	editableData: {},
	editableTypeData: {},
	startDraggleRow: null,
	startIndex: 0,
	endIndex: 0,
	startZoom: 0,
	endZoom: undefined,
	fileList: [],
	areaIndex: null,
	lineIndex: null,
	zoom: 1,
	zooms: 1,
	scales: 1,
	height: 380,
	chartWidth: `${1920 / (document.documentElement.offsetWidth || document.body.offsetWidth) * 100}%`
})

const customRow = (record, index) => {
	return {
		style: {
			cursor: 'move'
		},

		onMouseenter: (event) => {
			var ev = event || window.event
			ev.target.draggable = true
		},

		onDragstart: (event) => {
			var ev = event || window.event

			ev.stopPropagation()

			state.startDraggleRow = record
		},

		onDragover: (event) => {
			var ev = event || window.event

			ev.preventDefault()
		},

		onDrop: (event) => {
			var ev = event || window.event

			ev.stopPropagation()

			const twmpTableAreaData = JSON.parse(JSON.stringify(state.tableAreaData))
			const oldIndex = state.tableAreaData.findIndex(item => item.lineName === state.startDraggleRow.lineName)
			const newIndex = state.tableAreaData.findIndex(item => item.lineName === record.lineName)
			const elementToMove = state.tableAreaData.splice(oldIndex, 1)[0]
			state.tableAreaData.splice(newIndex, 0, elementToMove)
			state.tableAreaData.forEach((item, index) => item.sequence = twmpTableAreaData[index].sequence)
			state.tableData = state.tableLineData.concat(state.tableAreaData).sort((a, b) => a.sequence - b.sequence)
			initMidTermEcharts()
		}
	}
}

const onSelectAreaChange = selectedRowKeys => {
	state.selectedAreaRowKeys = selectedRowKeys
	state.selectedRowKeys = state.selectedAreaRowKeys.concat(state.selectedlineRowKeys)
	initMidTermEcharts()
}

const onSelectLineChange = selectedRowKeys => {
	state.selectedlineRowKeys = selectedRowKeys
	state.selectedRowKeys = state.selectedAreaRowKeys.concat(state.selectedlineRowKeys)
	initMidTermEcharts()
}

const changeType = (val, type) => {
	state.tableData.forEach(item => {
		item.type = state.editableTypeData[item.lineName]
	})
	state.tableLineData = state.tableData.filter(item => item.type !== 'area').sort((a, b) => a.sequence - b.sequence)
	state.tableAreaData = state.tableData.filter(item => item.type == 'area').sort((a, b) => a.sequence - b.sequence)
	if (state.selectedRowKeys.includes(val)) {
		if (state.tableLineData.find(item => item.lineName == val)) {
			state.selectedAreaRowKeys = state.selectedAreaRowKeys.filter(item => item !== val)
			state.selectedlineRowKeys.push(val)
		} else {
			state.selectedlineRowKeys = state.selectedlineRowKeys.filter(item => item !== val)
			state.selectedAreaRowKeys.push(val)
		}
	}

	if (type == 'area') {
		state.lineIndex = state.tableLineData.findIndex(item => item.lineName == val)
		state.areaIndex = null
		nextTick(() => {
			scroll_line_table.value.$el.querySelector('.ant-table-body').scrollTop = 40 * state.lineIndex
		})
	} else if (type == 'line') {
		if (state.editableTypeData[val] == 'area') {
			state.areaIndex = state.tableAreaData.findIndex(item => item.lineName == val)
			state.lineIndex = null
			nextTick(() => {
				scroll_area_table.value.$el.querySelector('.ant-table-body').scrollTop = 40 * state.areaIndex
			})
		}
	}

	initMidTermEcharts()
}

const changeColor = () => {
	state.tableData.forEach(item => {
		item.color = state.editableData[item.lineName]
	})
	state.tableLineData = state.tableData.filter(item => item.type !== 'area').sort((a, b) => a.sequence - b.sequence)
	state.tableAreaData = state.tableData.filter(item => item.type == 'area').sort((a, b) => a.sequence - b.sequence)
	initMidTermEcharts()
}

const initMidTermEcharts = () => {
	const option = getExampleSeries(JSON.parse(JSON.stringify(props.exampleData)), state.tableData, state.selectedRowKeys, add_watermark.value, watermark_text.value, state.startIndex, state.startZoom, state.endZoom)
	exampleChart.value.setOption(option, true)
	exampleChart.value.on('datazoom', (event) => {
		state.startZoom = event.start
		state.endZoom = event.end
	})
}

const restoreDefault = () => {
	state.tableData = JSON.parse(JSON.stringify(state.default_data))
	state.tableLineData = state.tableData.filter(item => item.type !== 'area').sort((a, b) => a.sequence - b.sequence)
	state.tableAreaData = state.tableData.filter(item => item.type == 'area').sort((a, b) => a.sequence - b.sequence)
	state.tableData.forEach(item => {
		state.editableData[item.lineName] = item.color
		state.editableTypeData[item.lineName] = item.type
	})

	state.selectedAreaRowKeys = state.selectedRowKeys.filter(item => state.tableAreaData.find(items => items.lineName == item))
	state.selectedlineRowKeys = state.selectedRowKeys.filter(item => state.tableLineData.find(items => items.lineName == item))

	initMidTermEcharts()
}

const download = () => {
	emits('download', state.tableData)
}

const changeJson = async({ file, fileList }) => {
	if (fileList.length == 0) return

	const reader = new FileReader()
	reader.readAsText(file)
	const balanceJsonText = await new Promise((resolve, reject) => {
		reader.onload = (e) => {
			try {
				resolve(e.target.result)
			} catch (error) {
				reject(error)
			}
		}
	})
	const jsonData = JSON.parse(balanceJsonText)

	state.tableData = Object.keys(jsonData.columns).filter(item => item != 'Time').map((item, index) => Object.assign({ lineName: item, key: item }, jsonData.columns[item])).sort((a, b) => a.sequence - b.sequence)
	state.tableLineData = state.tableData.filter(item => item.type !== 'area')
	state.tableAreaData = state.tableData.filter(item => item.type == 'area')
	state.tableData.forEach(item => {
		state.editableData[item.lineName] = item.color
		state.editableTypeData[item.lineName] = item.type
	})

	initMidTermEcharts()
}

const closeModal = () => {
	emits('cancel')
}

const confirm = (id) => {
	emits('confirm', state.tableData)
}

const screenScale = () => {
	let root
	if (isChromeHigh.value) {
		root = document.getElementsByClassName('home-body')[0].style.zoom || 1
		state.zoom = root
	} else {
		root = document.body.style.zoom
	}
	state.zooms = 1 / root
	state.scales = root
	state.height = 380 * root
}
const debouncedScreenScale = debounce(screenScale, 200)
onUnmounted(() => {
	window.removeEventListener('resize', debouncedScreenScale)
})

onMounted(() => {
	window.addEventListener('resize', debouncedScreenScale)

	exampleChart.value = markRaw(echarts.init(exampleChartRef.value))
	state.tableData = JSON.parse(JSON.stringify(props.data)).sort((a, b) => a.sequence - b.sequence)
	state.default_data = JSON.parse(JSON.stringify(props.default_data)).sort((a, b) => a.sequence - b.sequence)

	state.tableLineData = state.tableData.filter(item => item.type !== 'area')
	state.tableAreaData = state.tableData.filter(item => item.type == 'area')

	state.tableData.forEach(item => {
		state.editableData[item.lineName] = item.color
		state.editableTypeData[item.lineName] = item.type
	})

	screenScale()

	initMidTermEcharts()
})

</script>
<style lang="scss" scoped>
  .system-chart-modal{
    .ant-modal{
      // height: 80%!important;
      .ant-modal-body{
        >div{
          .modal_content{

            padding: 17px 35px;
            text-align: center;
            [data-doc-theme='light'] .ant-table-striped :deep(.table-striped) td {
              background-color: #fafafa;
            }
            [data-doc-theme='dark'] .ant-table-striped :deep(.table-striped) td {
              background-color: rgb(29, 29, 29);
            }
            .modal_content_table {
              display: grid;
              grid-template-columns: 1fr 1fr;
              grid-gap: 20px;
              >div {
                width: 100%;
              }
              i {
                font-size: 12px;
                color: #666;
              }
              .area_table :deep(.ant-table-tbody .ant-table-row ){
                box-shadow: 0 2px 4px 1px rgba(0, 0, 0, 0.2);
                // -5px -5px 15px rgba(0, 0, 0, 0.3);
              }

              .area_table :deep(.ant-table-tbody .ant-table-row-selected ){
                // box-shadow: 0 2px 4px 1px rgba(0, 0, 0, 0.2);
                background-image: linear-gradient( rgb(242, 244, 247),rgb(201, 204, 216));
                // background-color: rgb(226, 211, 211)!important;
              }
              .area_table :deep(.ant-table-tbody .ant-table-row-selected td ){
                // box-shadow: 0 2px 14px 1px rgba(242, 22, 22, 0.2);
                // background-image: linear-gradient( rgb(242, 244, 247),rgb(201, 204, 216));
                background-color: transparent!important;
              }

              :deep(.ant-table-tbody .ant-table-cell-row-hover ){
                background-image: linear-gradient(#ebd8d8, #ebd8d8)
              }
              // :deep(.ant-table-tbody tr:hover td) {
              //   background-color: #e9e6e6 !important;
              // }

              :deep(.ant-table-tbody tr td) {
                padding: 3px;
              }

              .ant-table-striped :deep(.table-striped) td {
                animation: highlight 3s 1;
              }

              @keyframes highlight {
                0% {
                  background-color: #f6cbcb;
                }
                100% {
                  background-color: transparent;
                }
              }

            }
            .modal_content_charts {
              width: 100%;
              height: 390px;

              // .exampleChart {
                // height: calc(100% - 16px);
                // width: 100%;
                // transform-origin: 0 0;

              // }
            }
          }
          .modal_btns_box{
            height: 35px;
            // margin-top: 17px;
            text-align: center;
            button{
              min-width: 90px;
              height: 30px;
              letter-spacing: 0;
              margin: 0 5px;
            }
          }
        }
      }
    }
  }
</style>
