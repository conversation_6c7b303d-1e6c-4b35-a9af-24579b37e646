import axios from "axios";
import message from "@/utils/message";
import network from "@/config/teap.config";
import { storeToRefs } from "pinia";
import { loadingStore } from "@/store/loadingStore";

import { routeStore } from "@/store/routeStore";
import router from "@/router/index";
import { basicApi } from "@/api/exampleApi";
import { throttle } from "@/utils/gis";
import { t } from "@/utils/common";
const cancelToken = axios.CancelToken;

const request = axios.create({
  baseURL: network.baseURL,

  headers: {
    "Content-Type": network.contentType,
  },
});
const handleLostFile = (url, response) => {
  url == "/backend/teap_api/"
    ? message.error(
        response.data.message || t("Request failed, please try again"),
        0
      )
    : message.error(
        response.data.message || t("Request failed, please try again")
      );
  const storeRoute = routeStore();
  const { routeTabs, activeKey, routeCache } = storeToRefs(storeRoute);
  basicApi({
    import_string_func: "teapcase:check_file_is_exist",
    func_arg_dict: {
      file_names: routeTabs.value.map((item) => item.filePath),
    },
  }).then((res) => {
    if (res.code === 1) {
      routeTabs.value = routeTabs.value.filter((item) =>
        res.func_result.includes(item.filePath)
      );
      if (routeTabs.value.length === 0) {
        router.push("/");
        routeCache.value = [];
        activeKey.value = "";
      } else {
        activeKey.value = routeTabs.value[0].key;
        router.push(activeKey.value);
      }
    }
  });
};
const throttledHandleLostFile = throttle(handleLostFile, 2000);
request.interceptors.request.use(
  (config) => {
    const storeLoading = loadingStore();
    const { loadingTimeData, loadingTime } = storeToRefs(storeLoading);
    config.metadata = { startTime: new Date() };
    if (loadingTimeData.value[config.url] && config.isShowLoading) {
      loadingTime.value = loadingTimeData.value[config.url];
    }
    if (config.isShowLoading) {
      storeLoading.showModal();
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

request.interceptors.response.use(
  (response) => {
    const storeLoading = loadingStore();
    const { loadingTimeData } = storeToRefs(storeLoading);
    response.config.metadata.endTime = new Date();
    loadingTimeData.value[response.config.url] =
      (response.config.metadata.endTime - response.config.metadata.startTime) /
      1000;
    const config = response.config;
    const url = config.url;

    if (url.indexOf("topo_graph") > -1) return response;
    if (url.indexOf("get_task_detail_result") > -1) return response.data;
    if (network.successCode.includes(response.data.code)) {
      return response.data;
    } else if (network.errorCode.includes(response.data.code)) {
      if (
        response.data.error_code &&
        response.data.error_code === network.lostFileCode
      ) {
        throttledHandleLostFile(url, response);
      } else {
        url == "/backend/teap_api/"
          ? message.error(response.data.message || t("请求出错，请重试"), 0)
          : message.error(response.data.message || t("请求出错，请重试"));
      }
      return Promise.reject(new Error(response.data.message || "Error"));
    } else {
      return response;
    }
  },
  (error) => {
    if (error.code == "ERR_CANCELED") {
      return;
    }
    message.error(t("请求出错，请重试"));
    return Promise.reject(error);
  }
);
export { request, cancelToken };
