import { request } from '@/request'

export const basicApi = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

export const getBaseDataApi = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

export const saveBaseDataApi = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		isShowLoading,
		data
	})
}

export const getModifyDataApi = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

export const checkDataApi = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

export const globalParameters = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

export const SaveParameters = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

export const createEmptyHdf = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		isShowLoading,
		data
	})
}

export const getTreeMenu = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

export const getReadNameCol = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

export const getReadOneRow = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

export const paramsReplenApi = data => {
	return request({
		method: 'POST',
		url: '/backend/api/',
		data
	})
}

export const filterBusNameApi = data => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/filter_name/',
		data
	})
}

export const filterTimeNameApi = data => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/filter_timeseries/',
		data
	})
}

export const topoGraph = data => {
	return request({
		method: 'GET',
		url: '/backend/teap_api_v3/topo_graph/',
		params: data

	})
}

export const getTimeseriesApi = data => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/parse_timeseries/',
		data
	})
}

export const islandCheckApi = data => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/topo_diagnostic/',
		data
	})
}

export const getDetailResultApi = (id, data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/get_task_detail_result/' + id + '/',
		data
	})
}
export const getResultApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/get_task_result/',
		data
	})
}

export const UploadCaseFile = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/upload_case_file/',
		data
	})
}

export const GetMidTermTaskResult = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/get_mid_term_task_result/',
		data
	})
}

export const DownloadTaskResult2 = id => {
	return request({
		method: 'POST',
		url: `/backend/teap_api/download_task_result_2/${id}/`,

		responseType: 'blob'
	})
}

export const DownloadCaseApi = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: `/backend/teap_api_v3/download_file/`,
		data,
		isShowLoading,
		responseType: 'blob'
	})
}

export const importXl = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/import_xl/',
		data,
		isShowLoading,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}

export const exportXl = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: `/backend/teap_api_v3/export_xl/`,
		data,
		isShowLoading,
		headers: {
			'Content-Type': 'multipart/form-data'
		},
		responseType: 'blob'
	})
}

export const GetH5FromTeapFile = data => {
	return request({
		method: 'POST',
		url: `/backend/teap_api_v3/get_tc_from_tr/`,
		data,
		responseType: 'blob'
	})
}

export const getTcFromTr = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/get_tc_from_tr/',
		isShowLoading,
		data
	})
}

export const deviceInitParam = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/device_init_param/query/',
		data
	})
}

export const updateDeviceInitParam = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/device_init_param/update/',
		data
	})
}

export const resetDeviceInitParam = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/device_init_param/reset/',
		data
	})
}

export const splitCaseApi = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/split_case/',
		data
	})
}
