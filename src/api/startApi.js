import { request } from '@/request'

export const importApi = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/import_case/',
		data,
		isShowLoading,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
export const mergeCase = (data, isShowLoading) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api_v3/merge_case/',
		data,
		isShowLoading,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}

export const uploadBPAApi = (params, data) => {
	return request({
		method: 'POST',
		url: '/backend/upload_bpa_file/',
		params,
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}

export const GetAllConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/bpa_ana_config/get_all_config/',
		data
	})
}
export const DelConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/bpa_ana_config/del_config/',
		data
	})
}
export const AddConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/bpa_ana_config/add_config/',
		data
	})
}
export const UpdateConfig = (data) => {
	return request({
		method: 'POST',
		url: '/backend/teap_api/bpa_ana_config/update_config/',
		data
	})
}
