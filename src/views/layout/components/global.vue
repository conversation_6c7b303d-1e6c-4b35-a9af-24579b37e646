<!-- 可能存在问题 -->
<template>
    <div class="global-body">
        <a-spin :spinning="state.loading" size="large" :tip="$t('Loading')">
            <div class="global-tabs">
                <a-tabs v-model:activeKey="globalActiveKey">
                    <a-tab-pane key="mid_term" :tab="state.sim_job_config.mid_term.long_name"
                        v-if="state.permissionList.includes('mid_term')"></a-tab-pane>
                    <a-tab-pane key="long_term" :tab="state.sim_job_config.long_term.long_name"
                        v-if="state.permissionList.includes('long_term')"></a-tab-pane>
                    <a-tab-pane key="capacity_balance" :tab="state.sim_job_config.capacity_balance.long_name"
                        v-if="state.permissionList.includes('capacity_balance')"></a-tab-pane>
                    <a-tab-pane key="short_term" :tab="state.sim_job_config.short_term.long_name"
                        v-if="state.permissionList.includes('short_term')"></a-tab-pane>
                </a-tabs>
            </div>

            <long-term @reset="handleReset" @saveSetting="saveSetting" v-show="globalActiveKey == 'long_term'"
                :isReady="state.isReady" :state="state" :stateDefault="stateDefault" :stateSetting="stateSetting"
                :stateAuto="stateAuto" :stateBase="stateBase"></long-term>

            <mid-term @reset="handleReset" @saveSetting="saveSetting" v-show="globalActiveKey == 'mid_term'"
                :isReady="state.isReady" :state="state" :stateDefault="stateDefault" :stateSetting="stateSetting"
                :stateAuto="stateAuto" :stateBase="stateBase"></mid-term>

            <short-term @reset="handleReset" @saveSetting="saveSetting" v-show="globalActiveKey == 'short_term'"
                :isReady="state.isReady" :state="state" :stateDefault="stateDefault" :stateSetting="stateSetting"
                :stateAuto="stateAuto" :stateBase="stateBase"></short-term>

            <capacity-balance @reset="handleReset" @saveSetting="saveSetting"
                v-show="globalActiveKey == 'capacity_balance'" :isReady="state.isReady"
                :globalActiveKey="globalActiveKey" :state="state" :stateSetting="stateSetting"></capacity-balance>
        </a-spin>
    </div>
    <a-modal v-if="state.visible" wrapClassName="modal_confirm" :afterClose="closeModal" :centered="true"
        v-model:open="state.visible" :footer="null" :closable="false" width="475px" :maskClosable="false">
        <div class="user-select">
            <div class="modal_top">
                <p>{{ $t('提示') }}</p>
                <close-outlined class="pointer" @click="handleCancel" />
            </div>
            <div class="modal_content relative">
                <img src="@/assets/icon/confirmIcon.png" alt="">
                <p>{{ $t('算例文件存在还未保存的改动，是否保存并开始仿真') }}？</p>
                <div class="modal_btns">
                    <a-button @click="handleCancel" size="small">{{ $t('取消') }}</a-button>
                    <a-button @click="handleOk('start')" type="primary" :style="{ color: '#fff' }" size="small">{{
                        $t('直接开始仿真') }}</a-button>
                    <a-button @click="handleOk('saveAndStart')" type="primary" ghost size="small">{{ $t('保存并开始仿真')
                        }}</a-button>
                </div>
            </div>
        </div>
    </a-modal>
</template>
<script setup>
import Mitt from '@/utils/mitt.js'
import { useRoute } from 'vue-router'
import { ref, reactive, onMounted, nextTick } from 'vue'
import message from '@/utils/message'
import { storeToRefs } from 'pinia'
import { routeStore } from '@/store/routeStore'
import { settingStore } from '@/store/settingStore'
import { globalParameters, SaveParameters, UploadCaseFile } from '@/api/exampleApi'
import { t } from '@/utils/common.js'

const route = useRoute()
const store = routeStore()
const { activeKey, routeTabs } = storeToRefs(store)

const storeSetting = settingStore()
const { permissionList, appTheme, recommended_parallel_number, sim_job_config } = storeToRefs(storeSetting)

const emit = defineEmits(['cancel', 'confirm'])
const globalActiveKey = ref(permissionList.value[0])
const handleTabChange = (val) => {
    if (state.routePath !== route.fullPath) return
    if (val == 'allTabs') return
    globalActiveKey.value = val
}
Mitt.on('handleTabChange', handleTabChange)

const handleScene = (val) => {
    stateSetting.case_info.scenario_selected = val
}
Mitt.on('handleScene', handleScene)
const state = reactive({
    visible: false,
    isReady: false,
    loading: false,
    routePath: route.fullPath,
    actionsId: null,
    calculateType: '',
    solution_method_long: 'partial',
    solution_method: appTheme.value !== 'TEAPZJ' ? 'partial' : 'simulate',
    scenariOptions: [],
    hydropowerOptions: [],
    permissionList,
    appTheme,
    recommended_parallel_number,
    sim_job_config,
    simulation_long_time: [],
    simulation_mid_time: [],
    simulation_short_time: [],
    simulation_capacity_time: []
})
const stateSetting = reactive({
    'case_info': {

    },
    'long_term': {
        'simulation': {
        },
        'partial': {
        },
        'fitted': {
        }
    },
    'mid_term': {
        'simulation': {
        },
        'partial': {
        },
        'fitted': {
        }
    },
    'short_term': {
        'simulation': {
        },
        'scuc': {
        }
    },
    'capacity_balance': {
        'general': {
        }
    }
})
const stateDefault = ref({
    'case_info': {
    },
    'general_parameters': {

    },
    'long_term': {
        'simulation': {
        },
        'partial': {
        },
        'fitted': {
        }
    },
    'mid_term': {
        'simulation': {
        },
        'partial': {
        },
        'fitted': {
        }
    },
    'short_term': {
        'simulation': {
        },
        'scuc': {
        }
    },
    'capacity_balance': {
        'general': {
        }
    }
})
const stateAuto = reactive({
    load_relax_cost_auto: true,
    reserve_relax_cost_auto: true,
    branch_relax_cost_auto: true,
    wind_curtailment_cost_auto: true,
    solar_curtailment_cost_auto: true,
    water_curtailment_cost_auto: true,
    load_dec_max_cost_auto: true,
    reserve_dec_max_cost_auto: true,
    coe_of_emerg_up_resv_relax_pen_auto: true,
    load_increase_limit_percentage_auto: true
})
const stateBase = ref({

})

const handleReset = e => {
    getGlobalParameters()
}

const getGlobalParameters = () => {
    state.isReady = false
    if (state.routePath !== route.fullPath) return
    state.loading = true
    globalParameters(
        {
            'import_string_func': 'teapcase:read_from_tc',
            'func_arg_dict': {
                'file_name': route.query.filePath,
                'sheet_name': 'parameter'
            }
        }
    ).then(res => {
        if (res.code == 1) {
            const { data } = res.func_result.parameter
            const { default_parameter } = res.func_result
            stateDefault.value = default_parameter.data
            state.scenariOptions = res.func_result.scenario.data.map(item => {
                return {
                    value: item,
                    label: item
                }
            })
            state.hydropowerOptions = res.func_result.hydropower_scenario.data.map(item => {
                return {
                    value: item,
                    label: item
                }
            })
            stateBase.value = data.general_parameters
            stateSetting.case_info = data.case_info
            stateSetting.long_term = data.long_term
            stateSetting.mid_term = data.mid_term
            stateSetting.short_term = data.short_term
            stateSetting.case_info.scenario_selected = res.func_result.scenario.data.includes(data.case_info.scenario_selected) ? data.case_info.scenario_selected : t('（空）')
            if (data.capacity_balance) {
                stateSetting.capacity_balance = data.capacity_balance
                state.simulation_capacity_time = [data.capacity_balance.general.start_datetime, data.capacity_balance.general.end_datetime]
            }
            if (res.func_result.hydropower_scenario.data <= 0) {
                stateSetting.case_info.hydropower_scenario = ''
            } else if (!res.func_result.hydropower_scenario.data.includes(data.case_info.hydropower_scenario)) {
                res.func_result.hydropower_scenario.data.includes(t('枯水年')) ? stateSetting.case_info.hydropower_scenario = t('枯水年') : stateSetting.case_info.hydropower_scenario = ''
            }
            state.simulation_long_time = [data.long_term.simulation.start_datetime.split(' ')[0], data.long_term.simulation.end_datetime.split(' ')[0]]
            state.simulation_mid_time = [data.mid_term.simulation.start_datetime.split(' ')[0], data.mid_term.simulation.end_datetime.split(' ')[0]]
            state.simulation_short_time = [data.short_term.simulation.start_datetime.split(' ')[0], data.short_term.simulation.end_datetime.split(' ')[0]]

            stateAuto.load_relax_cost_auto = data.general_parameters.load_relax_cost == 'AUTO'
            stateAuto.reserve_relax_cost_auto = data.general_parameters.reserve_relax_cost == 'AUTO'
            stateAuto.branch_relax_cost_auto = data.general_parameters.branch_relax_cost == 'AUTO'
            stateAuto.wind_curtailment_cost_auto = data.general_parameters.wind_curtailment_cost == 'AUTO'
            stateAuto.solar_curtailment_cost_auto = data.general_parameters.solar_curtailment_cost == 'AUTO'
            stateAuto.water_curtailment_cost_auto = data.general_parameters.water_curtailment_cost == 'AUTO'
            stateAuto.load_dec_max_cost_auto = data.general_parameters.load_dec_max_cost == 'AUTO'
            stateAuto.reserve_dec_max_cost_auto = data.general_parameters.reserve_dec_max_cost == 'AUTO'
            stateAuto.coe_of_emerg_up_resv_relax_pen_auto = data.general_parameters.coe_of_emerg_up_resv_relax_pen == 'AUTO'
            stateAuto.coe_of_emerg_up_resv_relax_pen_auto = data.general_parameters.coe_of_emerg_up_resv_relax_pen == 'AUTO'
            stateAuto.load_increase_limit_percentage_auto = data.short_term.simulation.load_increase_limit_percentage == 'AUTO'
        }
        state.isReady = true
        state.loading = false
    })
}
const saveSetting = (id, val, { stateCase, stateBase, stateAuto, stateMore }) => {
    if (state.routePath !== route.fullPath) return
    state.calculateType = val
    if (!id) return handleGlobalSave(val)
    state.actionsId = id

    const isSaved = routeTabs.value.find(item => item.key == activeKey.value).isSaved
    if (id && !isSaved) {
        state.visible = true
    } else {
        handleGlobalSave('savedToStart')
    }
}
const handleCancel = () => {
    state.visible = false
    state.actionsId = null
    state.calculateType = ''
    state.loading = false
}

const handleOk = (val) => {
    state.visible = false
    handleGlobalSave(val)
}
const handleGlobalSave = (val) => {
    if (state.routePath !== route.fullPath) return
    state.loading = true
    let tempType = state.calculateType
    if (val && val.type == 'saveAs') {
        tempType = globalActiveKey.value
    } else {
        tempType = state.calculateType == '' ? globalActiveKey.value : state.calculateType
    }
    const params = {}
    stateBase.value.load_relax_cost = stateAuto.load_relax_cost_auto ? 'AUTO' : !stateBase.value.load_relax_cost ? 0 : stateBase.value.load_relax_cost
    stateBase.value.reserve_relax_cost = stateAuto.reserve_relax_cost_auto ? 'AUTO' : !stateBase.value.reserve_relax_cost ? 0 : stateBase.value.reserve_relax_cost
    stateBase.value.branch_relax_cost = stateAuto.branch_relax_cost_auto ? 'AUTO' : !stateBase.value.branch_relax_cost ? 0 : stateBase.value.branch_relax_cost
    stateBase.value.wind_curtailment_cost = stateAuto.wind_curtailment_cost_auto ? 'AUTO' : !stateBase.value.wind_curtailment_cost ? 0 : stateBase.value.wind_curtailment_cost
    stateBase.value.solar_curtailment_cost = stateAuto.solar_curtailment_cost_auto ? 'AUTO' : !stateBase.value.solar_curtailment_cost ? 0 : stateBase.value.solar_curtailment_cost
    stateBase.value.water_curtailment_cost = stateAuto.water_curtailment_cost_auto ? 'AUTO' : !stateBase.value.water_curtailment_cost ? 0 : stateBase.value.water_curtailment_cost
    stateBase.value.load_dec_max_cost = stateAuto.load_dec_max_cost_auto ? 'AUTO' : !stateBase.value.load_dec_max_cost ? 0 : stateBase.value.load_dec_max_cost
    stateBase.value.reserve_dec_max_cost = stateAuto.reserve_dec_max_cost_auto ? 'AUTO' : !stateBase.value.reserve_dec_max_cost ? 0 : stateBase.value.reserve_dec_max_cost
    stateBase.value.coe_of_emerg_up_resv_relax_pen = stateAuto.coe_of_emerg_up_resv_relax_pen_auto ? 'AUTO' : !stateBase.value.coe_of_emerg_up_resv_relax_pen ? 0 : stateBase.value.coe_of_emerg_up_resv_relax_pen

    if (tempType == 'long_term') {
        params.long_term = stateSetting.long_term
    } else if (tempType == 'mid_term') {
        params.mid_term = stateSetting.mid_term
    } else if (tempType == 'short_term') {
        stateSetting.short_term.simulation.load_increase_limit_percentage = stateAuto.load_increase_limit_percentage_auto ? 'AUTO' : !stateSetting.short_term.simulation.load_increase_limit_percentage ? 0 : stateSetting.short_term.simulation.load_increase_limit_percentage
        params.short_term = stateSetting.short_term
    } else if (tempType == 'capacity_balance') {
        params.capacity_balance = stateSetting.capacity_balance
        Mitt.emit('updataAgTable')
    }
    params.case_info = stateSetting.case_info
    params.general_parameters = stateBase.value
    SaveParameters({
        'import_string_func': 'teapcase:write_to_tc',
        'func_arg_dict': {
            'file_name': route.query.filePath,
            'sheet_name': 'parameter',
            'data': { ...params },
            'ignore_temp_save': val == 'saveAndStart'
        }
    }).then(res => {
        if (res.code == 1) {
            state.loading = false
            if ((val && val.type == 'saveAs') || val == 'ignoreTempSave') {
                Mitt.emit('handleGlobalSaveas', val)
                return
            }
            if (state.actionsId) {
                if (val == 'savedToStart') {
                    const tempQuery = route.query.type
                    if (tempQuery == 'isNewBuilt' || tempQuery == 'isResultBuilt') {
                        Mitt.emit('handleActionBar', 'globalToSave')
                    } else {
                        nextTick(() => {
                            handleUploadCase()
                        })
                    }
                } else {
                    val == 'saveAndStart' ? Mitt.emit('handleActionBar', 'globalToSave') : Mitt.emit('handleActionBar', 'globalToSaveTemp')
                }
            } else {
                state.loading = false
                message.success(res.msg || t('保存成功'))
            }
        }
    }).catch(() => {
        state.loading = false
    })
}
Mitt.on('handleGlobalSave', handleGlobalSave)

const handleUploadCase = (path) => {
    if (state.routePath !== route.fullPath) return
    if (!state.actionsId) return
    UploadCaseFile({
        tc_file_name: path || route.query.filePath,
        job_type_id: state.actionsId,
        case_file_note: stateSetting.case_info.description,
        chronology_reduction_method: state.actionsId == 4 ? state.solution_method : state.actionsId == 5 ? state.solution_method_long : state.actionsId == 3 ? 'scuc' : ''
    }).then(res => {
        if (res.code == 1) {
            state.actionsId = null
            state.calculateType = ''
            message.success(res.msg || t('已添加到计算任务'))
            Mitt.emit('taskListOpen')
            emit('cancel')
        }
        state.actionsId = null
        state.calculateType = ''
        state.loading = false
    }).catch(() => {
        state.actionsId = null
        state.calculateType = ''
        state.loading = false
    })
}
Mitt.on('handleUploadCase', handleUploadCase)
defineExpose({ handleTabChange, handleGlobalSave })
onMounted(() => {
    getGlobalParameters()
})
</script>
<style lang="scss" scoped>
.global-body {
    width: 1350px;
    height: 96%;
    border: 1px solid #A7BCD7;
    border-radius: 6px;
    background-color: var(--theme-bg-color);
    position: relative;

    .activeClass {
        animation: blinking 1s 1;
    }

    @keyframes blinking {
        0% {
            box-shadow: transparent;
        }

        50% {
            box-shadow: #2C97F9 0 0 8px 0;
        }

        100% {
            box-shadow: transparent;
        }
    }

    .global-tabs {
        width: 100%;
        height: 30px;
        padding: 0 30px;
        // background-color: #F6F8FA;
        background-color: var(--theme-tabs-color);
        border-bottom: 1px solid #A7BCD7;
        border-radius: 6px 6px 0 0;
    }

    &:deep(.global-main-plan) {
        position: relative;
        width: 100%;
        height: calc(100% - 38px);
        padding: 15px 35px;
        // font-size: 12px;
        @include add-size(12px, $size);
        color: #474747;
        display: flex;
        justify-content: space-between;
        overflow: auto;

        .main-left {
            >p {
                font-size: 14px;
                line-height: 18px;
                font-family: 'SiYuan Medium', Serif;
            }

            .parameterSet {
                width: 448px;
                height: 70px;
                margin: 2px 0 10px 0;
                padding: 5px 15px;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;
                // display: grid;
                // grid-template-columns: 1fr 1fr 1fr;
                // grid-column-gap: 15px;
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-column-gap: 55px;

                :deep(.ant-switch-inner-unchecked) {
                    margin-top: -30px;
                }

                p {
                    line-height: 20px;
                }

                span {
                    font-size: 10px;
                    color: #fff;
                    background-color: #1496db;
                    border-radius: 2px;
                }

            }

            .parameterSet-short {
                width: 448px;
                height: 140px;
                margin: 2px 0 10px 0;
                padding: 5px 15px;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;
                // display: grid;
                // grid-template-columns: 1fr 1fr 1fr;
                // grid-column-gap: 15px;
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-column-gap: 55px;

                :deep(.ant-switch-inner-unchecked) {
                    margin-top: -30px;
                }

                p {
                    line-height: 20px;
                }

                span {
                    font-size: 10px;
                    color: #fff;
                    background-color: #1496db;
                    border-radius: 2px;
                }
            }

            .solutionMethod {
                width: 448px;
                height: 120px;
                margin: 5px 0 5px 0;
                padding: 5px 15px;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;

                .solutionMethod-top {
                    display: flex;
                    justify-content: space-between;

                    >div:first-child {
                        width: 55%;
                    }

                    >div:last-child {
                        width: 42%;
                    }
                }

                .solutionMethod-bottom {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 5px;

                    >div:first-child {
                        width: 55%;
                    }

                    >div:last-child {
                        width: 42%;
                    }
                }
            }

            .setContent-balance-reduce {
                width: 448px;
                height: 130px;
                margin: 5px 0 15px 0;
                padding: 10px 10px;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;

                .setContent-balance-way-mid {
                    display: flex;
                    flex-wrap: wrap;
                    align-content: flex-start;

                    >div {
                        width: 25%;
                    }

                    >div:first-child {
                        width: 72%;
                    }

                    p {
                        line-height: 20px;
                    }

                    :deep(.ant-switch-inner-unchecked) {
                        margin-top: -30px;
                    }
                }

                .setContent-balance-way {
                    display: flex;
                    flex-wrap: wrap;
                    align-content: flex-start;

                    >div {
                        width: 33.33%;
                    }

                    >div:first-child {
                        width: 66.6%;
                    }

                    p {
                        line-height: 20px;
                    }

                    :deep(.ant-switch-inner-unchecked) {
                        margin-top: -30px;
                    }
                }

                .setContent-balance-content-mid {
                    display: flex;
                    flex-wrap: wrap;
                    align-content: flex-start;

                    >div {
                        width: 24%;
                        margin-top: 5px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    >div:last-child {
                        width: 26%;
                    }

                    p {
                        line-height: 20px;
                    }

                    :deep(.ant-switch-inner-unchecked) {
                        margin-top: -30px;
                    }
                }

                .setContent-balance-content {
                    display: flex;
                    flex-wrap: wrap;
                    align-content: flex-start;

                    >div {
                        width: 33.33%;
                        margin-top: 5px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    p {
                        line-height: 20px;
                    }

                    :deep(.ant-switch-inner-unchecked) {
                        margin-top: -30px;
                    }
                }
            }

            .setContent-balance-reduce-short {
                width: 448px;
                height: 70px;
                margin: 5px 0 15px 0;
                padding: 10px 10px;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;

                .setContent-balance-way {
                    display: flex;
                    flex-wrap: wrap;
                    align-content: flex-start;

                    >div {
                        width: 33.33%;
                    }

                    p {
                        line-height: 20px;
                    }

                    :deep(.ant-switch-inner-unchecked) {
                        margin-top: -30px;
                    }
                }
            }

            .shortTerm-reduce {
                height: 188px;
            }

            .setContent-balance-content-unified {
                display: flex;
                flex-wrap: wrap;
                align-content: flex-start;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;

                >div {
                    width: 100%;
                    margin-top: 5px;
                }

                p {
                    line-height: 20px;
                }

                :deep(.ant-switch-inner-unchecked) {
                    margin-top: -30px;
                }
            }

            .remarkBox {
                width: 448px;
                height: 65px;
                margin-bottom: 10px;
                padding: 5px 10px;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;
            }

            .remarkBox1 {
                width: 448px;
                height: 128px;
                margin-bottom: 10px;
                padding: 15px 10px;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;
            }
        }

        .main-right-shortTerm {
            width: 800px;

            >p {
                font-size: 14px;
                font-family: 'SiYuan Medium', Serif;

                &:hover {
                    cursor: pointer;
                }
            }

            .main-right-shortTerm-content {
                height: calc(100% - 50px);
            }

            .setContent-set {
                width: 600px;
                height: 100%;
                margin-top: 8px;
                padding: 2px 15px;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;
                overflow: auto;

                .advancedSet_line {
                    display: flex;
                    justify-content: space-between;
                    margin-top: 15px;

                    p {
                        line-height: 20px;
                    }

                    .setting_bottom_input {
                        position: relative;

                        .defaultVal {
                            position: absolute;
                            left: 85px;
                            top: 26px;
                            color: #042f9a;
                        }

                        .ant-input-number {
                            margin-left: 5px;
                            font-size: 12px;
                            line-height: 30px;
                        }

                        .ant-radio-wrapper {
                            font-size: 14px;
                            line-height: 30px;
                            display: flex;

                            >span:last-child {
                                display: flex;
                                align-items: center;
                            }
                        }
                    }
                }

                .advancedSet_line:nth-child(2) {
                    display: flex;
                    justify-content: start;

                    >div {
                        width: 25%;
                    }
                }
            }

            .setContent-set-short {
                width: 800px;
                height: 100%;
                padding: 0 15px 10px 15px;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;
                overflow: auto;

                .advancedSet_line {
                    // display: flex;
                    // justify-content: space-between;
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr;
                    margin-top: 20px;

                    p {
                        line-height: 20px;
                    }

                    .advancedSet_line_input {
                        position: relative;

                        .defaultVal {
                            position: absolute;
                            left: 90px;
                            top: 3px;
                            color: #042f9a;
                        }
                    }

                    .setting_bottom_input {
                        position: relative;

                        .defaultVal {
                            position: absolute;
                            left: 85px;
                            top: 26px;
                            color: #042f9a;
                        }

                        .ant-input-number {
                            margin-left: 5px;
                            font-size: 12px;
                            line-height: 30px;
                        }

                        .ant-radio-wrapper {
                            font-size: 14px;
                            line-height: 30px;
                            display: flex;

                            >span:last-child {
                                display: flex;
                                align-items: center;
                            }
                        }
                    }
                }
            }

            .setContent-set-balance {
                width: 100%;
                height: 100%;
                padding: 0 15px 10px 15px;
                // margin: 5px 0 10px 0;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;
                overflow: auto;

                .advancedSet_line {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    margin-top: 20px;

                    >div {
                        width: 33.3%;
                    }

                    p {
                        line-height: 20px;
                    }

                    .setting_bottom_input {
                        position: relative;

                        .defaultVal {
                            position: absolute;
                            left: 85px;
                            top: 26px;
                            color: #042f9a;
                        }

                        .ant-input-number {
                            margin-left: 5px;
                            font-size: 12px;
                            line-height: 30px;
                        }

                        .ant-radio-wrapper {
                            font-size: 14px;
                            line-height: 30px;
                            display: flex;

                            >span:last-child {
                                display: flex;
                                align-items: center;
                            }
                        }
                    }
                }

                .advancedSet_line:last-child {
                    display: flex;
                    justify-content: start;

                    >div {
                        width: 66%;
                    }
                }
            }
        }

        .main-right {
            width: 800px;

            >p {
                font-size: 14px;
                line-height: 20px;
                font-family: 'SiYuan Medium', Serif;

                &:hover {
                    cursor: pointer;
                }
            }

            .setContent-set-balance-long {
                height: calc(100% - 50px);
            }

            .setContent-set-balance {
                width: 100%;
                height: 100%;
                padding: 0 15px 10px 15px;
                // margin: 5px 0 10px 0;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;
                overflow: auto;

                .advancedSet_line {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    margin-top: 20px;

                    >div {
                        width: 25%;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    p {
                        line-height: 20px;
                    }

                    .setting_bottom_input {
                        position: relative;

                        .defaultVal {
                            position: absolute;
                            left: 85px;
                            top: 26px;
                            color: #042f9a;
                        }

                        .ant-input-number {
                            margin-left: 5px;
                            font-size: 12px;
                            line-height: 30px;
                        }

                        .ant-radio-wrapper {
                            font-size: 14px;
                            line-height: 30px;
                            display: flex;

                            >span:last-child {
                                display: flex;
                                align-items: center;
                            }
                        }
                    }
                }

                .advancedSet_line:last-child {
                    display: flex;
                    justify-content: start;

                    >div {
                        width: 66%;
                    }
                }

                .advancedSet_line:nth-child(5) {
                    display: flex;
                    justify-content: start;

                    >div {
                        width: 25%;
                    }
                }
            }
        }

        .main-btn {
            position: absolute;
            bottom: 10px;
            right: 45px;
            width: 770px;
            height: 40px;
            padding-top: 15px;
            // line-height: 50px;
            // display: flex;
            // justify-content: space-around;
            border-radius: 4px;
            // border: 1px solid #A4BAD5;
            text-align: right;

            button {
                margin-left: 15px;
            }
        }
    }

    &:deep(.global-main-capacity) {
        width: 100%;
        height: calc(100% - 38px);
        padding: 15px 45px;
        // font-size: 12px;
        @include add-size(12px, $size);
        color: #474747;
        position: relative;
        display: flex;
        justify-content: space-between;

        .main-left {
            >p {
                font-size: 14px;
                line-height: 20px;
                font-family: 'SiYuan Medium', Serif;
            }

            .parameterSet {
                width: 448px;
                height: 180px;
                margin: 5px 0 10px 0;
                padding: 5px 15px;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;
                // display: grid;
                // grid-template-columns: 1fr 1fr 1fr;
                // grid-column-gap: 15px;
                display: grid;
                grid-template-columns: 1fr 1fr;
                grid-column-gap: 55px;

                .ant-switch-inner-unchecked {
                    margin-top: -30px;
                }

                p {
                    line-height: 20px;
                }

                // span {
                //     font-size: 10px;
                //     color: #fff;
                //     background-color: #1496db;
                //     border-radius: 2px;
                // }

            }

            .solutionMethod {
                width: 448px;
                height: 80px;
                margin: 5px 0 5px 0;
                padding: 5px 15px;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;

                .solutionMethod-top {
                    display: flex;
                    justify-content: space-between;

                    >div:first-child {
                        width: 80%;
                    }

                    >div:last-child {
                        width: 18%;
                    }
                }
            }

            .caseRemark {
                width: 448px;
                height: 65px;
                margin: 6px 0;
                padding: 5px 15px;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;
            }

            .setContent-balance-reduce {
                width: 448px;
                height: 200px;
                margin: 5px 0 20px 0;
                padding: 2px 10px;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;

                >div:first-child {
                    width: 70%;
                }

                .setContent-balance-content {
                    display: flex;
                    flex-wrap: wrap;
                    align-content: flex-start;

                    >div {
                        width: 33.33%;
                        margin-top: 15px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    p {
                        line-height: 20px;
                    }

                    .ant-switch-inner-unchecked {
                        margin-top: -30px;
                    }
                }
            }
        }

        .main-right {
            width: 600px;

            >p {
                font-size: 14px;
                line-height: 20px;
                font-family: 'SiYuan Medium', Serif;
            }

            .setContent-set-balance {
                width: 580px;
                height: 330px;
                padding: 5px 15px;
                margin: 5px 0 10px 0;
                border-radius: 4px;
                border: 1px solid #A4BAD5;
                background: #E5EDF2;

                .advancedSet_line {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                    margin-top: 20px;

                    >div {
                        width: 33.3%;
                    }

                    p {
                        line-height: 20px;
                    }

                    .setting_bottom_input {
                        position: relative;

                        .defaultVal {
                            position: absolute;
                            left: 85px;
                            top: 26px;
                            color: #042f9a;
                        }

                        .ant-input-number {
                            margin-left: 5px;
                            font-size: 12px;
                            line-height: 30px;
                        }

                        .ant-radio-wrapper {
                            font-size: 14px;
                            line-height: 30px;
                            display: flex;

                            >span:last-child {
                                display: flex;
                                align-items: center;
                            }
                        }
                    }
                }

                .advancedSet_line:last-child {
                    justify-content: start;
                }
            }
        }

        .main-btn {
            position: absolute;
            bottom: 10px;
            right: 45px;
            width: 300px;
            display: flex;
            justify-content: space-between;
        }
    }
}

.modal-main :deep(.ant-modal-header) {
    background: #4b8df0 !important;
}

:deep(.ant-switch) {
    height: 30px;
    line-height: 30px;
}

:deep(.ant-switch-handle) {
    position: absolute;
    top: 5px;
}

.modal_confirm {
    .ant-modal {
        .ant-modal-body {
            >div {
                .modal_content {
                    padding: 17px 35px;
                    text-align: center;

                    .ant-input-number .ant-input-number-input {
                        width: 100%;
                        height: 35px;
                    }

                    img {
                        width: 36px;
                        height: 36px;
                    }
                }

                .modal_btns {
                    width: 100%;
                    margin-top: 17px;
                    text-align: center;
                    display: flex;
                    justify-content: space-between;

                    button {
                        width: 110px;
                        height: 30px;
                        letter-spacing: 0;
                    }
                }
            }
        }
    }
}
</style>
