<!-- eslint-disable no-empty -->
<template>
  <a-modal wrapClassName="modal_BPA" :afterClose="closeModal" :centered="true"  v-model:open="state.ifShow" :footer="null" :closable="false" :maskClosable="false">
    <screen-scale>
        <div class="relative" @dragover="ondragover" @drop="ondrop">
        <div class="modal_top">
            <p>{{ $t('BPA文件解析') }}</p>
            <close-outlined class="pointer" @click="emit('close')" />
        </div>
            <a-spin :spinning="state.isLoading" size="large" :tip="$t(state.tip)">
            <div class="modal_BPA_content">
                <div class="BPA_left">
                <p class="bolder">{{ $t('BPA文件') }}</p>
                <div class="upload_div">
                    <a-upload-dragger
                    v-model:fileList="state.fileList"
                    name="file"
                    accept=".dat"
                    :multiple="false"
                    @change="upload_bpa"
                    :beforeUpload="()=>false"
                    :showUploadList="false"
                    >
                    <div class="input_text space-between">
                        <p class="ellipsis">{{ state.fileName }}</p>
                        <a-button type="primary" class="absolute">{{ $t('选择文件') }}</a-button>
                    </div>
                    </a-upload-dragger>
                </div>
                    <p class="bolder">{{ $t('解析配置') }}</p>
                    <div class="solution_div">
                    <a-select
                        v-model:value="state.selectSetting"
                        :placeholder="$t('选择默认配置')"
                        :options="state.settingOption"
                        :allowClear="true"
                    >
                    <template #suffixIcon><caret-down-outlined class="ant-select-suffix" /></template>
                    </a-select>
                    <a-button type="primary" @click="state.configShow=true"><template #icon><setting-outlined /></template>{{ $t('配置管理') }}</a-button>
                    </div>
                    <a-button :disabled="state.disabled" @click="start_btn" class="start_btn" type="primary">{{ $t('开始解析') }}</a-button>
                    <p>{{ $t('BPA解析日志') }}</p>
                    <div class="textarea auto" ref="scroll" v-html="state.log_value"></div>
                </div>
                <div class="BPA_right relative">
                    <div v-if="!state.isTransfer" class="disabled absolute"></div>
                    <p>{{ $t('智能匹配') }}</p>
                    <a-textarea v-model:value="state.match_text" @change="change" :placeholder="$t('请填写需要解析的分区，以回车或逗号隔开')"/>
                    <div class="handle_btn">
                    <a-button @click="clear">{{ $t('清空') }}</a-button>
                    <a-button type="primary" :disabled="!state.match_text" @click="start_match">{{ $t('开始匹配') }}</a-button>
                    </div>
                    <div class="hidden">
                        <div class="grid">
                        <p>{{ $t('可选分区') }}</p>
                        <p>{{ $t('已选分区') }}</p>
                        </div>
                        <a-transfer
                            v-model:target-keys="state.targetKeys"
                            :data-source="state.dataSource"
                            show-search
                            :filter-option="filterOption"
                            :render="item => item.label"
                            @change="handleChange"
                            @search="handleSearch"
                        >
                        <template
                            #children="{
                                direction,
                                filteredItems,
                                selectedKeys,
                                disabled: listDisabled,
                                onItemSelectAll,
                                onItemSelect,
                            }"
                            >
                            <a-table
                            :row-selection="
                                getRowSelection({
                                    disabled: listDisabled,
                                    selectedKeys,
                                    onItemSelectAll,
                                    onItemSelect,
                                })
                            "
                            :scroll="{ y: 280 }"
                            :columns="direction === 'left' ? state.leftColumns : state.rightColumns"
                            :data-source="filteredItems"
                            :showHeader="false"
                            size="small"
                            :pagination="false"
                            :style="{ pointerEvents: listDisabled ? 'none' : null }"
                            :custom-row="
                                ({ key, disabled: itemDisabled }) => ({
                                onClick: () => {
                                    if (itemDisabled || listDisabled) return;
                                    onItemSelect(key, !selectedKeys.includes(key));
                                },
                                onDblclick:()=>{
                                    dblAdd(key,direction)
                                }
                                })
                            "
                            />
                            </template>
                        </a-transfer>
                        <div class="checked">
                            <a-checkbox v-model:checked="state.checked1">
                                {{ $t('插入机组默认参数') }}
                            </a-checkbox>
                            <a-checkbox v-model:checked="state.checked2">
                                {{ $t('插入示例时序曲线') }}
                            </a-checkbox>
                        </div>
                        <div class="option_btn">
                            <a-button @click="downLoadLog" type="primary">{{ $t('日志下载') }}</a-button>
                            <a-button type="primary" @click="confirm" :disabled="state.targetKeys.length==0">{{ $t('确定') }}</a-button>
                            <a-button v-if="false" type="primary" @click="IslandDetection" :disabled="state.targetKeys.length==0">{{ $t('孤岛检测') }}</a-button>
                        </div>
                </div>
                </div>
            </div>
            </a-spin>
            <config-modal v-if="state.configShow" @close="updateConfig"></config-modal>
        </div>
    </screen-scale>
  </a-modal>
</template>
<script setup>
import { nextTick, onMounted, ref, reactive } from 'vue'
import { uploadBPAApi, GetAllConfig } from '@/api/index'
import { islandCheckApi } from '@/api/exampleApi'
import network from '@/config/teap.config'
import message from '@/utils/message'
import { settingStore } from '@/store/settingStore'
// import { CloseOutlined, SettingOutlined, CaretDownOutlined } from '@ant-design/icons-vue'
import { storeToRefs } from 'pinia'
import { downloadHtml } from '@/utils/teap.js'
import { t } from '@/utils/common.js'
import ConfigModal from './ConfigModal.vue'
const store = settingStore()
const { wsAcceptType } = storeToRefs(store)
const emit = defineEmits(['close', 'confirm'])
const leftTableColumns = [{
	dataIndex: 'label',
	title: ''
}]
const rightTableColumns = [{
	dataIndex: 'label',
	title: ''
}]
const state = reactive({
	settingOption: [],
	caseOption: [],
	ifShow: true,
	configShow: false,
	selectSetting: undefined,
	selectCase: undefined,
	disabled: true,
	match_text: '',
	isLoading: false,
	isTransfer: false,
	checked1: true,
	checked2: true,
	log_value: undefined,
	tip: 'BPA file uploading',
	bpa_encode: undefined,
	fileName: '',
	job_id: '',
	fileList: [],
	selectList: [],
	targetKeys: [],
	dataSource: [],
	leftColumns: ref(leftTableColumns),
	rightColumns: ref(rightTableColumns)
})
const scroll = ref()
if (!wsAcceptType.value.show_error_log && !wsAcceptType.value.show_info_log && !wsAcceptType.value.show_warning_log) {
	state.log_value = `<p style='color:red;'>${t('所有日志开关均被关闭')}！！！</p>`
}
const IslandDetection = () => {
	islandCheckApi({})
}
const closeModal = () => {
	emit('close')
}
const downLoadLog = () => {
	downloadHtml(state.log_value, t('BPA解析日志') + '.html')
}
const filterOption = (inputValue, option) => {
	return option.label.indexOf(inputValue) > -1
}
const handleSearch = (dir, value) => {
	//   console.log('search:', dir, value);
}
const handleChange = (nextTargetKeys, direction, moveKeys) => {
	//   console.log('targetKeys: ', nextTargetKeys);
	//   state.selectList = nextTargetKeys
	//   console.log('direction: ', direction);
	//   console.log('moveKeys: ', moveKeys);
}
const ondragover = (e) => {
	e.preventDefault()
	return false
}
const ondrop = (e) => {
	e.preventDefault()
	if (e.target.className.includes('input_text')) { /* empty */ } else {
		return false
	}
}

const confirm = () => {
	state.tip = 'parseing'
	state.isLoading = true
	let ws
	// eslint-disable-next-line no-empty
	if (!wsAcceptType.value.show_error_log && !wsAcceptType.value.show_info_log && !wsAcceptType.value.show_warning_log) {

	} else {
		const wsprefix = navigator.userAgent.includes('Electron') ? 'ws://' : location.protocol === 'https:' ? 'wss://' : 'ws://'
		ws = new WebSocket(wsprefix + network.websocket_Prefix + '/backend/teap_bpa_ws/' + state.job_id + '/')
		ws.onmessage = function(event) {
			const data = JSON.parse(event.data)
			if (data.find(item => item.teap_finished)) {
				ws.close()
			}
			handleLogList(data)
			nextTick(() => {
				scroll.value.scrollTop = scroll.value.scrollHeight
				ws.send('teap')
			})
		}
		ws.onopen = function() { ws.send('teap') }
	}
	const formdata = new FormData()
	formdata.append('bpa_encode', state.bpa_encode)
	formdata.append('job_id', state.job_id)
	formdata.append('check_a', state.checked1)
	formdata.append('check_b', state.checked2)
	formdata.append('op_name', 'download_bpa')
	formdata.append('select_zone_list', state.targetKeys)
	if (state.selectSetting) {
		formdata.append('config_ini_id', state.selectSetting)
	}
	uploadBPAApi(
		{

		},
		formdata
	).then(res => {
		state.isLoading = false
		if (res.code == 1) {
			emit('confirm', res.download_file_name, res.download_file_path)
		} else {
			ws.close()
		}
	}).catch(() => {
		state.isLoading = false
		ws.close()
	})
}
function handleLog(data, index) {
	let str
	data.log_level == 'Error'
		? wsAcceptType.value.show_error_log
			? str = `${(index == 0 && !state.log_value) ? '' : '<br>'}<font>${data.teap_log.slice(0, 14)}</font><font style='color:red;line-break:anywhere;'> [Error] ${data.teap_log.slice(14, data.teap_log.toString().length)}</font>`
			: str = ''
		: data.log_level == 'Warning'
			? wsAcceptType.value.show_warning_log
				? str = `${(index == 0 && !state.log_value) ? '' : '<br>'}<font>${data.teap_log.slice(0, 14)}</font><font style='color:#007bff;line-break:anywhere;'> [Warning] ${data.teap_log.slice(14, data.teap_log.toString().length)}</font>`
				: str = ''
			: data.log_level == 'Info'
				? wsAcceptType.value.show_info_log
					? str = `${(index == 0 && !state.log_value) ? '' : '<br>'}<font style='line-break:anywhere;'>${data.teap_log.slice(0, 14)} [Info] ${data.teap_log.slice(14, data.teap_log.toString().length)}</font>`
					: str = ''
				: str = data.teap_log
	return str
}
function handleLogList(data) {
	const log = data.reduce((a, b, index) => {
		if (b.teap_log) {
			return a + handleLog(b, index)
		} else {
			return a
		}
	}, '')
	state.log_value = state.log_value ? state.log_value + log : log
	// data.forEach((item, index) => {
	// 	state.log_value = state.log_value ? state.log_value + handleLog(item, index) : handleLog(item, index)
	// })
}
const start_webSocket = () => {
	state.tip = 'BPA file uploading...'
	let ws
	// eslint-disable-next-line no-empty
	if (!wsAcceptType.value.show_error_log && !wsAcceptType.value.show_info_log && !wsAcceptType.value.show_warning_log) {

	} else {
		const wsprefix = navigator.userAgent.includes('Electron') ? 'ws://' : location.protocol === 'https:' ? 'wss://' : 'ws://'
		ws = new WebSocket(wsprefix + network.websocket_Prefix + '/backend/teap_bpa_ws/' + state.job_id + '/')
		ws.onmessage = function(event) {
			const data = JSON.parse(event.data)
			if (data.find(item => item.teap_finished)) {
				ws.close()
			}
			handleLogList(data)
			nextTick(() => {
				scroll.value.scrollTop = scroll.value.scrollHeight
				ws.send('teap')
			})
		}
		ws.onopen = function() { ws.send('teap') }
	}
	const formdata = new FormData()
	formdata.append('bpa_encode', state.bpa_encode)
	formdata.append('job_id', state.job_id)
	formdata.append('op_name', 'get_all_zone')
	uploadBPAApi(
		{},
		formdata
	).then(res => {
		state.isLoading = false
		if (res.code == 1) {
			message.success(res.message)
			state.isTransfer = true
			state.dataSource = res.all_zone_list.map((item, index) => {
				return {
					label: item,
					key: item
				}
			})
		} else {
			ws.close()
		}
	})
}
const change = (e) => {
	state.match_text = e.target.value.replace(/，/g, ',')
	// state.match_text = e.target.value.replace(/[^a-zA-Z0-9 \n\u4E00-\u9FA5]/g,",")
}
const start_match = () => {
	const arrs = state.match_text.split('\n').reduce((a, b) => {
		return a.concat(b.split(',').filter(item => item != ''))
	}, [])
	arrs.forEach(item => {
		if (state.dataSource.map(item => item.label).includes(item) && !state.targetKeys.includes(item)) {
			state.targetKeys.push(item)
		}
	})
	state.match_text = ''
}
const start_btn = () => {
	state.isLoading = true
	start_webSocket()
	state.isLoading = false
}
const getConfigOptions = () => {
	GetAllConfig().then(res => {
		if (res.code == 1) {
			state.settingOption = res.data.map(item => {
				return Object.assign({ ...item }, {
					label: item.config_name,
					value: item.id
				})
			})
		}
	})
}
const updateConfig = () => {
	state.selectSetting = undefined
	getConfigOptions()
	state.configShow = false
}
const clear = () => {
	state.match_text = ''
}
const upload_bpa = (info) => {
	state.tip = 'BPA file uploading...'
	state.fileName = info.file.name
	state.isLoading = true

	state.isTransfer = false
	state.dataSource = []
	state.targetKeys = []
	state.match_text = ''

	const formdata = new FormData()
	formdata.append('file', info.file)
	formdata.append('file_name', info.file.name)
	formdata.append('op_name', 'upload_bpa_file')
	state.log_value = ''
	uploadBPAApi({}, formdata).then(res => {
		if (res.code == 1) {
			message.success(res.message)
			state.bpa_encode = res.bpa_encode
			state.job_id = res.job_id
			state.disabled = false
			state.isLoading = false
		} else {
			state.log_value = `<p style='color:red;'>${res.message}</p>`
			state.isLoading = false
		}
	})
}
const dblAdd = (e, a) => {
	if (a == 'left') {
		state.targetKeys.push(e)
	} else {
		state.targetKeys = state.targetKeys.filter(item => item != e)
	}
}
const getRowSelection = ({
	disabled,
	selectedKeys,
	onItemSelectAll,
	onItemSelect
}) => {
	return {
		getCheckboxProps: item => ({
			disabled: disabled || item.disabled
		}),
		onSelectAll(selected, selectedRows) {
			const treeSelectedKeys = selectedRows.filter(item => !item.disabled).map(({
				key
			}) => key)
			onItemSelectAll(treeSelectedKeys, selected)
		},
		onSelect({
			key
		}, selected) {
			onItemSelect(key, selected)
		},
		selectedRowKeys: selectedKeys
	}
}
onMounted(() => {
	getConfigOptions()
})
</script>
<style lang="scss">
  .modal_BPA{
    .ant-modal{
      width: 70%!important;
      .ant-modal-body{
          .modal_BPA_content{
              padding: 10px 28px 28px;
              display: grid;
              grid-template-columns: 1fr 1.2fr;
              grid-column-gap: 40px;
              height: 850px;
              >div{
                  >p{
                      font-size: 18px;
                      font-weight: bolder;
                      line-height: 32px;
                      color: #545454;
                  }
              }
              .upload_div{
                  .ant-upload{
                      padding: 0px;
                      height: 36px;
                      overflow: hidden;
                  }
                  .ant-upload-drag{
                      border: 1px solid #d9d9d9;
                      box-sizing: content-box;
                  }
                  .input_text{
                      background: #fff;
                      padding-left: 10px;
                      height: 36px;
                      line-height: 36px;
                      font-size: 16px;
                      p{
                          width: 90%;
                      }
                      >button:last-child{
                          right: 0px;
                          width: 90px;
                          border: none;
                          // border-radius: 0;
                          border-left: 1px solid #d9d9d9;
                      }
                  }
              }
              .ant-select{
                  width: 100%;
                  .ant-select-selector{
                      height: 36px!important;
                  }
                  .ant-select-arrow{
                      font-size: 14px;
                  }
              }
              .solution_div{
                  height: 36px;
                  display: grid;
                  grid-template-columns: 1.9fr 1fr;
                  grid-column-gap: 20px;
                  margin-bottom: 20px;
              }
              .start_btn{
                  // margin: 15px 0;
                  margin:0 0 5px;
              }
              button{
                  width: 100%;
                  font-size: 16px;
                  height: 36px;
                  border-radius: 5px;
              }
              .textarea,textarea{
                  resize:none;
                  width: 100%;
                  border-radius: 5px;
                  font-size: 16px;
              }
              .textarea{
                  border: 1px solid #d9d9d9;
                  padding: 5px;
                  height: 587px;
                  // height: 525px;
              }
              textarea{
                  height: 200px;
              }
              .handle_btn{
                  margin: 20px 0;
                  display: flex;
                  padding-left: 250px;
                  button{
                      margin-left: 20px;
                  }
              }
              .ant-transfer{
                  background-color: #fff;
                  justify-content: space-between;
                  margin-bottom: 10px;
                  .ant-transfer-list{
                      width: 50%;
                      height: 380px;
                  }
                  .ant-transfer-operation{
                      width: 30px;
                  }
              }
              .disabled{
                  height: 100%;
                  width: 100%;
                  z-index: 99;
                  background: rgba($color: #fff, $alpha: 0.5);
                  &:hover{
                      cursor: not-allowed;
                  }
              }
              .ant-table{
                  .ant-table-cell{
                      padding:5px 10px!important;
                      line-height: 20px;
                  }
              }
              .hidden{
                  >div:first-child{
                      grid-template-columns: 1fr 1fr;
                      margin: 10px 0px;
                      p{
                          font-size: 18px;
                          font-weight: bolder;
                          text-align: center;
                          line-height: 24px;
                      }
                  }
                  .ant-empty-image{
                      height: 135px;
                  }
                  .ant-table-placeholder{
                      td{
                          border-bottom: none;
                      }
                  }
                  ::-webkit-scrollbar{
                      width:7px;
                      height:7px;
                  }
                  .checked{
                      display: flex;
                      justify-content: space-around;
                      .ant-checkbox-inner{
                          width: 20px;
                          height: 20px;
                          // background-color: #337ab7;
                          // border-color: #337ab7;
                      }
                      span{
                          font-size: 18px;
                          font-weight: bolder;
                      }
                  }
                  .ant-transfer-operation{
                      button{
                          margin: 10px 0;
                      }
                  }
              }
              .option_btn{
                  margin-top: 20px;
                  display: grid;
                  grid-template-columns: 1fr 1fr;
                  grid-column-gap: 40px;
              }
          }
          .minus_padding{
              padding-bottom: 20px;
          }
      }
    }
  }
</style>
