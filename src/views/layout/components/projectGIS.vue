<template>
   <transition class="TEAP-preview-gis" name="fade-transform" mode="out-in">
      <div class="main_body">

        <div class="main_content">
          <div class="main_header">
            <p>{{ $t('拓扑绘制') }}</p>
            <CloseOutlined @click.stop="handleCancel"/>
          </div>
          <div class="main_gis">
            <div v-show="loading" class="spinStyle"><a-spin :spinning="loading" size="large" :tip="$t('拓扑绘制中')"></a-spin></div>
            <iframe v-show="!loading" :src="formState.gisHtml" frameborder="0" width="99%" :style="{height: '100%'}" scrolling="auto" title="拓扑图"></iframe>
          </div>
        </div>
      </div>
  </transition>
</template>
<script setup>

import { ref, reactive, defineEmits, onMounted } from 'vue'
import { useRoute } from 'vue-router'

import Mitt from '@/utils/mitt.js'
import { topoGraph } from '@/api/exampleApi'

const emits = defineEmits(['closeCount'])

const route = useRoute()

const formState = reactive({
	type: '220',
	gisHtml: undefined
})

const loading = ref(true)

const handleOk = e => {
	const fileName_h5 = route.query.filePath
	Mitt.emit('handleGisLoading', true)
	topoGraph({
		'h5_filename': fileName_h5,
		'voltag': formState.type
	}).then(res => {
		console.log(1111, res)

		const baseUrl = process.env.NODE_ENV == 'development' ? 'http://*************:90' : window.location.origin
		formState.gisHtml = `${baseUrl}/backend/teap_api_v3/topo_graph/?h5_filename=${fileName_h5}&voltag=${formState.type}`

		loading.value = false
	}).catch(() => {
		emits('cancel')
		Mitt.emit('handleGisLoading', false)
	})
}

const handleCancel = e => {
	emits('cancel')
}

onMounted(() => {
	handleOk()
})

</script>
<style  lang="scss" scoped>

// .modal-main :deep(.ant-modal-header) {
//   background: #4b8df0!important;
// }
// .modal_gis {

  // background-color: #d2bfbf;
  // opacity: 0.5;
  .main_body {
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    text-align: center;
    margin-top: 10px;
    border-radius: 6px;
    .main_content {
      background-color: #fff;
      width: 80%;
      height: 90%;
      margin: auto;
      border-radius: 6px;
    }
    .main_header {

      width: 100%;
      height: 30px;
      padding: 0 15px;
      display: flex;
      justify-content: space-between;
      background-color: #454E79;
      color: #fff;
      line-height: 30px;
      border-radius: 6px 6px 0 0;
    }
    .main_gis {
      width: 100%;
      height: calc(100% - 30px);
      border-radius: 0 0 6px 6px;
    }
    .spinStyle {
      margin-top: 300px!important;
    }
  }

// }

</style>
