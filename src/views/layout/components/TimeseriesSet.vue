<template>
	<a-modal
		wrapClassName="modal_timeseries"
		:afterClose="closeModal"
		:centered="true"
		v-model:open="state.visible"
		:footer="null"
		:closable="false"
		:maskClosable="false"
	>
		<screen-scale>
			<div class="modal_top">
				<p>{{ props.isNewBuilt ? $t('请设置算例时间范围') : $t('时序设置')}}</p>
				<close-outlined class="pointer" @click="emit('cancel')" />
			</div>
			<a-spin :spinning="state.loading" size="large" :tip="$t('接口请求中')">
				<div class="modal_content relative">
					<p>{{ $t('时间范围') }}</p>
					<div class="modal_setting">
						<a-range-picker
							v-model:value="state.caseDate"
							style="width: 360px"
							valueFormat="YYYY-MM-DD"
							@calendarChange="onCalendarChange"
						/>
					</div>
					<p>{{ $t('时间间隔') }}</p>
					<div class="modal_upload">
						<a-select
							ref="select"
							v-model:value="state.case_info.data_freq"
							style="width: 360px"
						>
							<a-select-option value="M" disabled>{{ $t('分钟') }}</a-select-option>
							<a-select-option value="H">{{ $t('小时') }}</a-select-option>
							<a-select-option value="D" disabled>{{ $t('天') }}</a-select-option>
						</a-select>
					</div>
					<div class="modal_btn">
						<a-button @click="emit('cancel')">{{ $t('取消') }}</a-button>
						<a-button @click="handleConfirm" type="primary" :style="{color: '#fff'}">{{ $t('确认') }}</a-button>
					</div>
				</div>
			</a-spin>
		</screen-scale>
	</a-modal>
</template>
<script setup>
import { onMounted, reactive } from 'vue'
import { useRoute } from 'vue-router'
import { Modal } from 'ant-design-vue'
import message from '@/utils/message'
import { CloseOutlined } from '@ant-design/icons-vue'
import { globalParameters, SaveParameters } from '@/api/exampleApi'
import { t } from '@/utils/common'
const route = useRoute()
const state = reactive({
	visible: true,
	confirm_flag: false,
	loading: false,
	caseDate: ['2030-01-01', '2030-12-31'],
	case_info: {
		start_datetime: '2030-01-01',
		end_datetime: '2030-12-31',
		data_freq: 'H'
	}
})
const emit = defineEmits(['cancel', 'confirm'])
const closeModal = () => {
	emit('cancel')
}
const props = defineProps({
	isNewBuilt: {
		type: Boolean,
		default: false
	}
})
const onCalendarChange = val => {
	if (!val[1]) {
		state.caseDate[0] = val[0]
		state.caseDate[1] = val[0].slice(0, 4) + '-12-31'
	}
}

const handleConfirm = () => {
	state.loading = true
	SaveParameters({
		'import_string_func': 'teapcase:update_date_range_in_tc',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'start_datetime': `${state.caseDate[0]} 00:00:00`,
			'end_datetime': `${state.caseDate[1]} 23:00:00`,
			'ignore_temp_save': props.isNewBuilt,
			'confirm_flag': state.confirm_flag
		}
	}).then(res => {
		if (res.code == 1 && res.func_result.code == 1) {
			state.loading = false
			state.confirm_flag = false
			message.success(res.msg || t('保存成功'))
			emit('confirm')
		} else {
			state.loading = false
			Modal.confirm({
				title: t('注意'),
				content: res.func_result.message,
				wrapClassName: 'modal_confirm',
				onOk() {
					state.confirm_flag = true
					handleConfirm()
				},
				onCancel() {
					state.confirm_flag = false
				}
			})
		}
	})
}

const getGlobalParameters = () => {
	globalParameters(
		{
			'import_string_func': 'teapcase:read_from_tc',
			'func_arg_dict': {
				'file_name': route.query.filePath,
				'sheet_name': 'parameter'
			}
		}
	).then(res => {
		const { parameter } = res.func_result
		state.case_info = parameter.data.case_info
		state.caseDate[0] = parameter.data.case_info.start_datetime.split(' ')[0]
		state.caseDate[1] = parameter.data.case_info.end_datetime.split(' ')[0]
		state.loading = false
	})
}
onMounted(() => {
	if (!props.isNewBuilt) {
		state.loading = true
		getGlobalParameters()
	}
})
</script>
<style lang="scss">
.modal_timeseries{
	.ant-modal{
		width: auto!important;
		.ant-modal-body{
			>div{
				.modal_content{
					padding: 10px 30px 90px;
					>p{
						font-size: 15px;
					}
					.modal_setting{
						padding: 10px 0;
						height: 55px;
					}
					.modal_upload{
						padding: 10px 0;
						height: 55px;
					}
				}
			}
		}
	}
}
// .modal_confirm {
//   :deep(.ant-modal .ant-modal-content) {
//     padding: 15px !important;
//   }
// }

</style>

