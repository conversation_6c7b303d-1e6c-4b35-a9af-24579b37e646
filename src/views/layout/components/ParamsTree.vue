<template>
  <div class="ParamsTree">
    <a-input-search v-model:value="searchValue" style="margin-bottom: 8px" @change="onSearch" allow-clear :placeholder="$t('请输入')" />
    <div class="tree_tabs">
     {{ $t('设备选择') }}
    </div>
    <div class="tree_box" ref="treeBoxRef" @scroll="handleTreeNodeScroll">
      <a-tree
        v-model:expandedKeys="expandedKeys"
        v-model:selectedKeys="selectedKeys"
        :loadedKeys="loadedKeys"
        :tree-data="treeData"
        :field-names="fieldNames"
        :autoExpandParent="autoExpandParent"
        :show-icon="true"
        :height="state.treeHeight"
        @select="onSelect"
      >
        <template #icon="{ value }">
          <template  v-if="value">
            <img src="@/assets/tree-icon/GridSideInner.png" :style="{width: '15px', height: '15px'}" alt="">
          </template>

        </template>
        <template #title="{ label, failNumber }">
          <span v-if="label.indexOf(searchValue) > -1">
            {{ label.substr(0, label.indexOf(searchValue)) }}
            <span style="color: #f50">{{ searchValue }}</span>
            {{ label.substr(label.indexOf(searchValue) + searchValue.length) }}
            <span v-show="failNumber !== 0" style="color: #f50">{{ failNumber }}</span>
            <check-circle-filled v-show="failNumber === 0" style="color: #65b85d"/>
          </span>
          <span v-else>{{ label }}</span>
        </template>
      </a-tree>
    </div>
  </div>
</template>
<script setup>

import Mitt from '@/utils/mitt.js'
import { ref, reactive, defineEmits, onMounted } from 'vue'
import { useRoute } from 'vue-router'

import { storeToRefs } from 'pinia'
import { routeStore } from '@/store/routeStore'

import { deviceInitParam } from '@/api/exampleApi'

const route = useRoute()
const store = routeStore()
const { activeKey, routeTabs } = storeToRefs(store)

const treeData = ref([])
const treeStructure = ref({})

const emits = defineEmits(['selectTree', 'paramsTreeData'])
const expandedKeys = ref([])

const selectedKeys = ref(['gen'])

const loadedKeys = ref([])

const state = reactive({
	routePath: route.fullPath,
	scrollTop: 0,
	treeChildNode: [],
	treeHeight: 620,
	selectable: true
})

const dataList = ref([])

const handleTreeNodeScroll = (e) => {
	state.scrollTop = e.target.scrollTop
}

const fieldNames = {
	key: 'value',
	title: 'label',
	failNumber: 'failNumber'
}

const traverseTreeForChild = (data) => {
	if (data === null || data === undefined) return
	for (let i = 0; i < data.length; i++) {
		dataList.value.push({
			value: data[i].value,
			label: data[i].label
		})
		if (data[i].children && data[i].children.length > 0) {
			expandedKeys.value.push(data[i].value)
			traverseTreeForChild(data[i].children)
		}
	}
}

const traverseTree = (data) => {
	if (data === null || data === undefined) return
	for (let i = 0; i < data.length; i++) {
		dataList.value.push({
			value: data[i].value,
			label: data[i].label
		})
		if (!data[i].amount && !data[i].isLast) {
			expandedKeys.value.push(data[i].value)
			traverseTree(data[i].children)
		}
	}
}

const searchValue = ref('')
const autoExpandParent = ref(false)

const onSelect = (selectedKey, { selected, selectedNodes, node, event }) => {
	if (node.isExpand) {
		const index = expandedKeys.value.indexOf(node.key)
		if (index === -1) {
			expandedKeys.value.push(node.key)
		} else {
			expandedKeys.value = expandedKeys.value.filter((item) => item !== node.key)
		}
		selectedKeys.value = [sessionStorage.getItem('paramsTreeNode')]
		return
	}

	if (selectedKey.length === 0) {
		return selectedKeys.value[0] = sessionStorage.getItem('paramsTreeNode')
	}

	const isUnsaved = routeTabs.value.find(item => item.key == activeKey.value).isUnsaved
	if (isUnsaved) {
		const targetNode = selectedKey[0]
		selectedKeys.value[0] = sessionStorage.getItem('paramsTreeNode')

		Mitt.emit('paramsTreeChange', {
			treeNode_new: targetNode,
			treeNode_old: selectedKeys.value[0]
		})
	} else {
		onSelectParamsTreeChange(selectedKey[0])
	}

	localStorage.setItem('scrollTop', state.scrollTop)
}

const onSelectParamsTreeChange = (selectedKey) => {
	sessionStorage.setItem('paramsTreeNode', selectedKey)
	selectedKeys.value[0] = selectedKey

	emits('selectTree', selectedKey, treeStructure.value[selectedKey])
}
Mitt.on('onSelectParamsTreeChange', onSelectParamsTreeChange)

const getTreeStructure = (data) => {
	data.forEach(item => {
		treeStructure.value[item.value] = item.label
		if (item.amount || item.amount == 0) {
			item.label = `${item.label}（${item.amount}）`
		}
		if (item.children) {
			getTreeStructure(item.children)
		}
	})
}

const onSearch = (val) => {
	if (state.routePath !== route.fullPath) return
	treeData.value = []
	dataList.value = []
	if (searchValue.value == '') {
		expandedKeys.value = []
		getTreeMenuList()

		return
	}

	deviceInitParam({
		'import_string_func': 'teapcase:tc_structure_tree',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'filter_name': searchValue.value
		}
	}).then(res => {
		treeData.value = res.data
		traverseTreeForChild(treeData.value)
		getTreeStructure(treeData.value)
	})
}

const getTreeMenuList = () => {
	if (state.routePath !== route.fullPath) return
	deviceInitParam({

	}).then(res => {
		treeData.value = res.data
		const paramsTreeData = res.data.map(item => item.value)
		emits('paramsTreeData', paramsTreeData)
		emits('defaultLabel', treeData.value[0].label)

		getTreeStructure(treeData.value)
	})
}

defineExpose({ getTreeMenuList, traverseTree })

onMounted(() => {
	if (navigator.userAgent.includes('Electron')) {
		state.treeHeight = 680
	} else {
		state.treeHeight = 620
	}
	getTreeMenuList()
})

</script>
<style  lang="scss" scoped>
  .ParamsTree {
    width: 100%;
    height: 100%;
    background-color: var(--theme-bg-color);
    .tree_tabs {
      display: flex;
      // padding: 2px 4px 0 4px;
      height: 36px;
      padding: 0 7px 0 37px;
      // box-sizing: border-box;
      border: 1px solid #A2B5CC;
      border-radius: 5px 5px 0 0;
      background: #E0E2E3;
      background: rgba(2, 84, 159, 0.1608);
      line-height: 38px;
      // font-size: 15px;
      @include add-size(15px, $size);
      font-weight: normal;
      color: #474747;
      position: sticky;
      top: 0;
      z-index: 10;
    }
    .tree_box {
      width: 100%;
      height: calc(100% - 75px);
      // margin-top: 2px;
      padding: 5px 10px;
      // border: 1px solid #e6e9f0;
      border: 1px solid #A2B5CC;
      border-top: 0;
      border-radius: 0 0 5px 5px;
      box-sizing: border-box;
      // overflow: auto; // 超出这个最大高度的数据，会被隐藏起来，上下滑动
    }
  }

  .ParamsTree :deep(.ant-tree)  {
    background: transparent;
    color: #1E3D59;
    // overflow: auto;
    white-space: nowrap;
    // font-size: 14px;
    @include add-size(14px, $size);
  }
  .ParamsTree :deep(.ant-tree .ant-tree-node-content-wrapper, .ant-tree .ant-tree-checkbox+span) {
    width: 100%;
    padding: 0 0px;
  }
  .ParamsTree :deep(.ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle, .ant-tree .ant-tree-checkbox+span .ant-tree-iconEle) {
    // vertical-align: middle;
    padding-top: 2px;
  }

  .ParamsTree :deep(.ant-tree .ant-tree-treenode) {
      width: 100%;
  }
  .ParamsTree :deep(.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected, .ant-tree .ant-tree-checkbox+span.ant-tree-node-selected) {
    width: 100%;
    background-color: #B4CEE6;
  }
  .ParamsTree :deep(.ant-tree .ant-tree-node-content-wrapper:hover, ) {
    width: 100%;
  }

</style>
