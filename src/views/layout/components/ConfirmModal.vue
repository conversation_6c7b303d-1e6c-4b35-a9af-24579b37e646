<template>
    <a-modal
        wrapClassName="modal_confirm"
        :afterClose="closeModal"
        :centered="true"
        v-model:open="state.visible"
        :footer="null"
        :closable="false"
        :maskClosable="false"
    >
        <screen-scale>
            <div class="modal_top">
                <p>{{ $t('提示') }}</p>
                <close-outlined class="pointer" @click="emits('cancel')" />
            </div>
            <div class="modal_content relative">
                <img src="@/assets/icon/confirmIcon.png" alt="">
                <p>{{ $t('是否保存对') }}“ {{ props.caseName }} ”{{$t('的更改')}}？</p>
                <div class="modal_btns">
                    <a-button @click="handleOk" type="primary" :style="{color:'#fff'}" size="small">{{ $t('保存') }}</a-button>
                    <a-button @click="handleclose" type="primary" ghost size="small"  :style="{margin:'0 17px'}">{{ $t('不保存') }}</a-button>
                    <a-button @click="handleCancel" size="small">{{ $t('取消') }}</a-button>
                </div>
            </div>
        </screen-scale>
    </a-modal>
</template>
<script setup>
import { reactive, defineEmits, onMounted } from 'vue'

const emits = defineEmits(['cancel', 'close', 'confirm'])

const props = defineProps({
	caseName: {
		type: String,
		default: ''
	}
})

const state = reactive({
	visible: true
})

const handleOk = () => {
	emits('confirm')
}

const handleCancel = () => {
	emits('cancel')
}
const closeModal = () => {
	emits('cancel')
}

const handleclose = () => {
	emits('close')
}

onMounted(() => {

})

</script>
<style lang="scss" scoped>
.modal_confirm{
    .ant-modal{
        width: auto!important;
        .ant-modal-body{
            >div{
                .modal_content{
                    padding: 17px 35px;
                    text-align: center;
                    .ant-input-number .ant-input-number-input {
                        width: 100%;
                        height: 35px;
                    }
                    img {
                        width: 36px;
                        height: 36px;
                    }
                }
                .modal_btns{
                    margin-top: 17px;
                    text-align: center;
                    button{
                        width: 90px;
                        height: 30px;
                        letter-spacing: 0;
                    }
                }
            }
        }
    }
}
</style>
