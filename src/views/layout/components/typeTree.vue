
<template>
  <div v-if="state.activeTab == 'dataCase'" class="tree_box" ref="treeBoxRef">
    <a-tree
      ref="treeRef"
      v-model:expandedKeys="expandedKeys"
      v-model:selectedKeys="selectedKeys"
      :loadedKeys="loadedKeys"
      :tree-data="treeData"
      :field-names="fieldNames"
      :autoExpandParent="autoExpandParent"
      :show-icon="true"
      :height="state.treeHeight"
      @select="onSelect"
    >
      <template #icon="{ value, f_node }">
        <template v-if="value == 'timeseries'">
          <img src="@/assets/tree-icon/timeseries.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
        <template v-else-if="value == 'Grid Side'">
          <img src="@/assets/tree-icon/GridSide.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>
        <template v-else-if="value == 'Generator Side'">
          <img src="@/assets/tree-icon/GeneratorSide.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>
        <template v-else-if="value == 'Demand Side'">
          <img src="@/assets/tree-icon/DemandSide.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>
        <template v-else-if="value == 'Storage Side'">
          <img src="@/assets/tree-icon/StorageSide.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>
        <template v-else-if="value == 'Planned Generator Side Pool'">
          <img src="@/assets/tree-icon/PlannedGeneratorSidePool.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>
        <template v-else-if="value == 'Planned Storage Side Pool'">
          <img src="@/assets/tree-icon/PlannedStorageSidePool.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>
        <template v-else-if="value == 'Planned Grid Side Pool'">
          <img src="@/assets/tree-icon/PlannedGridSidePool.png" :style="{width: '20px', height: '20px'}" alt="">
        </template>

        <template v-else-if="f_node == 'Grid Side' || f_node == 'Planned Grid Side Pool'">
          <img src="@/assets/tree-icon/GridSideInner.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
        <template v-else-if="f_node == 'Generator Side' || f_node == 'Planned Generator Side Pool'">
          <img src="@/assets/tree-icon/GeneratorSideInner.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
        <template v-else-if="f_node == 'Demand Side'">
          <img src="@/assets/tree-icon/DemandSideInner.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
        <template v-else-if="f_node == 'Storage Side' || f_node == 'Planned Storage Side Pool'">
          <img src="@/assets/tree-icon/StorageSideInner.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>

        <template v-else>
          <img src="@/assets/tree-icon/device.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
      </template>
      <template #title="{ label, failNumber }">
        <span v-if="label.indexOf(searchValue) > -1">
          {{ label.substr(0, label.indexOf(searchValue)) }}
          <span style="color: #f50">{{ searchValue }}</span>
          {{ label.substr(label.indexOf(searchValue) + searchValue.length) }}
          <span v-show="failNumber !== 0" style="color: #f50">{{ failNumber }}</span>
          <check-circle-filled v-show="failNumber === 0" style="color: #65b85d"/>
        </span>
        <span v-else>{{ label }}</span>
      </template>
    </a-tree>
  </div>
  <div v-else ref="treeBoxRef" class="tree_box">
    <a-tree
      v-model:expandedKeys="expandedCalKeys"
      v-model:selectedKeys="selectedCalKeys"
      :tree-data="state.countWayTree"
      :field-names="fieldNames"
      :autoExpandParent="autoExpandParent"
      :show-icon="true"
      @select="onSelectcal"
    >
      <template #icon="{ value }">
        <template v-if="value == 'count_scene'">
          <img src="@/assets/tree-icon/computationalScenario.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
        <template v-else-if="value == $t('平水年')|| value == $t('枯水年') || value == $t('（空）')">
          <img src="@/assets/tree-icon/singleScene.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
        <template v-else>
          <img src="@/assets/tree-icon/device.png" :style="{width: '15px', height: '15px'}" alt="">
        </template>
      </template>
      <template #title="{ label, failNumber }">
        <span v-if="label.indexOf(searchValue) > -1">
          {{ label.substr(0, label.indexOf(searchValue)) }}
          <span style="color: #f50">{{ searchValue }}</span>
          {{ label.substr(label.indexOf(searchValue) + searchValue.length) }}
          <span v-show="failNumber !== 0" style="color: #f50">{{ failNumber }}</span>
          <check-circle-filled v-show="failNumber === 0" style="color: #65b85d"/>
        </span>
        <span v-else>{{ label }}</span>
      </template>
    </a-tree>
  </div>

</template>
<script setup>

import Mitt from '@/utils/mitt.js'
import { ref, reactive, defineEmits, onMounted, onActivated, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import message from '@/utils/message'
import { storeToRefs } from 'pinia'
import { routeStore } from '@/store/routeStore'

import { getTreeMenu } from '@/api/exampleApi'
import { t } from '@/utils/common'
const route = useRoute()
const store = routeStore()
const { activeKey, routeTabs } = storeToRefs(store)

const treeData = ref([])
const treeStructure = ref({})

const emits = defineEmits(['selectTree'])
const expandedKeys = ref([])
const expandedCalKeys = ref(['count_scene'])

const selectedKeys = ref([routeTabs.value.find(item => item.key == activeKey.value).treeNode || sessionStorage.getItem('treeType') || 'bus'])
const selectedCalKeys = ref([])

const loadedKeys = ref([])

const state = reactive({
	routePath: route.fullPath,
	activeTab: 'dataCase',
	scrollTop: 0,
	treeChildNode: [],
	treeHeight: 620,
	selectable: true,
	countWayTree: [
		{
			label: t('计算场景'),
			value: 'count_scene',
			disabled: false,
			children: [{
				label: t('（空）'),
				value: 'base_scene',
				failNumber: null
			}]
		}
	]
})

const treeBoxRef = ref()
const treeRef = ref()
const dataList = ref([])

const getTreeTabChange = (val) => {
	if (state.routePath !== route.fullPath) return
	dataList.value = []
	state.activeTab = val
	nextTick(() => {
		screenScale()
	})
}

const handleTreeNodeScroll = (val) => {
	if (state.routePath !== route.fullPath) return
	if (state.activeTab !== 'dataCase') return
	const treeNode = val || selectedKeys.value[0]
	treeRef.value.scrollTo({ key: treeNode })
}

const fieldNames = {
	key: 'value',
	title: 'label',
	failNumber: 'failNumber'
}

const traverseTreeForChild = (data) => {
	if (data === null || data === undefined) return
	for (let i = 0; i < data.length; i++) {
		dataList.value.push({
			value: data[i].value,
			label: data[i].label
		})
		if (data[i].children && data[i].children.length > 0) {
			expandedKeys.value.push(data[i].value)
			traverseTreeForChild(data[i].children)
		}
	}
}

const traverseTree = (data) => {
	if (data === null || data === undefined) return
	for (let i = 0; i < data.length; i++) {
		dataList.value.push({
			value: data[i].value,
			label: data[i].label
		})
		if (!data[i].amount && !data[i].isLast) {
			expandedKeys.value.push(data[i].value)
			traverseTree(data[i].children)
		}
	}
}

const searchValue = ref('')
const autoExpandParent = ref(false)

const onSelect = (selectedKey, { selected, selectedNodes, node, event }) => {
	if (node.isExpand) {
		const index = expandedKeys.value.indexOf(node.key)
		if (index === -1) {
			expandedKeys.value.push(node.key)
		} else {
			expandedKeys.value = expandedKeys.value.filter((item) => item !== node.key)
		}
		selectedKeys.value = [sessionStorage.getItem('treeType')]
		return
	}

	if (selectedKey.length === 0) {
		return selectedKeys.value[0] = sessionStorage.getItem('treeType')
	}

	Mitt.emit('stopEditing')
	Mitt.emit('clearFilter')

	const isUnsaved = routeTabs.value.find(item => item.key == activeKey.value).isUnsaved
	if (isUnsaved) {
		const targetNode = selectedKey[0]
		selectedKeys.value[0] = sessionStorage.getItem('treeType')

		if (sessionStorage.getItem('treeType').includes('-')) {
			Mitt.emit('treeChangeSaveItemized', {
				type: 'treeChange',
				treeNode_new: targetNode,
				treeNode_old: selectedKeys.value[0]
			})
		} else {
			Mitt.emit('treeChangesave', {
				type: 'treeChange',
				treeNode_new: targetNode
			})
		}
	} else {
		onSelectTreeChange(selectedKey[0])
	}
	localStorage.setItem('scrollTop', state.scrollTop)
	handleTreeNodeScroll()
}
const onSelectTreeChange = (selectedKey) => {
	sessionStorage.setItem('treeNode', selectedKey)
	sessionStorage.setItem('treeType', selectedKey)
	selectedKeys.value[0] = selectedKey
	if (selectedKey.includes('-')) {
		if (selectedKey.includes('integrated')) {
			selectedKeys.value = [sessionStorage.getItem('treeType')]
			return message.warning(t('一体化电站暂不支持此功能') + '！')
		}

		Mitt.emit('handleItemizedView', selectedKey)
		expandedKeys.value.push(selectedKey.split('-')[0])
	} else {
		emits('selectTree', selectedKey, treeStructure.value[selectedKey])
		Mitt.emit('handleOverView', selectedKey)
	}
}
Mitt.on('onSelectTreeChange', onSelectTreeChange)

const onSelectcal = (selectedKey, { selected, selectedNodes, node, event }) => {
	if (selectedKey.length === 0) {
		selectedCalKeys.value = [selectedKey[0]]
		return
	}

	if (selectedKey[0] !== 'count_scene') {
		Mitt.emit('handleScene', selectedKey[0])
	}
}

const changeTree = (val) => {
	selectedCalKeys.value = [val]
}
Mitt.on('changeTree', changeTree)

const getTreeStructure = (data) => {
	data.forEach(item => {
		treeStructure.value[item.value] = item.label
		if (item.amount || item.amount == 0) {
			item.label = `${item.label}（${item.amount}）`
		}
		if (item.children) {
			getTreeStructure(item.children)
		}
	})
}

const onSearch = (val) => {
	if (state.routePath !== route.fullPath) return
	treeData.value = []
	dataList.value = []
	searchValue.value = val
	if (searchValue.value == '') {
		expandedKeys.value = []
		getTreeMenuList()
		return
	}

	getTreeMenu({
		'import_string_func': 'teapcase:tc_structure_tree',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'filter_name': searchValue.value
		}
	}).then(res => {
		treeData.value = res.func_result.tc_structure_tree
		traverseTreeForChild(treeData.value)
		getTreeStructure(treeData.value)
	})
}

const getTreeMenuList = (val) => {
	if (state.routePath !== route.fullPath) return
	getTreeMenu({
		'import_string_func': 'teapcase:tc_structure_tree',
		'func_arg_dict': {
			'file_name': route.query.filePath,
			'filter_name': ''
		}
	}).then(res => {
		const { hydropower_scenario, scenario, tc_structure_tree } = res.func_result
		state.hydropower_scenario = hydropower_scenario.data
		state.scenario = scenario.data
		state.tc_structure_tree = tc_structure_tree

		treeData.value = tc_structure_tree.filter(item => !item.children || item.children.length > 0)
		if (val != 'saveRefresh') {
			traverseTree(treeData.value)
		}
		getTreeStructure(treeData.value)

		state.countWayTree[0].children = res.func_result.scenario.data.map(item => {
			return {
				value: item,
				label: item,
				isLeaf: true
			}
		})
		selectedCalKeys.value = [res.func_result.scenario_selected]
	})
}
Mitt.on('getTreeMenuList', getTreeMenuList)

const handleRefresh = () => {
	getTreeMenuList()
}
Mitt.on('handleRefresh', handleRefresh)

const screenScale = () => {
	const resizeObserver = new ResizeObserver(entries => {
		for (const entry of entries) {
			const { height } = entry.contentRect

			state.treeHeight = height
		}
	})
	const targetElement = document.querySelector('.tree_box')
	resizeObserver.observe(targetElement)
}

const getTreeHeight = (val) => {
	screenScale()

	if (val) {
		setTimeout(() => {
			handleTreeNodeScroll(val)
		}, 50)
	}
}
Mitt.on('getTreeHeight', getTreeHeight)

defineExpose({ getTreeMenuList, traverseTree, getTreeTabChange, getTreeHeight, onSearch })

onMounted(() => {
	getTreeMenuList()
})

onActivated(() => {
	screenScale()
})

</script>
<style  lang="scss" scoped>
  .tree_box {
    width: 100%;
    height: 100%;
  }

  .tree_box :deep(.ant-tree)  {
    background: transparent;
    color: #1E3D59;
    // overflow: auto;
    white-space: nowrap;
    // font-size: 14px;
    @include add-size(14px, $size);
  }
  .tree_box :deep(.ant-tree .ant-tree-node-content-wrapper, .ant-tree .ant-tree-checkbox+span) {
    width: 100%;
    padding: 0 0px;
  }
  .tree_box :deep(.ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle, .ant-tree .ant-tree-checkbox+span .ant-tree-iconEle) {
    // vertical-align: middle;
    padding-top: 2px;
  }

  .tree_box :deep(.ant-tree .ant-tree-treenode) {
      width: 100%;
  }
  .tree_box :deep(.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected, .ant-tree .ant-tree-checkbox+span.ant-tree-node-selected) {
    width: 100%;
    background-color: #B4CEE6;
  }
  .tree_box :deep(.ant-tree .ant-tree-node-content-wrapper:hover, ) {
    width: 100%;
  }
  .tree_box :deep(.ant-tree-list-scrollbar) {
    width: 12px!important;
  }
  .tree_box :deep(.ant-tree-list-scrollbar-thumb) {
    border-radius: 2px!important;
    background: #999!important;
  }

  // .tree_box :deep(.ant-tree-list-holder-inner) {
  //   transform: translateY(0)!important;
  // }
</style>
