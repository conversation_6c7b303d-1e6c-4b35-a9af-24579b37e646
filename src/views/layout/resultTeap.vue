<template>
  <div class="user-select modal_result">
    <div class="resultTabs">
      <a-tabs v-model:activeKey="activeKey" @change="tabsChange">
        <a-tab-pane v-for="item in state.tabs" :key="item.value" :tab="item.label"></a-tab-pane>

      </a-tabs>
    </div>
    <div class="resultMain">
      <a-spin :tip="$t('数据加载中')" size="large" :spinning="state.loading">
        <div v-if="activeKey == '2'" class="modal_result_data">
          <balance-chart @showLoading="state.loading = true" v-if="state.activeKeyShow2" @refresh="refresh" @hideLoading="state.loading = false"></balance-chart>
        </div>
        <div v-if="activeKey == '3'" class="modal_result_data">
          <curve-chart @showLoading="state.loading = true" v-if="state.activeKeyShow3" @refresh="refresh" @hideLoading="state.loading = false"></curve-chart>
        </div>
        <div v-if="activeKey == '4'" class="modal_result_data">
          <detail-chart @showLoading="state.loading = true" v-if="state.activeKeyShow4" @refresh="refresh" @hideLoading="state.loading = false"></detail-chart>
        </div>
        <div v-if="activeKey == '5'" class="modal_result_data">
          <count-params @loading="state.loading = true" @cancel="state.loading = false"></count-params>
        </div>
      </a-spin>
    </div>
  </div>
</template>
<script>
export default {
	name: 'resultTeap'
}
</script>
<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import message from '@/utils/message'
import Mitt from '@/utils/mitt.js'
import { DownloadTaskResult2, GetH5FromTeapFile } from '@/api/exampleApi'
import { GetSimulationTaskResult } from '@/api/index'
import { downloadApiFile, t } from '@/utils/common.js'

// import Sortable from 'sortablejs'

const route = useRoute()

const state = reactive({
	routePath: route.fullPath,
	loading: false,
	activeIndex: undefined,
	// activeKeyShow1: true,
	activeKeyShow2: false,
	activeKeyShow3: false,
	activeKeyShow4: false,
	activeKeyShow5: false,
	activeKeyShow6: false,
	tabs: [
		{ label: t('工作位置图'), value: '2' },
		{ label: t('曲线'), value: '3' },
		{ label: t('统计图表'), value: '4' },
		{ label: t('计算参数'), value: '5' }
	]

})
const refresh = (val) => {
	state.loading = true
	state['activeKeyShow' + val] = false
	nextTick(() => {
		state['activeKeyShow' + val] = true
	})
}
const activeKey = ref('2')

// 切换tab栏
const tabsChange = (key) => {
	// 获取'电力电量平衡'任务计算结果的API
	/**
  * 'balance_df' 电力电量平衡图
  * 'key_summaries'
  * 'result_dict'
  */
	if (!state['activeKeyShow' + key]) {
		state['activeKeyShow' + key] = true
	}
}

// 顶部操作栏
const handleActionBar = (val) => {
	if (state.routePath !== route.fullPath) return
	if (val == 'downExcel') {
		fileExport()
	} else if (val == 'downloadResultH5') {
		fileExportH5()
	}
}
Mitt.on('handleActionBar', handleActionBar)

// 下载Excel
const fileExport = () => {
	if (!route.query.id) {
		message.warning(t('该结果不支持此功能'))
		return
	}
	state.loading = true
	DownloadTaskResult2(Number(route.query.id)).then(res => {
		downloadApiFile(res)
		state.loading = false
	})
}

// 下载.teap文件的h5
const fileExportH5 = () => {
	// const fileName = route.query.name.substring(12).slice(0, -5)
	const fileName = route.query.caseFileName
	state.loading = true
	GetH5FromTeapFile({
		result_file_path: route.query.filePath,
		tc_file_name: `${fileName}.tc`,
		download_flag: true
	}, true).then(res => {
		downloadApiFile(res)
		state.loading = false
	})
}

const getScene = async() => {
	const url = route.query.type == 'mid_term' ? '/backend/teap_api_v3/get_mid_term_task_result/' : '/backend/teap_api_v3/get_long_term_task_result/'
	const tempQuery = {
		'group': `_result`,
		result_file_path: route.query.filePath
	}
	GetSimulationTaskResult(url, tempQuery).then(res => {
		if (res.sim_mode == 'capacity_balance') {
			state.tabs = [
				{ label: t('平衡表'), value: '1' }
			]
		}

		// getMidTermResult()
	})
}

onMounted(() => {
	state.loading = true
	state.activeKeyShow2 = true
	getScene()
	// nextTick(() => {
	// 	const el = document.querySelector('.resultTabs .ant-tabs-nav-list')
	// 	new Sortable(el, {
	// 		animation: 150,
	// 		swapThreshold: 10,
	// 		// direction: 'horizontal',
	// 		draggable: '.ant-tabs-tab',
	// 		onStart: (e) => {
	// 			console.log(111, e)
	// 		},
	// 		onUpdate: (e) => {
	// 			// const updatedTabs = [...routeTabs.value]
	// 			// const [removed] = updatedTabs.splice(e.oldIndex, 1)
	// 			// updatedTabs.splice(e.newIndex, 0, removed)
	// 			// routeTabs.value = updatedTabs
	// 			// console.log(222, routeTabs.value)
	// 		},
	// 		onSort: (e) => {
	// 			console.log(333, e)
	// 		},
	// 		onEnd: (e) => {
	// 			console.log(444, e)
	// 		}
	// 	})
	// })
})
</script>
<style lang="scss" scoped>

.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.modal_result{
  [class*="class0"] {
    border: 2px solid rgb(84, 112, 198);
    box-shadow: rgb(84, 112, 198) 0px 2px 4px;
  }
  [class*="class1"]{
    border: 2px solid rgb(145, 204, 117);
    box-shadow: rgb(145, 204, 117) 0px 2px 4px;
  }
  [class*="class2"]{
    border: 2px solid rgb(250, 200, 88);
    box-shadow: rgb(250, 200, 88) 0px 2px 4px;
  }
  [class*="class3"]{
    border: 2px solid rgb(238, 102, 102);
    box-shadow: rgb(238, 102, 102) 0px 2px 4px;
  }
  [class*="class4"]{
    border: 2px solid rgb(115, 192, 222);
    box-shadow: rgb(115, 192, 222) 0px 2px 4px;
  }
  [class*="class5"]{
    border: 2px solid rgb(59, 162, 114);
    box-shadow: rgb(59, 162, 114) 0px 2px 4px;
  }
  [class*="class6"]{
    border: 2px solid rgb(252, 132, 82);
    box-shadow: rgb(252, 132, 82) 0px 2px 4px;
  }
  [class*="class7"]{
    border: 2px solid rgb(154, 96, 180);
    box-shadow: rgb(154, 96, 180) 0px 2px 4px;
  }
  [class*="class8"]{
    border: 2px solid rgb(234, 124, 204);
    box-shadow: rgb(234, 124, 204) 0px 2px 4px;
  }
  [class*="class9"] {
    border: 2px solid gray ;
    box-shadow: gray  0px 2px 4px;
  }
  .animate{
    animation: shakeX 1s;
  }
}
.user-select {
  height: 100%;
  padding: 5px 10px 5px;
  background-color: var(--theme-bg-color);
  .resultTabs {
    width: 100%;
    height: 30px;
    padding: 0 20px;
    background-color: #F6F8FA;
    border: 1px solid #9B9EA8;
    border-radius: 6px;
  }
  .resultMain {
    width: 100%;
    height: calc(100% - 32px);
    margin-top: 2px;
    border: 1px solid #9B9EA8;
    border-radius: 6px;
    // background-color: #047cff;
  }
  .modal_result_data {
    width: 100%;
    height: 100%;
    color: #424246;
    padding: 15px;
    box-sizing: border-box;
    .modal_result_data_title {
      width: 100%;
      height: 30px;
      display: flex;
      line-height: 30px;
      border-bottom: 1px solid #B5B8CA;
      div {
        width: 80px;
        text-align: center;
      }

    }
    .modal_result_data_list {
      display: flex;
      div {
        width: 80px;
        height: 30px;
        text-align: center;
        line-height: 30px;
      }
    }

  }
}
:deep(.ant-tabs .ant-tabs-tab)  {
  line-height: 4px;
  padding: 12px 15px;
}
</style>
