<template>
  <div class="home-body" @click.stop="blankClick">
    <div class="header">
      <div class="title user-select" ref="tabBar">
        <img v-if="state.appTheme == 'PRSAS'" src="@/assets/PRSAS.png" alt="">
        {{ state.app_title }}
      </div>
      <div class="header_tabs user-select">
        <div
          @click.stop="mainPanesClick('start')"
          @mousedown.stop=""
          :class="state.actionsMain == 'start' ? 'activeTab' : 'defultTab'"
        >{{ $t('开始') }}</div>
        <div
          @click.stop="mainPanesClick('count')"
          @mousedown.stop=""
          :class="state.actionsMain == 'count' ? 'activeTab' : 'defultTab'"
        >{{ $t('仿真计算') }}</div>
        <div
          @click.stop="mainPanesClick('apply')"
          @mousedown.stop=""
          :class="state.actionsMain == 'apply' ? 'activeTab' : 'defultTab'"
        >{{ $t('应用') }}</div>
        <div
          @click.stop="mainPanesClick('other')"
          @mousedown.stop=""
          :class="state.actionsMain == 'other' ? 'activeTab' : 'defultTab'"
        >{{ $t('其他') }}</div>
        <div
          v-show="decodeActiveKey.includes($t('结果查看'))"
          @click.stop="mainPanesClick('result')"
          @mousedown.stop=""
          :class="state.actionsMain == 'result' ? 'activeTab' : 'defultTab'"
        >{{ $t('结果查看') }}</div>
      </div>
      <div class="menu_icon" v-if="userAgent.includes('Electron')">
        <MinusOutlined @click.stop="reduceScreen" />
        <FullscreenOutlined @click.stop="changeFullScreen" v-if="!state.isFullScreen" />
        <FullscreenExitOutlined @click.stop="changeFullScreen" v-else />
        <CloseOutlined @click.stop="closeElectronCheck" />
      </div>
    </div>
    <div class="header_main user-select">
      <div class="header-type" v-show="state.actionsMain == 'start'">
        <div class="header-btn" style="padding: 0 6px 0  28px;">
          <div
            :class="['header-item', state.actionsMove == 'newBuilt-hover' ? 'bgShadow' : state.actionsMove == 'newBuilt' ? 'choiceBgShadow' : '']"
            @mouseenter="mouseenterActions('newBuilt-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('newBuilt')"
            @mouseup="mouseUpActions('newBuilt-hover')"
            @click.stop="handleNewBuilt(t('未命名'), 0)"
            style="padding: 10px 13px 2px 13px;"
            v-if="!state.permissionList.includes('gis')"
          >
            <img src="@/assets/toolbar-icon/start/newBuilt.png" alt="">
            <p>{{ $t('新建') }}</p>
          </div>
          <div
            :class="['header-create-item', state.actionsMove == 'newBuilt-hover' ? 'bgShadow' : state.actionsMove == 'newBuilt' ? 'choiceBgShadow' : '']"
            @mouseenter="mouseenterActions('newBuilt-hover')"
            @mouseleave="mouseleaveActions('')"
            v-else
          >
            <div :class="[state.actionsMove == 'newBuilt-hover' ? 'activeLine' : state.actionsMove == 'newBuilt' ? 'activeChoicveLine' : 'autoLine']"></div>
            <div class="img_box"
              @click.stop="state.dropDownCreateShow=false;handleNewBuilt(t('未命名'), 0)"
              @mousedown="mouseenterActions('newBuilt')"
              @mouseup="mouseUpActions('newBuilt-hover')"
            >
              <img src="@/assets/toolbar-icon/start/newBuilt.png" alt="">
            </div>
            <div class="text_box"
              @click.stop="handleOpen('create')"
              @mousedown="mouseenterActions('newBuilt')"
              @mouseup="mouseUpActions('newBuilt-hover')"
            >
            {{ $t('新建') }}
            </div>
            <div class="dropDownIcon"
              @click.stop="handleOpen('create')"
              @mousedown="mouseenterActions('newBuilt')"
              @mouseup="mouseUpActions('newBuilt-hover')"
            >
              <CaretDownOutlined />
            </div>
            <div :class="['dropDownList']" v-show="state.dropDownCreateShow">
              <div :class="[state.actionsMove_drop == 'createTc' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'createTc'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('create_tc')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'createTc' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/save.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('新建tc文件') }}</div>
              </div>
              <div :class="[state.actionsMove_drop == 'createTg' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'createTg'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('create_tg')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'createTg' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/save.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('新建tg文件') }}</div>
              </div>
            </div>
          </div>
          <div
            :class="['header-import-item', state.actionsMove == 'import-hover' ? 'bgShadow' : state.actionsMove == 'import' ? 'choiceBgShadow' : '']"
            @mouseenter="mouseenterActions('import-hover')"
            @mouseleave="mouseleaveActions('')"
          >
            <div class="upload_all_box" @mousedown="mouseenterActions('import')" @mouseup="mouseUpActions('import-hover')">
              <a-upload
                v-model:fileList="state.fileList"
                name="file"
                :accept="state.permissionList.includes('gis')?'.xlsx,.tc,.yml,.tr,.tg':'.xlsx,.tc,.yml,.tr'"
                :multiple="false"
                :beforeUpload="()=>false"
                :showUploadList="false"
              >
                <img src="@/assets/toolbar-icon/start/importFile.png" :style="{width: '42px', height: '48px'}" alt="">
              </a-upload>
            </div>
            <div class="text_box" @click.stop="handleOpen('import')" @mousedown="mouseenterActions('import')" @mouseup="mouseUpActions('import-hover')">
              {{ $t('打开') }}
            </div>
            <div :class="[state.actionsMove == 'import-hover' ? 'activeLine' : state.actionsMove == 'import' ? 'activeChoicveLine' : 'autoLine']"></div>
            <div class="dropDownIcon" @click.stop="handleOpen('import')" @mousedown="mouseenterActions('import')" @mouseup="mouseUpActions('import-hover')"><CaretDownOutlined /></div>
            <div :class="['dropDownList',state.permissionList.includes('gis')?'dropDownList_gis':'']"
            v-show="state.dropDownShow">

              <div :class="[state.actionsMove_drop == 'tgImport' ? 'dropDownActive' : '']"
                  @mouseenter="state.actionsMove_drop = 'tgImport'"
                  @mouseup="state.actionsMove_drop = ''"
                  v-if="state.permissionList.includes('gis')"
                >
                  <div class="upload_box" @click="openGis">
                    <div :class="['dropIcon_box',state.actionsMove_drop == 'tgImport' ? 'dropIcon_box_active' : '']">
                      <img src="@/assets/toolbar-icon/start/importFile.png" :style="{width: '16px', height: '16px'}" alt="">
                    </div>
                    <div :style="{fontSize: '15px', color: '#1B252B', lineHeight: '25px', fontFamily: 'SiYuan Medium'}">{{ $t('tg导入') }}</div>
                  </div>
              </div>

              <div :class="[state.actionsMove_drop == 'tcImport' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'tcImport'"
                @mouseup="state.actionsMove_drop = ''"
              >
                <a-upload
                  v-model:fileList="state.fileList"
                  name="file"
                  accept=".tc"
                  :multiple="false"
                  :beforeUpload="()=>false"
                  :showUploadList="false"
                >
                  <div class="upload_box">
                    <div :class="['dropIcon_box',state.actionsMove_drop == 'tcImport' ? 'dropIcon_box_active' : '']">
                      <img src="@/assets/toolbar-icon/start/importFile.png" :style="{width: '16px', height: '16px'}" alt="">
                    </div>
                    <div :style="{fontSize: '15px', color: '#1B252B', lineHeight: '25px', fontFamily: 'SiYuan Medium'}">{{ $t('tc导入') }}</div>
                  </div>
                </a-upload>
              </div>

              <div :class="[state.actionsMove_drop == 'exceImport' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'exceImport'"
                @mouseup="state.actionsMove_drop = ''"
              >
                <a-upload
                  v-model:fileList="state.fileList"
                  name="file"
                  accept=".xlsx"
                  :multiple="false"
                  :beforeUpload="()=>false"
                  :showUploadList="false"
                >
                  <div class="upload_box">
                    <div :class="['dropIcon_box',state.actionsMove_drop == 'exceImport' ? 'dropIcon_box_active' : '']">
                      <img src="@/assets/toolbar-icon/start/importFile.png" :style="{width: '16px', height: '16px'}" alt="">
                    </div>
                    <div :style="{fontSize: '15px', color: '#1B252B', lineHeight: '25px', fontFamily: 'SiYuan Medium'}">{{ $t('Excel导入') }}</div>
                  </div>
                </a-upload>
              </div>

            </div>
          </div>
          <div
            :class="['header-save-item', state.actionsMove == 'save-hover' ? 'bgShadow' : state.actionsMove == 'save' ? 'choiceBgShadow' : '',!decodeActiveKey.includes($t('结果查看')+'-')?'':'grayscale']"
            @mouseenter="mouseenterActions('save-hover')"
            @mouseleave="mouseleaveActions('')"
            v-if="!decodeActiveKey.includes('GIS-')"
          >
            <div :class="[state.actionsMove == 'save-hover' ? 'activeLine' : state.actionsMove == 'save' ? 'activeChoicveLine' : 'autoLine']"></div>
            <div class="img_box"
              @click.stop="handleActionBar('save')"
              @mousedown="mouseenterActions('save')"
              @mouseup="mouseUpActions('save-hover')"
            >
              <img src="@/assets/toolbar-icon/start/save.png" alt="">
            </div>
            <div class="text_box"
              @click.stop="handleOpen('save')"
              @mousedown="mouseenterActions('save')"
              @mouseup="mouseUpActions('save-hover')"
            >
            {{ $t('保存') }}
            </div>
            <div class="dropDownIcon"
              @click.stop="handleOpen('save')"
              @mousedown="mouseenterActions('save')"
              @mouseup="mouseUpActions('save-hover')"
            >
              <CaretDownOutlined />
            </div>
            <div :class="[state.isDebug ? 'dropDownList1' : 'dropDownList']" v-show="state.dropDownSaveShow">
              <div :class="[state.actionsMove_drop == 'downloadTc' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'downloadTc'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('tc')"
                v-if="!decodeActiveKey.includes('GIS-')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'downloadTc' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/save.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('另存为tc文件') }}</div>
              </div>
              <div :class="[state.actionsMove_drop == 'downloadExcel' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'downloadExcel'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('xlsx')"
                v-if="!decodeActiveKey.includes('GIS-')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'downloadExcel' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/save.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('另存为Excel文件') }}</div>
              </div>
              <div :class="[state.actionsMove_drop == 'downloadYaml' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'downloadYaml'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('yml')"
                v-if="!decodeActiveKey.includes('GIS-')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'downloadYaml' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/save.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('另存为yml文件') }}</div>
              </div>
              <div :class="[state.actionsMove_drop == 'downloadV2Xlsx' ? 'dropDownActive' : '']"
                v-show="state.isDebug"
                @mouseenter="state.actionsMove_drop = 'downloadV2Xlsx'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('v2Xlsx')"
                v-if="!decodeActiveKey.includes('GIS-')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'downloadV2Xlsx' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/save.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('另存为v2版本xlsx') }}</div>
              </div>
            </div>
          </div>
          <div
            :class="['header-item', state.actionsMove == 'save-hover' ? 'bgShadow' : state.actionsMove == 'save' ? 'choiceBgShadow' : '']"
            @mouseenter="mouseenterActions('save-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('save')"
            @mouseup="mouseUpActions('save-hover')"
            @click.stop="handleActionBar('save')"
            style="padding: 10px 13px 2px 13px;"
            v-else
          >
            <img src="@/assets/toolbar-icon/start/save.png" alt="">
            <p>{{ $t('保存') }}</p>
          </div>
          <div :class="['header-item-merge', state.actionsMove == 'caseMerge-hover' ? 'bgShadow' :state.actionsMove == 'caseMerge' ? 'choiceBgShadow' : '']"
            @mouseenter="mouseenterActions('caseMerge-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('caseMerge')"
            @mouseup="mouseUpActions('caseMerge-hover')"
          >
            <a-upload
              v-model:fileList="state.mergeFileList"
              name="file"
              accept=".tc"
              :multiple="true"
              :beforeUpload="()=>false"
              :showUploadList="false"
            >

              <div class="upload_box">
                <img src="@/assets/toolbar-icon/apply/caseMerge.png" alt="">
                <p>{{ $t('算例拼合') }}</p>
              </div>

            </a-upload>
          </div>
          <div :class="['header-item', state.actionsMove == 'caseSplit-hover' ? 'bgShadow' :state.actionsMove == 'caseSplit' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            @mouseenter="mouseenterActions('caseSplit-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('caseSplit')"
            @mouseup="mouseUpActions('caseSplit-hover')"
            @click.stop="handleActionBar('caseSplit')"
          >
            <img src="@/assets/toolbar-icon/apply/caseSplit.png" alt="">
            <p>{{ $t('算例拆分') }}</p>
          </div>
          <div
            :class="['header-item', state.actionsMove == 'bpaVersion-hover' ? 'bgShadow' : state.actionsMove == 'bpaVersion' ? 'choiceBgShadow' : '']"
            v-show="state.permissionList.includes('bpa_manager')"
            @mouseenter="mouseenterActions('bpaVersion-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('bpaVersion')"
            @mouseup="mouseUpActions('bpaVersion-hover')"
            @click.stop="handleActionBar('bpaVersion')"
            style="padding: 10px 13px 2px 13px;"
          >
            <img src="@/assets/toolbar-icon/start/BPA.png" alt="">
            <p>{{ $t('BPA管理') }}</p>
          </div>
        </div>
        <div class="header_typeName">
          {{ $t('文件') }}
        </div>

      </div>
      <div class="header-type" v-show="state.actionsMain == 'count'">
        <div class="header-btn">
          <div :class="['header-item', state.actionsMove == 'long_term-hover' ? 'bgShadow' : state.actionsMove == 'long_term' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            v-show="state.permissionList.includes('long_term')"
            @mouseenter="mouseenterActions('long_term-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('long_term')"
            @mouseup="mouseUpActions('long_term-hover')"
            @click.stop="handleActionBar('long_term')"
          >
            <img src="@/assets/toolbar-icon/count/long_term.png" alt="">
            <p v-if="state.sim_job_config.long_term">{{ state.sim_job_config.long_term.long_name }}</p>
          </div>

          <div :class="['header-item', state.actionsMove == 'mid_term-hover' ? 'bgShadow' : state.actionsMove == 'mid_term' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            v-show="state.permissionList.includes('mid_term')"
            @mouseenter="mouseenterActions('mid_term-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('mid_term')"
            @mouseup="mouseUpActions('mid_term-hover')"
            @click.stop="handleActionBar('mid_term')"
          >
            <img src="@/assets/toolbar-icon/count/mid_term.png" alt="">
            <p v-if="state.sim_job_config.mid_term">{{ state.sim_job_config.mid_term.long_name }}</p>
          </div>
          <div :class="['header-item', state.actionsMove == 'short_term-hover' ? 'bgShadow' : state.actionsMove == 'short_term' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            v-show="state.permissionList.includes('short_term')"
            @mouseenter="mouseenterActions('short_term-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('short_term')"
            @mouseup="mouseUpActions('short_term-hover')"
            @click.stop="handleActionBar('short_term')"
          >
            <img src="@/assets/toolbar-icon/count/provinceqiu.png" alt="">
            <p v-if="state.sim_job_config.short_term">{{ state.sim_job_config.short_term.long_name }}</p>
          </div>
          <div :class="['header-item', 'grayscale', state.actionsMove == 'largebase-hover' ? 'bgShadow' : state.actionsMove == 'largebase' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            @mouseenter="mouseenterActions('largebase-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('largebase')"
            @mouseup="mouseUpActions('largebase-hover')"
            v-if="false"
          >
            <img src="@/assets/toolbar-icon/count/largebase.png" alt="">
            <p>{{ $t('送出规划') }}</p>
          </div>
          <div :class="['header-item', 'grayscale', state.actionsMove == 'provinceqiu-hover' ? 'bgShadow' : state.actionsMove == 'provinceqiu' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            @mouseenter="mouseenterActions('provinceqiu-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('provinceqiu')"
            @mouseup="mouseUpActions('provinceqiu-hover')"
            v-if="false"
          >
            <img src="@/assets/toolbar-icon/count/provinceqiu.png" alt="">
            <p>{{ $t('省间联络') }}</p>
          </div>
          <div :class="['header-item', state.actionsMove == 'capacity_balance-hover' ? 'bgShadow' : state.actionsMove == 'capacity_balance' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            v-show="state.permissionList.includes('capacity_balance')"
            @mouseenter="mouseenterActions('capacity_balance-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('capacity_balance')"
            @mouseup="mouseUpActions('capacity_balance-hover')"
            @click.stop="handleActionBar('capacity_balance')"
          >
            <img src="@/assets/toolbar-icon/count/capacity_balance.png" alt="">
            <p v-if="state.sim_job_config.capacity_balance">{{ state.sim_job_config.capacity_balance.long_name }}</p>
          </div>

          <div
            :class="['header-save-item',state.actionsMove == 'alternating-current-hover' ? 'bgShadow' : state.actionsMove == 'alternating-current' ? 'choiceBgShadow' : '']"
            v-show="state.permissionList.includes('ac_power_flow')"
            @mouseenter="mouseenterActions('alternating-current-hover')"
            @mouseleave="mouseleaveActions('')"
          >
            <div :class="[state.actionsMove == 'alternating-current-hover' ? 'activeLine' : state.actionsMove == 'alternating-current' ? 'activeChoicveLine' : 'autoLine']"></div>
            <div class="img_box"
              @click.stop="handleActionBar('alternating-current')"
              @mousedown="mouseenterActions('alternating-current')"
              @mouseup="mouseUpActions('alternating-current-hover')"
            >
            <img src="@/assets/toolbar-icon/count/alternating.png" alt="">
            </div>
            <div class="text_box"
              @click.stop="handleOpen('alternating-current')"
              @mousedown="mouseenterActions('alternating-current')"
              @mouseup="mouseUpActions('alternating-current-hover')"
            >
            {{ state.sim_job_config.ac_power_flow?state.sim_job_config.ac_power_flow.long_name:'' }}
            </div>
            <div class="dropDownIcon"
              @click.stop="handleOpen('alternating-current')"
              @mousedown="mouseenterActions('alternating-current')"
              @mouseup="mouseUpActions('alternating-current-hover')"
            >
              <CaretDownOutlined />
            </div>
            <div :class="[state.isDebug ? 'dropDownList1' : 'dropDownList','dropDownList2_gis']" v-show="state.dropDownAlternatingCurrentShow">
              <div :class="[state.actionsMove_drop == 'alternating-current-bpa' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'alternating-current-bpa'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('alternating-current-bpa')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'alternating-current-bpa' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/importFile.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('基于BPA') }}</div>
              </div>
              <div :class="[state.actionsMove_drop == 'alternating-current-tc' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'alternating-current-tc'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('alternating-current-tc')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'alternating-current-tc' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/importFile.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('基于tc') }}</div>
              </div>
            </div>
          </div>
          <div
            :class="['header-save-item',state.actionsMove == 'n-breakdown-hover' ? 'bgShadow' : state.actionsMove == 'n-breakdown' ? 'choiceBgShadow' : '']"
            v-show="state.permissionList.includes('n_1')||state.permissionList.includes('n_2')"
            @mouseenter="mouseenterActions('n-breakdown-hover')"
            @mouseleave="mouseleaveActions('')"
          >
            <div :class="[state.actionsMove == 'n-breakdown-hover' ? 'activeLine' : state.actionsMove == 'n-breakdown' ? 'activeChoicveLine' : 'autoLine']"></div>
            <div class="img_box"
              @click.stop="handleActionBar('n-breakdown')"
              @mousedown="mouseenterActions('n-breakdown')"
              @mouseup="mouseUpActions('n-breakdown-hover')"
            >
            <img src="@/assets/toolbar-icon/count/n-breakdow.png" alt="">
            </div>
            <div class="text_box"
              @click.stop="handleOpen('n-breakdown')"
              @mousedown="mouseenterActions('n-breakdown')"
              @mouseup="mouseUpActions('n-breakdown-hover')"
            >
            {{ state.sim_job_config.n_1?state.sim_job_config.n_1.long_name:'' }}
            </div>
            <div class="dropDownIcon"
              @click.stop="handleOpen('n-breakdown')"
              @mousedown="mouseenterActions('n-breakdown')"
              @mouseup="mouseUpActions('n-breakdown-hover')"
            >
              <CaretDownOutlined />
            </div>
            <div :class="[state.isDebug ? 'dropDownList1' : 'dropDownList','dropDownList2_gis']" v-show="state.dropDownBreakDownShow">
              <div :class="[state.actionsMove_drop == 'n-breakdown-bpa' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'n-breakdown-bpa'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('n-breakdown-bpa')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'n-breakdown-bpa' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/importFile.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('基于BPA') }}</div>
              </div>
              <div :class="[state.actionsMove_drop == 'n-breakdown-tc' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'n-breakdown-tc'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('n-breakdown-tc')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'n-breakdown-tc' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/importFile.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('基于tc') }}</div>
              </div>
            </div>
          </div>

          <div :class="['header-item', state.actionsMove == 'short-circuit-breakdown-hover' ? 'bgShadow' : state.actionsMove == 'short-circuit-breakdown' ? 'choiceBgShadow' : '']"
            v-show="state.permissionList.includes('short_circuit')"
            @mouseenter="mouseenterActions('short-circuit-breakdown-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('short-circuit-breakdown')"
            @mouseup="mouseUpActions('short-circuit-breakdown-hover')"
            @click.stop="state.breakdownType=2;state.BreakdownShow=true"
          >
            <img src="@/assets/toolbar-icon/count/short_circuit.png" alt="">
            <p v-if="state.sim_job_config.short_circuit">{{ state.sim_job_config.short_circuit.long_name }}</p>
          </div>
          <div :class="['header-item', state.actionsMove == 'bpaScan-hover' ? 'bgShadow' :state.actionsMove == 'bpaScan' ? 'choiceBgShadow' : '',state.bpaScanSpin?'grayscale':'']"
              v-show="state.permissionList.includes('bpa_scanner')"
              @mouseenter="mouseenterActions('bpaScan-hover')"
              @mouseleave="mouseleaveActions('')"
              @mousedown="mouseenterActions('bpaScan')"
              @mouseup="mouseUpActions('bpaScan-hover')"
              @click.stop="bpaScan()"
            >
              <img src="@/assets/toolbar-icon/apply/bpaScan.png" alt="">
              <p>{{ $t('BPA扫描') }}</p>
            </div>
        </div>
        <div class="header_typeName">
          {{ $t('仿真计算场景') }}
        </div>
      </div>
      <div class="header-type" v-show="state.actionsMain == 'apply'">
        <div class="header-btn">
          <div :class="['header-item', state.actionsMove == 'loadGenerate-hover' ? 'bgShadow' : state.actionsMove == 'loadGenerate' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'','grayscale']"
            @mouseenter="mouseenterActions('loadGenerate-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('loadGenerate')"
            @mouseup="mouseUpActions('loadGenerate-hover')"
            @click.stop="handleApply('loadGenerate')"
          >
            <img src="@/assets/toolbar-icon/apply/loadGenerate.png" alt="">
            <p>{{ $t('负荷生成') }}</p>
          </div>
          <div :class="['header-item', state.actionsMove == 'windGenerate-hover' ? 'bgShadow' :state.actionsMove == 'windGenerate' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'','grayscale']"
            @mouseenter="mouseenterActions('windGenerate-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('windGenerate')"
            @mouseup="mouseUpActions('windGenerate-hover')"
            @click.stop="handleApply('windGenerate')"
          >
            <img src="@/assets/toolbar-icon/apply/windGenerate.png" alt="">
            <p>{{ $t('风电生成') }}</p>
          </div>
          <div :class="['header-item', state.actionsMove == 'solarGenerate-hover' ? 'bgShadow' :state.actionsMove == 'solarGenerate' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'','grayscale']"
            @mouseenter="mouseenterActions('solarGenerate-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('solarGenerate')"
            @mouseup="mouseUpActions('solarGenerate-hover')"
            @click.stop="handleApply('solarGenerate')"
          >
            <img src="@/assets/toolbar-icon/apply/solarGenerate.png" alt="">
            <p>{{ $t('光伏生成') }}</p>
          </div>
          <div :class="['header-item', state.actionsMove == 'UnitOverhaul-hover' ? 'bgShadow' :state.actionsMove == 'UnitOverhaul' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            @mouseenter="mouseenterActions('UnitOverhaul-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('UnitOverhaul')"
            @mouseup="mouseUpActions('UnitOverhaul-hover')"
            @click.stop="handleApply('UnitOverhaul')"
          >
            <img src="@/assets/toolbar-icon/apply/UnitOverhaul.png" alt="">
            <p>{{ $t('检修生成') }}</p>
          </div>

        </div>

        <div class="header_typeName">
          {{ $t('应用场景') }}
        </div>
      </div>
      <div class="header-type" v-show="state.actionsMain == 'other'">
        <div class="header-btn">
          <div :class="['header-item', state.actionsMove == 'systemSet-hover' ? 'bgShadow' :state.actionsMove == 'systemSet' ? 'choiceBgShadow' : '']"
            @mouseenter="mouseenterActions('systemSet-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('systemSet')"
            @mouseup="mouseUpActions('systemSet-hover')"
            @click.stop="state.ShowSystemModal = true"
          >
            <img src="@/assets/toolbar-icon/other/systemSet.png" alt="">
            <p>{{ $t('系统设置') }}</p>
          </div>
          <div :class="['header-item', state.actionsMove == 'help-hover' ? 'bgShadow' :state.actionsMove == 'help' ? 'choiceBgShadow' : '','grayscale']"
            @mouseenter="mouseenterActions('help-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('help')"
            @mouseup="mouseUpActions('help-hover')"
            @blur="state.actionsMove = 'help-hover'"
            @click.stop="systemHelp"
          >

            <img src="@/assets/toolbar-icon/other/help.png" alt="">
            <p>{{ $t('系统帮助') }}</p>
          </div>
          <div v-if="userAgent.includes('Electron')"
            :class="['header-item', state.actionsMove == 'update-hover' ? 'bgShadow' :state.actionsMove == 'update' ? 'choiceBgShadow' : '']"
            @mouseenter="mouseenterActions('update-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('update')"
            @mouseup="mouseUpActions('update-hover')"
            @click.stop="checkUpdate"
          >
            <img src="@/assets/toolbar-icon/other/update.png" alt="">
            <p>{{ $t('检查更新') }}</p>
          </div>
        </div>
        <div class="header_typeName">
          {{ $t('其他') }}
        </div>
      </div>
      <div class="header-type" v-show="state.actionsMain == 'result' && decodeActiveKey.includes($t('结果查看') + '-')">
        <div class="header-btn">

          <div :class="['header-item', state.actionsMove == 'downloadResult-hover' ? 'bgShadow' :state.actionsMove == 'downloadResult' ? 'choiceBgShadow' : '']"
            @mouseenter="mouseenterActions('downloadResult-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('downloadResult')"
            @mouseup="mouseUpActions('downloadResult-hover')"
            @click.stop="downloadResult"
          >
            <img src="@/assets/toolbar-icon/result/downloadResult.png" alt="">
            <p>{{ $t('下载结果') }}</p>
          </div>
          <div :class="['header-item', state.actionsMove == 'paramView-hover' ? 'bgShadow' :state.actionsMove == 'paramView' ? 'choiceBgShadow' : '',route.name=='resultGis'&&route.query.type!='short_circuit'?'grayscale':'']"
            @mouseenter="mouseenterActions('paramView-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('paramView')"
            @mouseup="mouseUpActions('paramView-hover')"
            @click.stop="handleActionBar('paramView')"
          >
            <img src="@/assets/toolbar-icon/result/paramView.png" alt="">
            <p>{{ $t('查看参数') }}</p>
          </div>
          <div :class="['header-item', state.actionsMove == 'downloadResultH5-hover' ? 'bgShadow' :state.actionsMove == 'downloadResultH5' ? 'choiceBgShadow' : '']"
            @mouseenter="mouseenterActions('downloadResultH5-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('downloadResultH5')"
            @mouseup="mouseUpActions('downloadResultH5-hover')"
            @click.stop="handleActionBar('downloadResultH5')"
          >
            <img src="@/assets/toolbar-icon/result/downloadResultH5.png" alt="">
            <p>{{ $t('下载算例') }}</p>
          </div>
          <div :class="['header-item', state.actionsMove == 'openResultH5-hover' ? 'bgShadow' :state.actionsMove == 'openResultH5' ? 'choiceBgShadow' : '',state.spinning ? 'grayscale' : '',route.name=='resultGis'?'grayscale':'']"
            @mouseenter="mouseenterActions('openResultH5-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('openResultH5')"
            @mouseup="mouseUpActions('openResultH5-hover')"
            @click.stop="openResultCase()"
          >
            <img src="@/assets/toolbar-icon/result/openResultH5.png" alt="">
            <p>{{ $t('打开算例') }}</p>
          </div>
        </div>
        <div class="header_typeName">
          {{ $t('结果查看') }}
        </div>
      </div>

      <div class="header-type" v-show="state.actionsMain == 'start'">
        <div class="header-tool">
          <div class="header-item">
            <div :class="[state.actionsMove == 'reset-hover' ? 'bgShadow' : state.actionsMove == 'reset' ? 'choiceBgShadow' : '',!decodeActiveKey.includes($t('结果查看'))?'':'grayscale']"
              @mouseenter="mouseenterActions('reset-hover')"
              @mouseleave="mouseleaveActions('')"
              @mousedown="mouseenterActions('reset')"
              @mouseup="mouseUpActions('reset-hover')"
              @click.stop="handleActionBar('reset')"
            >
              <img src="@/assets/toolbar-icon/start/reset.png" alt=""><p>{{ $t('重置') }}</p>
            </div>
            <div :class="[state.actionsMove == 'revoke-hover' ? 'bgShadow' : state.actionsMove == 'revoke' ? 'choiceBgShadow' : '',!decodeActiveKey.includes($t('结果查看'))?'':'grayscale']"
              @mouseenter="mouseenterActions('revoke-hover')"
              @mouseleave="mouseleaveActions('')"
              @mousedown="mouseenterActions('revoke')"
              @mouseup="mouseUpActions('revoke-hover')"
              @click.stop="handleActionBar('revoke')"
            >
              <img src="@/assets/toolbar-icon/start/revoke.png" alt=""><p>{{ $t('撤销') }}</p>
            </div>
            <div :class="[state.actionsMove == 'forward-hover' ? 'bgShadow' : state.actionsMove == 'forward' ? 'choiceBgShadow' : '',!decodeActiveKey.includes($t('结果查看'))?'':'grayscale']"
              @mouseenter="mouseenterActions('forward-hover')"
              @mouseleave="mouseleaveActions('')"
              @mousedown="mouseenterActions('forward')"
              @mouseup="mouseUpActions('forward-hover')"
              @click.stop="handleActionBar('forward')"
            >
              <img src="@/assets/toolbar-icon/start/forward.png" alt=""><p>{{ $t('恢复') }}</p>
            </div>
          </div>

          <div class="header-item">
            <div :class="[state.actionsMove == 'copyData-hover' ? 'bgShadow' : state.actionsMove == 'copyData' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
              @mouseenter="mouseenterActions('copyData-hover')"
              @mouseleave="mouseleaveActions('')"
              @mousedown="mouseenterActions('copyData')"
              @mouseup="mouseUpActions('copyData-hover')"
              @click.stop="handleActionBar('copyData')"
            >
              <img src="@/assets/toolbar-icon/start/copyData.png" alt=""><p>{{ $t('复制') }}</p>
            </div>
            <div :class="[state.actionsMove == 'pasteData-hover' ? 'bgShadow' : state.actionsMove == 'pasteData' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
              @mouseenter="mouseenterActions('pasteData-hover')"
              @mouseleave="mouseleaveActions('')"
              @mousedown="mouseenterActions('pasteData')"
              @mouseup="mouseUpActions('pasteData-hover')"
              @click.stop="handleActionBar('pasteData')"
            >
              <img src="@/assets/toolbar-icon/start/pasteData.png" alt=""><p>{{ $t('粘贴') }}</p>
            </div>
            <div :class="[state.actionsMove == 'cutData-hover' ? 'bgShadow' : state.actionsMove == 'cutData' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
              @mouseenter="mouseenterActions('cutData-hover')"
              @mouseleave="mouseleaveActions('')"
              @mousedown="mouseenterActions('cutData')"
              @mouseup="mouseUpActions('cutData-hover')"
              @click.stop="handleActionBar('cutData')"
            >
              <img src="@/assets/toolbar-icon/start/cutData.png" alt=""><p>{{ $t('剪切') }}</p>
            </div>

          </div>
          <div class="header-item">

            <div :class="[state.actionsMove == 'insertRow-hover' ? 'bgShadow' : state.actionsMove == 'insertRow' ? 'choiceBgShadow' : '',!decodeActiveKey.includes($t('结果查看'))?'':'grayscale']"
              @mouseenter="mouseenterActions('insertRow-hover')"
              @mouseleave="mouseleaveActions('')"
              @mousedown="mouseenterActions('insertRow')"
              @mouseup="mouseUpActions('insertRow-hover')"
              @click.stop="handleActionBar('insertRow')"
            >

              <img src="@/assets/toolbar-icon/start/insertRow.png" alt=""><p>{{ $t('新增行') }}</p>
            </div>
            <div :class="[state.actionsMove == 'deleteRow-hover' ? 'bgShadow' : state.actionsMove == 'deleteRow' ? 'choiceBgShadow' : '',!decodeActiveKey.includes($t('结果查看'))?'':'grayscale']"
              @mouseenter="mouseenterActions('deleteRow-hover')"
              @mouseleave="mouseleaveActions('')"
              @mousedown="mouseenterActions('deleteRow')"
              @mouseup="mouseUpActions('deleteRow-hover')"
              @click.stop="handleActionBar('deleteRow')"
            >

              <img src="@/assets/toolbar-icon/start/deleteRow.png" alt=""><p>{{ $t('删除行') }}</p>
            </div>
            <div :class="[state.actionsMove == 'clearFilter-hover' ? 'bgShadow' : state.actionsMove == 'clearFilter' ? 'choiceBgShadow' : '',!decodeActiveKey.includes($t('结果查看'))?'':'grayscale']"
              @mouseenter="mouseenterActions('clearFilter-hover')"
              @mouseleave="mouseleaveActions('')"
              @mousedown="mouseenterActions('clearFilter')"
              @mouseup="mouseUpActions('clearFilter-hover')"
              @click.stop="handleActionBar('clearFilter')"
            >
              <img src="@/assets/toolbar-icon/start/clearFilter.png" alt=""><p>{{ $t('清除筛选') }}</p>
            </div>

          </div>

          <div :class="['header-tool-item', state.actionsMove == 'xlsInteractive-hover' ? 'bgShadow' :state.actionsMove == 'xlsInteractive' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            @mouseenter="mouseenterActions('xlsInteractive-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('xlsInteractive')"
            @mouseup="mouseUpActions('xlsInteractive-hover')"
            @click.stop="handleOpen('xlsInteractive')"
          >

            <img src="@/assets/toolbar-icon/start/xlsInteractive.png" alt="">
            <p>{{ $t('表格交互') }}</p>
            <div class="dropDownIcon">
              <CaretDownOutlined  style="color: #606166;"/>
            </div>
            <div class="dropDownList" v-show="state.dropDownXlsShow">
              <div :class="[state.actionsMove_drop == 'tableDownload' ? 'dropDownActive' : '', state.isModalVisible ? 'grayscale' : '']"
                @mouseenter="state.actionsMove_drop = 'tableDownload'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('tableDownload')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'tableDownload' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/xlsInteractive.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('当前表导出') }}</div>
              </div>
              <div :class="[state.actionsMove_drop == 'tableUpload' ? 'dropDownActive' : '', state.isModalVisible ? 'grayscale' : '']"
                @mouseenter="state.actionsMove_drop = 'tableUpload'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('tableUpload')"
              >
                <a-upload
                  v-model:fileList="state.fileSheetList"
                  name="file"
                  :multiple="false"
                  :beforeUpload="()=>false"
                  :showUploadList="false"
                >
                  <div class="upload_box">
                    <div :class="['dropIcon_box',state.actionsMove_drop == 'tableUpload' ? 'dropIcon_box_active' : '']">
                      <img src="@/assets/toolbar-icon/start/xlsInteractive.png" :style="{width: '16px', height: '16px'}" alt="">
                    </div>
                    <div :style="{fontSize: '15px', color: '#1B252B', lineHeight: '25px', fontFamily: 'SiYuan Medium'}">{{ $t('当前表导入') }}</div>
                  </div>
                </a-upload>
              </div>

            </div>
          </div>

          <div :class="['header-tool-item-batch', state.actionsMove == 'batching-hover' ? 'bgShadow' :state.actionsMove == 'batching' ? 'choiceBgShadow' : '',!decodeActiveKey.includes($t('结果查看'))?'':'grayscale']"
            @mouseenter="mouseenterActions('batching-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('batching')"
            @mouseup="mouseUpActions('batching-hover')"
            @click.stop="handleOpen('batching')"
          >
            <img src="@/assets/toolbar-icon/start/dataReplace.png" alt="">
            <p>{{ $t('批量处理') }}</p>
            <div class="dropDownIcon">
              <CaretDownOutlined  style="color: #606166;"/>
            </div>
            <div class="dropDownList" v-show="state.dropDownDataReplaceShow">
              <div :class="[state.actionsMove_drop == 'dataHandle' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'dataHandle'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('dataHandle')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'dataHandle' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/dataReplace.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('计算') }}</div>
              </div>
              <div :class="[state.actionsMove_drop == 'dataReplace' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'dataReplace'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('dataReplace')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'dataReplace' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/dataReplace.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('替换') }}</div>
              </div>
              <div :class="[state.actionsMove_drop == 'balanceGoals' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'balanceGoals'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('balanceGoals')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'balanceGoals' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/dataReplace.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('透视图修改') }}</div>
              </div>
            </div>
          </div>

          <div
            :class="['header-tool-item-params', state.actionsMove == 'paramsReplen-hover' ? 'bgShadow' : state.actionsMove == 'paramsReplen' ? 'choiceBgShadow' : '',!decodeActiveKey.includes($t('结果查看'))?'':'grayscale']"
            @mouseenter="mouseenterActions('paramsReplen-hover')"
            @mouseleave="mouseleaveActions('')"
          >
            <div :class="[state.actionsMove == 'paramsReplen-hover' ? 'activeLine' : state.actionsMove == 'paramsReplen' ? 'activeChoicveLine' : 'autoLine']"></div>
            <div class="img_box"
              @click.stop="handleActionBar('paramsReplen')"
              @mousedown="mouseenterActions('paramsReplen')"
              @mouseup="mouseUpActions('paramsReplen-hover')"
            >
              <img src="@/assets/toolbar-icon/start/paramsReplen.png" alt="">
            </div>
            <div class="text_box"
              @click.stop="handleOpen('paramsReplen')"
              @mousedown="mouseenterActions('paramsReplen')"
              @mouseup="mouseUpActions('paramsReplen-hover')"
            >
            {{ $t('参数补充') }}
            </div>
            <div class="dropDownIcon"
              @click.stop="handleOpen('paramsReplen')"
              @mousedown="mouseenterActions('paramsReplen')"
              @mouseup="mouseUpActions('paramsReplen-hover')"
            >
              <CaretDownOutlined />
            </div>
            <div class="dropDownList" v-show="state.dropDownParamsManageShow">
              <div :class="[state.actionsMove_drop == 'paramsManage' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'paramsManage'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="toParams()"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'paramsManage' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/paramsReplen.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('参数管理') }}</div>
              </div>
              <div :class="[state.actionsMove_drop == 'restoreDefault' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'restoreDefault'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('restoreDefault')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'restoreDefault' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/paramsReplen.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('恢复默认') }}</div>
              </div>
            </div>
          </div>

          <!-- <div :class="['header-tool-item',state.actionsMove == 'addCurve-hover' ? 'bgShadow' :state.actionsMove == 'addCurve' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            @mouseenter="mouseenterActions('addCurve-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('addCurve')"
            @mouseup="mouseUpActions('addCurve-hover')"
            @click.stop="handleActionBar('addCurve')"
          >
            <img src="@/assets/toolbar-icon/start/addCurve.png" alt="">
            <p>{{ $t('上传时序') }}</p>
          </div> -->
          <div :class="['header-tool-item', state.actionsMove == 'timeMatch-hover' ? 'bgShadow' :state.actionsMove == 'timeMatch' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            @mouseenter="mouseenterActions('timeMatch-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('timeMatch')"
            @mouseup="mouseUpActions('timeMatch-hover')"
            @click.stop="handleActionBar('timeMatch')"
          >
            <img src="@/assets/toolbar-icon/start/timeMatch.png" alt="">
            <p>{{ $t('智能关联') }}</p>
          </div>
          <div :class="['header-tool-item', state.actionsMove == 'typeChange-hover' ? 'bgShadow' :state.actionsMove == 'typeChange' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            @mouseenter="mouseenterActions('typeChange-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('typeChange')"
            @mouseup="mouseUpActions('typeChange-hover')"
            @click.stop="handleActionBar('typeChange')"
          >
            <img src="@/assets/toolbar-icon/start/typeChange.png" alt="">
            <p>{{ $t('类型转换') }}</p>
          </div>
        </div>
        <div class="header_typeName">
          {{ $t('工具') }}
        </div>
      </div>

      <div class="header-type" v-show="state.actionsMain == 'start'">
        <div class="header-btn" style="padding: 0 6px 0  6px;">
          <div :class="['header-item', state.actionsMove == 'overView-hover' ? 'bgShadow' :state.actionsMove == 'overView' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale',state.treeNode == 'timeseries'?'grayscale':'']"
            @mouseenter="mouseenterActions('overView-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('overView')"
            @mouseup="mouseUpActions('overView-hover')"
            @click.stop="handleActionBar('itemizedView')"
          >
            <img src="@/assets/toolbar-icon/start/formView.png" alt="">
            <p>{{ $t('逐项视图') }}</p>
          </div>
          <div :class="['header-item', state.actionsMove == 'projectGis-hover' ? 'bgShadow' :state.actionsMove == 'projectGis' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            v-show="state.permissionList.includes('gis')"
            @mouseenter="mouseenterActions('projectGis-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('projectGis')"
            @mouseup="mouseUpActions('projectGis-hover')"
            @click.stop="handleActionBar('showGis')"
          >
            <img src="@/assets/toolbar-icon/start/gis.png" alt="">
            <p>{{ $t('GIS视图') }}</p>
          </div>
          <div :class="['header-item', state.actionsMove == 'curvePreview-hover' ? 'bgShadow' : state.actionsMove == 'curvePreview' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale',state.treeNode == 'timeseries'?'grayscale':'']"
            @mouseenter="mouseenterActions('curvePreview-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('curvePreview')"
            @mouseup="mouseUpActions('curvePreview-hover')"
            @click.stop="handleActionBar('curvePreview')"
          >
            <img src="@/assets/toolbar-icon/start/curvePreview.png" alt="">
            <p>{{ $t('曲线预览') }}</p>
          </div>

          <div :class="['header-view-item', state.actionsMove == 'statistic-hover' ? 'bgShadow' :state.actionsMove == 'statistic' ? 'choiceBgShadow' : '',!decodeActiveKey.includes($t('结果查看'))?'':'grayscale']"
            @mouseenter="mouseenterActions('statistic-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('statistic')"
            @mouseup="mouseUpActions('statistic-hover')"
            @click.stop="handleOpen('statistic')"
          >
            <img src="@/assets/toolbar-icon/start/statistic.png" alt="">
            <p>{{ $t('信息统计') }}</p>
            <div class="dropDownIcon">
              <CaretDownOutlined  style="color: #606166;"/>
            </div>
            <div class="dropDownList" v-show="state.dropDownStatisticShow">
              <div :class="[state.actionsMove_drop == 'zhuangjiStatistic' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'zhuangjiStatistic'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('zhuangjiStatistic')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'zhuangjiStatistic' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/statistic.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('装机统计') }}</div>
              </div>
              <div :class="[state.actionsMove_drop == 'capacityStatistic' ? 'dropDownActive' : '']"
                @mouseenter="state.actionsMove_drop = 'capacityStatistic'"
                @mouseup="state.actionsMove_drop = ''"
                @click.stop="handleActionBar('capacityStatistic')"
              >
                <div :class="['dropIcon_box',state.actionsMove_drop == 'capacityStatistic' ? 'dropIcon_box_active' : '']">
                  <img src="@/assets/toolbar-icon/start/statistic.png" :style="{width: '16px', height: '16px'}" alt="">
                </div>
                <div>{{ $t('检修容量统计') }}</div>
              </div>

            </div>
          </div>
          <div :class="['header-item', state.actionsMove == 'fieldSet-hover' ? 'bgShadow' : state.actionsMove == 'fieldSet' ? 'choiceBgShadow' : '',decodeActiveKey.includes($t('编辑器'))?'':'grayscale']"
            @mouseenter="mouseenterActions('fieldSet-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('fieldSet')"
            @mouseup="mouseUpActions('fieldSet-hover')"
            @click.stop="handleActionBar('fieldSet')"
          >
            <img src="@/assets/toolbar-icon/start/fieldSet.png" alt="">
            <p>{{ $t('字段设置') }}</p>
          </div>
        </div>
        <div class="header_typeName">
          {{ $t('视图') }}
        </div>
      </div>

      <div class="header-type" v-show="state.actionsMain == 'count'">
        <div class="header-btn" style="padding: 0 6px 0  6px;">
          <div :class="['header-item', state.actionsMove == 'solver-hover' ? 'bgShadow' :state.actionsMove == 'solver' ? 'choiceBgShadow' : '']"
            @mouseenter="mouseenterActions('solver-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('solver')"
            @mouseup="mouseUpActions('solver-hover')"
            @click.stop="state.ShowSolverModal = true"
            style="padding: 10px 12px 2px 12px;"
          >
            <img src="@/assets/toolbar-icon/count/solver.png" alt="">
            <p>{{ $t('求解器') }}</p>
          </div>
          <div :class="['header-item', state.actionsMove == 'taskList-hover' ? 'bgShadow' :state.actionsMove == 'taskList' ? 'choiceBgShadow' : '']"
            @mouseenter="mouseenterActions('taskList-hover')"
            @mouseleave="mouseleaveActions('')"
            @mousedown="mouseenterActions('taskList')"
            @mouseup="mouseUpActions('taskList-hover')"
            @click.stop="state.tableShow = !state.tableShow"
          >
            <img src="@/assets/toolbar-icon/count/taskList.png" alt="">
            <p>{{ $t('任务列表') }}</p>
          </div>
        </div>
        <div class="header_typeName">
          {{ $t('仿真设置') }}
        </div>
      </div>
    </div>
    <div :class="['main',state.appTheme == 'PRSAS' ? 'main-bg-prsas' : state.appTheme == 'TEAPJS' ? 'main-bg-js' : 'main-bg']">
      <div class="main-tabs">
        <a-tabs @tabClick="tabClick" v-model:activeKey="activeKey" type="editable-card" @edit="onEdit" hideAdd>
          <a-tab-pane v-for="item in routeTabs" :key="item.key" >
            <template #tab>
              <a-tooltip>
                <template #title>{{ item.title }}</template>
                <span>
                  <i class="dot" v-show="!item.isSaved && item.isSaved !== undefined"></i>
                  {{ formatCloud(item.title) }}
                </span>
              </a-tooltip>

            </template>
          </a-tab-pane>
        </a-tabs>
      </div>

      <div class="control_btn" @click.stop="tableShow = !tableShow">
        <div class="control_up_icon">︽</div>
        <div class="control_status_card">
          <div> <span style="color: #27B148;">{{ $t('计算中') }}：</span>{{ state.startingCase ? state.startingCase : $t('无') }}</div>
          <div> <span style="color: #E6B800;">{{ $t('等待中') }}：</span>{{ state.waitingCase ? state.waitingCase + $t('个算例') : $t('无') }}</div>
        </div>
      </div>
      <div class="main_router">
        <router-view v-slot="{ Component }" v-if="routerRefresh">
          <keep-alive :include="routeCache">
            <component :key="route.fullPath" :is="Component" />
          </keep-alive>
        </router-view>
      </div>
    </div>
    <loading-footer></loading-footer>
  </div>

  <system-modal @close="systemModalClose" @confirm="systemModalConfirm" v-if="state.ShowSystemModal"></system-modal>
  <system-pdf @close="state.ShowSystempdf=false" v-if="state.ShowSystempdf"></system-pdf>
  <bpa-modal v-if="state.BpaShow" @close="state.BpaShow=false" @confirm="confirmBpa"></bpa-modal>
  <!-- <table-view-copy v-show="tableShow"></table-view-copy> -->
	<transition name="table">
    <table-view ref="tableViewRef" v-if="tableShow"></table-view>
	</transition>

  <ConfirmModal v-if="state.confirmVisible" :caseName="state.caseName" @confirm="handleConfirmModal" @close="handleCloseModal" @cancel="handleCancelModal"></ConfirmModal>

  <solver-modal v-if="state.ShowSolverModal" @close="state.ShowSolverModal=false"></solver-modal>
  <unit-overhaul @refresh="state.unitShow = false;refresh()" v-if="state.unitShow" @close="state.unitShow=false"></unit-overhaul>
  <wind-solar type="solar" @refresh="state.solarShow = false;refresh()" v-if="state.solarShow" @close="state.solarShow=false"></wind-solar>
  <wind-solar type="wind" @refresh="state.windShow = false;refresh()" v-if="state.windShow" @close="state.windShow=false"></wind-solar>
  <WindAndSolar type="windSolar" @refresh="state.windSolarShow = false;refresh()" v-if="state.windSolarShow" @close="state.windSolarShow=false"></WindAndSolar>

  <load-modal v-if="state.loadShow" @close="state.loadShow=false" @refresh="state.loadShow = false;refresh()"></load-modal>
  <new-built v-if="state.newBuiltShow" v-model:open="state.newBuiltShow" :caseName="state.resultFileName" :type="state.newBuiltType" @close="state.newBuiltShow=false,state.isRemoveTab = false;" @confirm="handleNewBuiltConfirm"></new-built>

  <TimeseriesSet v-if="state.timeseriesSetVisible"
    :isNewBuilt="true"
    v-model:open="state.timeseriesSetVisible"
    @confirm="state.timeseriesSetVisible = false"
    @cancel="state.timeseriesSetVisible = false">
  </TimeseriesSet>
  <gis-create v-if="state.createGisShow" @close="state.createGisShow = false" @confirm="goGis"></gis-create>
  <gis-open :gisPath="state.gisPath" :gisName="state.gisName" v-if="state.openGisShow" @close="state.openGisShow = false" @confirm="goGis"></gis-open>
  <gis-message v-if="state.messageShow" @close="state.messageShow=false" :data="state.warningMessage"></gis-message>
  <breakdown-modal :BreakdownFilePath="state.BreakdownFilePath" :BreakdownFileName="state.BreakdownFileName" :acceptFileType="state.acceptFileType" :type="state.breakdownType" v-if="state.BreakdownShow" @close="state.BreakdownShow = false;state.BreakdownFileName = undefined;state.BreakdownFilePath = undefined;state.acceptFileType=undefined" @confirm="()=>{}"></breakdown-modal>
  <tscan :type="1" v-if="state.tscanShow" @close="state.tscanShow = false"></tscan>

    <bpa-version
    v-if="state.bpaVersionVisible"
    @close="state.bpaVersionVisible = false"
  >
  </bpa-version>
</template>

<script setup>
import Mitt from '@/utils/mitt.js'
import { storeToRefs } from 'pinia'
import { onMounted, onUnmounted, reactive, ref, watch, computed, nextTick, createVNode } from 'vue'

import { useRoute, useRouter } from 'vue-router'

import { message, Modal } from 'ant-design-vue'
import Sortable from 'sortablejs'
import { debounce } from '@/utils/gis'
import { loadingStore } from '@/store/loadingStore'
import { routeStore } from '@/store/routeStore'
import { settingStore } from '@/store/settingStore'

import { downloadApiFile, removePrefixSuffix } from '@/utils/common'
import { importApi, mergeCase } from '@/api/startApi'
import { DownloadTrFile } from '@/api/index'
import { UploadTgFile } from '@/api/gis'
import { createEmptyHdf, getTcFromTr, getBaseDataApi, UploadCaseFile } from '@/api/exampleApi'

import ConfirmModal from './components/ConfirmModal.vue'
import BpaModal from './components/BpaModal.vue'
import SolverModal from './components/SolverModal.vue'
import TimeseriesSet from './components/TimeseriesSet.vue'
import { useI18n } from 'vue-i18n'
const { t } = useI18n()

const store = routeStore()
const tabBar = ref()
const tableViewRef = ref()
const paddingSize = ref(0)

const { routeCache, activeKey, routeTabs, routerRefresh } = storeToRefs(store)

const router = useRouter()
const route = useRoute()
const storeLoading = loadingStore()
const storeSetting = settingStore()
const { tableShow, tableType, isDebug, app_title, appTheme, permissionList, startingCase, waitingCase, sim_job_config, isChromeHigh } = storeToRefs(storeSetting)
const userAgent = ref(navigator.userAgent)
const state = reactive({
	createGisShow: false,
	bpaVersionVisible: false,
	gisPath: undefined,
	gisName: undefined,
	tscanShow: false,
	messageShow: false,
	openGisShow: false,
	breakdownType: 0,
	acceptFileType: undefined,
	BreakdownFilePath: undefined,
	BreakdownFileName: undefined,
	BreakdownShow: false,
	warningMessage: [],
	fileList: [],
	fileSheetList: [],
	mergeFileList: [],
	isFullScreen: false,
	isCloseElectron: false,
	actionsMain: 'start',
	actionsMove: '',
	actionsMove_drop: '',
	ShowSystemModal: false,
	ShowSystempdf: false,
	ShowSolverModal: false,
	BpaShow: false,
	newBuiltShow: false,
	dropDownShow: false,
	dropDownDataReplaceShow: false,
	dropDownCreateShow: false,
	dropDownStatisticShow: false,
	dropDownSaveShow: false,
	dropDownAlternatingCurrentShow: false,
	dropDownBreakDownShow: false,
	dropDownParamsManageShow: false,
	dropDownXlsShow: false,
	timeseriesSetVisible: false,
	isModalVisible: false,
	fileName: '',
	filePath: '',
	tabUrl: '',
	unitShow: false,
	loadShow: false,
	solarShow: false,
	windShow: false,
	windSolarShow: false,
	confirmVisible: false,
	targetKey: null,
	caseName: '',
	isPin: false,
	isRemoveTab: false,
	resultFileName: '',
	bpaScanSpin: false,
	newBuiltType: 'newBuilt',
	startingCase,
	waitingCase,
	tableShow,
	tableType,
	isDebug,
	app_title: userAgent.value.includes('Electron') && window.config ? window.config.webConfig.app_title : app_title,
	appTheme,
	permissionList,
	treeNode: '',
	sim_job_config,
	spinning: false
})

const softLicSuccess = () => {
	refresh()
}
Mitt.on('softLicSuccess', softLicSuccess)

const reduceScreen = () => {
	window.electronApi.sendToMain('reduce-window')
}

const changeFullScreen = () => {
	if (state.isFullScreen) {
		window.electronApi.sendToMain('normal-window')
	} else {
		window.electronApi.sendToMain('maximize-window')
	}
	state.isFullScreen = !state.isFullScreen
}

const closeElectronCheck = () => {
	if (state.startingCase) {
		Modal.confirm({
			title: '注意',
			centered: true,
			content: createVNode('div', {
				style: ''
			}, `${state.startingCase} ${t('正在计算中，确认关闭程序？')}`),
			okText: '确定',
			cancelText: '取消',
			onOk() {
				closeElectron()
			},
			onCancel() {

			}
		})
		return
	} else {
		closeElectron()
	}
}
const closeElectron = () => {
	const unSavedList = routeTabs.value.filter(item => !item.isSaved && item.isSaved !== undefined)
	if (unSavedList.length <= 0) return window.electronApi.sendToMain('close-window')
	state.isCloseElectron = true
	if (!unSavedList.some(item => item.key == activeKey.value)) {
		activeKey.value = unSavedList[0].key
		tabClick(unSavedList[0].key)
	}
	onEdit(activeKey.value)
}

const handleOpen = (val) => {
	if (val == 'create') {
		state.dropDownCreateShow = !state.dropDownCreateShow
		state.dropDownShow = false
	  state.dropDownSaveShow = false
		state.dropDownDataReplaceShow = false
		state.dropDownParamsManageShow = false
		state.dropDownXlsShow = false
		state.dropDownStatisticShow = false
		state.dropDownAlternatingCurrentShow = false
		state.dropDownBreakDownShow = false
	} else if (val == 'import') {
		state.dropDownShow = !state.dropDownShow
		state.dropDownCreateShow = false
	  state.dropDownSaveShow = false
		state.dropDownDataReplaceShow = false
		state.dropDownParamsManageShow = false
		state.dropDownXlsShow = false
		state.dropDownStatisticShow = false
		state.dropDownAlternatingCurrentShow = false
		state.dropDownBreakDownShow = false
	} else if (val == 'save') {
		state.dropDownSaveShow = !state.dropDownSaveShow
		state.dropDownCreateShow = false
		state.dropDownShow = false
		state.dropDownDataReplaceShow = false
		state.dropDownParamsManageShow = false
		state.dropDownXlsShow = false
		state.dropDownStatisticShow = false
		state.dropDownAlternatingCurrentShow = false
		state.dropDownBreakDownShow = false
	} else if (val == 'batching') {
		state.dropDownDataReplaceShow = !state.dropDownDataReplaceShow
		state.dropDownCreateShow = false
		state.dropDownShow = false
		state.dropDownSaveShow = false
		state.dropDownParamsManageShow = false
		state.dropDownXlsShow = false
		state.dropDownStatisticShow = false
		state.dropDownAlternatingCurrentShow = false
		state.dropDownBreakDownShow = false
	} else if (val == 'paramsReplen') {
		state.dropDownParamsManageShow = !state.dropDownParamsManageShow
		state.dropDownCreateShow = false
		state.dropDownDataReplaceShow = false
		state.dropDownShow = false
		state.dropDownSaveShow = false
		state.dropDownXlsShow = false
		state.dropDownStatisticShow = false
		state.dropDownAlternatingCurrentShow = false
		state.dropDownBreakDownShow = false
	} else if (val == 'xlsInteractive') {
		state.dropDownXlsShow = !state.dropDownXlsShow
		state.dropDownCreateShow = false
		state.dropDownParamsManageShow = false
		state.dropDownDataReplaceShow = false
		state.dropDownShow = false
		state.dropDownSaveShow = false
		state.dropDownStatisticShow = false
		state.dropDownAlternatingCurrentShow = false
		state.dropDownBreakDownShow = false
		state.isModalVisible = routeTabs.value.find(item => item.key == activeKey.value).isModalVisible
	} else if (val == 'statistic') {
		state.dropDownCreateShow = false
		state.dropDownParamsManageShow = false
		state.dropDownDataReplaceShow = false
		state.dropDownShow = false
		state.dropDownSaveShow = false
		state.dropDownXlsShow = false
		state.dropDownAlternatingCurrentShow = false
		state.dropDownBreakDownShow = false
		state.dropDownStatisticShow = !state.dropDownStatisticShow
	} else if (val == 'alternating-current') {
		state.dropDownCreateShow = false
		state.dropDownParamsManageShow = false
		state.dropDownDataReplaceShow = false
		state.dropDownShow = false
		state.dropDownSaveShow = false
		state.dropDownXlsShow = false
		state.dropDownAlternatingCurrentShow = !state.dropDownAlternatingCurrentShow
		state.dropDownStatisticShow = false
		state.dropDownBreakDownShow = false
	} else if (val == 'n-breakdown') {
		state.dropDownCreateShow = false
		state.dropDownParamsManageShow = false
		state.dropDownDataReplaceShow = false
		state.dropDownShow = false
		state.dropDownSaveShow = false
		state.dropDownXlsShow = false
		state.dropDownBreakDownShow = !state.dropDownBreakDownShow
		state.dropDownAlternatingCurrentShow = false
		state.dropDownStatisticShow = false
	}
}
const decodeActiveKey = computed(() => {
	return decodeURIComponent(activeKey.value)
})

const formatCloud = computed(() => {
	return function(title) {
		if (title.length > 30) {
			return title.substring(0, 15) + '...' + title.slice(-10)
		} else {
			return title
		}
	}
})

const handleApply = (val) => {
	Mitt.emit('handleActionBar', 'saveTemp')
	switch (val) {
		case 'loadGenerate':
			state.loadShow = true
			break
		case 'windGenerate':
			state.windShow = true
			break
		case 'solarGenerate':
			state.solarShow = true
			break
		case 'UnitOverhaul':
			state.unitShow = true
			break
		case 'windSolarGenerate':
			state.windSolarShow = true
			break
	}
}

const systemModalConfirm = (val) => {
	state.ShowSystemModal = false
	handleActionBar('balanceSet')
}

const openGis = () => {
	state.gisName = undefined
	state.gisPath = undefined
	state.openGisShow = true
}
const createGis = () => {
	state.createGisShow = true
}
const goGis = ({ tg_file_name, tg_file_path, warning_msg }, type) => {
	if (warning_msg && warning_msg.length > 0) {
		state.warningMessage = warning_msg
		state.messageShow = true
	}
	if (type == 'create') {
		state.createGisShow = false
	} else {
		state.openGisShow = false
	}
	addNewTab({
		name: `GIS-` + tg_file_name,
		type: 'gis',
		filePath: tg_file_path,
		gisType: type
	})
}
const systemHelp = () => {
	state.ShowSystempdf = true
}

const handleTaskListOpen = (val) => {
	state.tableShow = true
	state.tableType = 'task'
}
Mitt.on('taskListOpen', handleTaskListOpen)

const systemModalClose = (unit) => {
	state.ShowSystemModal = false
	handleActionBar('unitChange')
}

const blankClick = () => {
	state.actionsMove = ''
	state.dropDownShow = false
	state.dropDownCreateShow = false
	state.dropDownSaveShow = false
	state.dropDownDataReplaceShow = false
	state.dropDownParamsManageShow = false
	state.dropDownXlsShow = false
	state.dropDownStatisticShow = false
	state.dropDownAlternatingCurrentShow = false
	state.dropDownBreakDownShow = false
	if (!state.isPin) {
		state.tableShow = false
	}
}

const handlePin = (val) => {
	state.isPin = val
}
Mitt.on('handlePin', handlePin)

function removeAfterDot(str) {
	const dotIndex = str.indexOf('.')
	if (dotIndex !== -1) {
		return str.substring(0, dotIndex)
	}
	return str
}

const openResultCase = (record) => {
	state.spinning = true
	const fileName = record ? record.case_file_name : routeTabs.value.find(item => item.key == activeKey.value).caseFileName

	handleResultCase(fileName, 0, record ? record.result_file_path : null, record ? record.id : route.query.id)
}
Mitt.on('openResultCase', openResultCase)

const handleResultCase = (fileName, tempNum, filePath, resultId) => {
	const tempResultCase = routeTabs.value.find(item => item.title == t('编辑器') + `-${fileName}.tc` && resultId == item.resultId)
	if (tempResultCase && tempResultCase.type == 'isResultBuilt') {
		activeKey.value = tempResultCase.key
		tabClick(tempResultCase.key)
		state.spinning = false
	} else {
		getTcFromTr({
			result_file_path: filePath || route.query.filePath,
			download_flag: false,
			tc_file_name: `${fileName}.tc`
		}, true).then(res => {
			state.spinning = false
			if (res.code == 1) {
				addNewTab({
					name: t('编辑器') + `-${fileName}.tc`,
					type: 'isResultBuilt',
					caseFileName: fileName,
					filePath: res.tc_file_path,
					resultId: resultId
				})
			}
			storeLoading.hiddenModal()
		}).catch(() => {
			storeLoading.hiddenModal()
		})
	}
}

const openTaskCase = (record) => {
	state.spinning = true
	const fileName = record.case_file_name
	handleTaskCase(fileName, record.temp_tc_file_name)
}
Mitt.on('openTaskCase', openTaskCase)

const handleTaskCase = (fileName, filePath) => {
	const tempResultCase = routeTabs.value.find(item => item.title == t('编辑器') + `-${fileName}.tc`)
	if (tempResultCase && tempResultCase.type == 'isEditor') {
		activeKey.value = tempResultCase.key
		tabClick(tempResultCase.key)
		state.spinning = false
	} else {
		state.spinning = false
		addNewTab({
			name: t('编辑器') + `-${fileName}.tc`,
			type: 'isEditor',
			filePath: filePath
		})
	}
}

const openResultView = (record) => {
	const tempResult = routeTabs.value.find(item => item.filePath == (record.job_output_teap_file_path || record.result_file_path))
	if (tempResult) {
		activeKey.value = tempResult.key
		tabClick(tempResult.key)
	} else {
		const find = Object.keys(state.sim_job_config).map(item => {
			return {
				name: state.sim_job_config[item].full_name,
				job_id: state.sim_job_config[item].job_id,
				type: item
			}
		}).find(items => items.job_id == record.job_type_id)
		addNewTab({
			name: t('结果查看') + `-${removeAfterDot(record.result_file_name)}-${find.name}`,
			type: find.type,
			filePath: record.job_output_teap_file_path || record.result_file_path,
			id: record.id,
			caseFileName: record.case_file_name

		})
	}
}
Mitt.on('openResultView', openResultView)

const confirmBpa = (tc_file_name, tc_file_path) => {
	state.BpaShow = false
	addNewTab({
		name: t('编辑器') + `-${tc_file_name}`,
		type: 'isNewBuilt',
		filePath: tc_file_path
	})
}

const toParams = (tc_file_name, tc_file_path) => {
	addNewTab({
		name: t('参数管理'),
		type: 'params'
	})
}

const handleActionBar = async(val) => {
	clearOpenDropDown()

	const tempQuery = route.query.type
	if (val == 'bpaVersion') {
		state.bpaVersionVisible = true
	} else if (val == 'showGis') {
		Mitt.emit('showGis')
	} else if (val == 'alternating-current') {
		if (route.name != 'detail') {
			message.error(t('请选择tc或bpa文件进行该操作'))
		} else if (tempQuery == 'isEditor' || tempQuery == 'firstSave' || tempQuery == 'isNewBuilt') {
			storeLoading.showModal()
			UploadCaseFile({
				tc_file_name: route.query.filePath,
		    job_type_id: 101
			}).then(res => {
				if (res.code == 1) {
					message.success(res.message)
					storeLoading.hiddenModal()
					if (!tableShow.value) {
						tableShow.value = true
					}
				}
			})
		}
	} else if (val == 'alternating-current-bpa' || val == 'alternating-current-tc') {
		if (val == 'alternating-current-bpa') {
			state.acceptFileType = '.dat'
		} else {
			state.acceptFileType = '.tc'
		}
		state.breakdownType = 0
		state.BreakdownShow = true
	} else if (val == 'n-breakdown') {
		if (route.name != 'detail') {
			message.error(t('请选择tc或bpa文件进行该操作'))
		} else if (tempQuery == 'isEditor' || tempQuery == 'firstSave' || tempQuery == 'isNewBuilt') {
			state.acceptFileType = '.tc,.dat'
			state.BreakdownFilePath = route.query.filePath
			state.BreakdownFileName = route.query.name.replace(t('编辑器') + '-', '')
			state.breakdownType = 1
		  state.BreakdownShow = true
		}
	} else if (val == 'n-breakdown-bpa' || val == 'n-breakdown-tc') {
		if (val == 'n-breakdown-bpa') {
			state.acceptFileType = '.dat'
		} else {
			state.acceptFileType = '.tc'
		}
		state.breakdownType = 1
		state.BreakdownShow = true
	} else if (tempQuery == 'gis' && val == 'save') {
		Mitt.emit('saveGis', 'saveTg')
	} else if (val == 'create_tc') {
		handleNewBuilt(t('未命名'), 0)
	} else if (val == 'create_tg') {
		createGis()
	} else if ((val == 'save' || val == 'saveAndClose') && (tempQuery == 'isNewBuilt' || tempQuery == 'isResultBuilt')) {
		if (navigator.userAgent.includes('Electron')) {
			Mitt.emit('saveInElectron')
		} else {
			Mitt.emit('handleActionBar', 'isNewBuilt')
		}
	} else if ((val == 'save' || val == 'saveAndClose') && tempQuery == 'firstSave') {
		Mitt.emit('handleActionBar', 'firstSave')
	} else if (val == 'restoreDefault') {
		if (tempQuery == 'params') {
			Mitt.emit('handleActionBar', val)
		} else {
			message.warning(t('请在参数管理页面执行此功能！'))
		}
	} else {
	 val == 'saveAndClose' ? Mitt.emit('handleSaveAndClose', state.targetKey) :	Mitt.emit('handleActionBar', val)
	}
}

const handleOpenNewBuilt = (val) => {
	const tempTitle = routeTabs.value.find(item => item.key == activeKey.value).title
	state.resultFileName = tempTitle.substring(4).slice(0, -3)
	state.newBuiltType = val
	state.newBuiltShow = true
}
Mitt.on('handleOpenNewBuilt', handleOpenNewBuilt)

const bpaScan = (val) => {
	state.bpaScanSpin = true
	if (navigator.userAgent.includes('Electron')) {
		window.electronApi.sendToMain('bpaScan')
		window.electronApi.receiveFromMain('bpaScan_ready', () => {
			state.bpaScanSpin = false
		})
	} else {
		state.tscanShow = true
		const iframe = document.createElement('iframe')
		iframe.style.display = 'none'
		iframe.src = 'tscan://'
		document.body.appendChild(iframe)
		setTimeout(function() {
			document.body.removeChild(iframe)
			state.bpaScanSpin = false
		}, 500)
	}
}

const canSave = async(val) => {
	if (val) {
    	const res = await window.electronApi.waitToMain('save_file', route.query.filePath)
		if (res.filePath) {
			handleNewBuiltConfirm(res.fileName, res.filePath)
		}
	}
}
Mitt.on('canSave', canSave)

const SaveAs = (res) => {
	handleNewBuiltConfirm(res.fileName, res.filePath)
}
Mitt.on('SaveAs', SaveAs)

const mainPanesClick = (val) => {
	state.actionsMain = val
}

const mouseenterActions = (val) => {
	if (state.dropDownShow ||
  state.dropDownCreateShow ||
	state.dropDownSaveShow ||
	state.dropDownDataReplaceShow ||
  state.dropDownParamsManageShow ||
  state.dropDownXlsShow ||
  state.dropDownAlternatingCurrentShow ||
  state.dropDownBreakDownShow ||
  state.dropDownStatisticShow) {
		return
	}
	state.actionsMove = val
}
const mouseleaveActions = (val) => {
	if (state.dropDownShow ||
  state.dropDownCreateShow ||
	state.dropDownSaveShow ||
	state.dropDownDataReplaceShow ||
  state.dropDownParamsManageShow ||
  state.dropDownXlsShow ||
  state.dropDownAlternatingCurrentShow ||
  state.dropDownBreakDownShow ||
  state.dropDownStatisticShow) {
		return
	}
	state.actionsMove = val
}

const mouseUpActions = (val) => {
	state.actionsMove = val
}
const refresh = () => {
	Mitt.emit('handleRefresh')
}

const downloadResult = () => {
	storeLoading.showModal()
	DownloadTrFile({
		tr_filename: route.query.filePath
	}).then(res => {
		storeLoading.hiddenModal()
		downloadApiFile(res)
	}).catch(() => {
		storeLoading.hiddenModal()
	})
}
const checkUpdate = () => {
	window.electronApi.sendToMain('check_update', 'check_update')
}

watch(() => state.fileList, (fileList) => {
	if (fileList.length == 0) return
	if (fileList[0].name.includes('.tg')) {
		state.gisName = fileList[0].name
		if (navigator.userAgent.includes('Electron')) {
			state.gisPath = fileList[0].originFileObj.path
			state.fileList = []
			state.openGisShow = true
		} else {
			storeLoading.showModal()
			const formdata = new FormData()
			formdata.append('file', fileList[0].originFileObj)
			UploadTgFile({}, formdata).then(res => {
				storeLoading.hiddenModal()
				if (res.code == 1) {
					state.gisPath = res.file_path
					state.fileList = []
					state.openGisShow = true
				}
			}).catch(() => {
				storeLoading.hiddenModal()
			})
		}
		return
	}
	const formdata = new FormData()
	if (navigator.userAgent.includes('Electron')) {
		const find = routeTabs.value.find(item => item.filePath == fileList[0].originFileObj.path)
		if (find) {
			router.push(find.key)
			state.fileList = []
			return
		}
		formdata.append('file_path', fileList[0].originFileObj.path)
	} else {
		formdata.append('file', fileList[0].originFileObj)
	}

	importApi(formdata, true).then(res => {
		if (res.code == 1) {
			state.fileName = res.file_name
			state.filePath = res.file_path
			if (res.file_name.includes('.tr')) {
				if (res.sim_mode) {
					addNewTab({
						name: t('结果查看') + `-${removeAfterDot(res.file_name)}-${state.sim_job_config[res.sim_mode].full_name}`,
						type: res.sim_mode,
						filePath: res.file_path,
						caseFileName: removePrefixSuffix(res.file_name)
					})
				} else {
					const find = Object.keys(state.sim_job_config).map(item => {
						return {
							name: state.sim_job_config[item].full_name,
							job_id: state.sim_job_config[item].job_id,
							type: item
						}
					}).find(items => items.job_id == res.job_type_id)
					addNewTab({
						name: t('结果查看') + `-${removeAfterDot(res.file_name)}-${find.name}`,
						type: find.type,
						filePath: res.file_path,
						caseFileName: removePrefixSuffix(res.file_name)
					})
				}
				storeLoading.hiddenModal()
			} else {
				addNewTab({
					name: t('编辑器') + `-${res.file_name}`,
					type: 'isEditor',
					filePath: res.file_path
				})
				setTimeout(() => {
					Mitt.emit('handleCloseModal', res.file_path)
					Mitt.emit('handleTreeTabChange', 'dataCase')
				}, 500)
			}
		}
		state.fileList = []
		storeLoading.hiddenModal()
	}).catch(() => {
		state.fileList = []
		storeLoading.hiddenModal()
	})
})

watch(() => state.mergeFileList, (mergeFileList) => {
	if (mergeFileList.length == 0) return
	if (navigator.userAgent.includes('Electron')) {
		const formdata = new FormData()
		const paths = mergeFileList.map(item => item.originFileObj.path).join(',')
		const tempFileName = mergeFileList.map(item => item.name).join(',')
		formdata.append('file_paths', paths)
		formdata.append('file_name', tempFileName)
		mergeCase(formdata, true).then(res => {
			if (res.code == 1) {
				state.fileName = res.file_name
				state.filePath = res.file_path
				addNewTab({
					name: t('编辑器') + `-${res.file_name}`,
					type: 'isNewBuilt',
					filePath: res.file_path,
					id: 'dataMerge'
				})
				setTimeout(() => {
					Mitt.emit('handleCloseModal', res.file_path)
					Mitt.emit('handleTreeTabChange', 'dataCase')
				}, 500)
			}
			state.mergeFileList = []
			storeLoading.hiddenModal()
		}).catch(() => {
			state.mergeFileList = []
			storeLoading.hiddenModal()
		})
	} else {
		state.formdata = new FormData()

		mergeFileList.forEach(item => {
			state.formdata.append('files', item.originFileObj)
		})
		fileNameCheck(t('未命名'), 0)
	}
})

const fileNameCheck = (fileName, tempNum) => {
	let tempFileName
	tempNum < 1 ? tempFileName = fileName : tempFileName = `${fileName}(${tempNum})`
	const isHaveFileName = routeTabs.value.some(item => item.title == t('编辑器') + `-${tempFileName}.tc`)
	if (isHaveFileName) {
		const tempindex = tempNum + 1
		fileNameCheck(fileName, tempindex)
	} else {
		state.formdata.append('file_name', tempFileName)
		mergeCase(state.formdata, true).then(res => {
			if (res.code == 1) {
				state.fileName = res.file_name
				state.filePath = res.file_path
				addNewTab({
					name: t('编辑器') + `-${res.file_name}`,
					type: 'isNewBuilt',
					filePath: res.file_path,
					id: 'dataMerge'
				})
				setTimeout(() => {
					Mitt.emit('handleCloseModal', res.file_path)
					Mitt.emit('handleTreeTabChange', 'dataCase')
				}, 500)
			}
			state.mergeFileList = []
			storeLoading.hiddenModal()
		}).catch(() => {
			state.mergeFileList = []
			storeLoading.hiddenModal()
		})
	}
}
watch(() => state.fileSheetList, (fileSheetList) => {
	if (fileSheetList.length == 0) return
	Mitt.emit('importXls', fileSheetList)
	state.fileSheetList = []
})
watch(() => activeKey.value, (v) => {
	if (v.includes('/detail?name=')) {
		state.actionsMain = 'start'
	} else if (v.includes('/result?name=') || v.includes('/resultGis?name=') || v.includes('/resultTeap?name=')) {
		state.actionsMain = 'result'
	} else {
		state.actionsMain = 'start'
	}
}, { immediate: true })

const handleNewBuiltConfirm = (name, path) => {
	state.newBuiltShow = false
	if (state.isRemoveTab) {
		 onEdit(state.targetKey)
		if (state.isCloseElectron) {
			setTimeout(() => {
				closeElectron()
			}, 500)
		}
		return
	}
	routeTabs.value = routeTabs.value.filter(item => item.title != route.query.name)

	var url = window.location.href

	var newQuery = 'name=' + t('编辑器') + `-${name}&type=firstSave&filePath=${path}`

	var newUrl = url.split('?')[0] + '?' + newQuery
	window.location.href = newUrl
}

const handleNewBuilt = (fileName, tempNum) => {
	let tempFileName
	tempNum < 1 ? tempFileName = fileName : tempFileName = `${fileName}(${tempNum})`
	const isHaveFileName = routeTabs.value.some(item => item.title == t('编辑器') + `-${tempFileName}.tc`)
	if (isHaveFileName) {
		const tempindex = tempNum + 1
		handleNewBuilt(fileName, tempindex)
	} else {
		createEmptyHdf({
			'import_string_func': 'teapcase:create_empty_tc',
			'func_arg_dict': {
				'file_name': tempFileName

			}
		}, true).then(res => {
			if (res.code == 1 && res.func_result.code == 1) {
				const { tc_file_name, tc_file_path } = res.func_result
				addNewTab({
					name: t('编辑器') + `-${tc_file_name}`,
					type: 'isNewBuilt',
					filePath: tc_file_path
				})
			}
		}).catch(() => {

		})
	}
}

const getSplitCase = (val) => {
	addNewTab({
		name: t('编辑器') + `-${val.file_name}`,
		type: 'isEditor',
		filePath: val.file_path
	})
}
Mitt.on('getSplitCase', getSplitCase)

const addNewTab = (params) => {
	const { type, id } = params
	if (['isNewBuilt', 'firstSave', 'isEditor', 'isResultBuilt'].includes(type)) {
		state.tabUrl = '/detail'
	} else if (['mid_term', 'long_term', 'capacity_balance', 'teap2', 'ac_power_flow', 'n_1', 'n_2', 'short_circuit', 'short_term'].includes(type)) {
		if (type == 'teap2' || type == 'short_term') {
			state.tabUrl = '/resultTeap'
		} else if (['mid_term', 'long_term', 'capacity_balance'].includes(type)) {
			state.tabUrl = '/result'
		} else if (['ac_power_flow', 'n_1', 'n_2', 'short_circuit'].includes(type)) {
			state.tabUrl = '/resultGis'
		}
	} else if (['params'].includes(type)) {
		state.tabUrl = '/params'
	} else if (['gis'].includes(type)) {
		state.tabUrl = '/gis'
	}

	router.push({
		path: state.tabUrl,
		query: Object.assign(params, {
			date: Date.now()
		})
	}
	)
	if (['isNewBuilt'].includes(type) && id !== 'dataMerge') state.timeseriesSetVisible = true

	setTimeout(() => {
		document.querySelector('.ant-tabs-nav-list').offsetWidth > document.querySelector('.ant-tabs-nav-wrap').offsetWidth ? paddingSize.value = '40px' : paddingSize.value = 0
	}, 200)
}
const clearOpenDropDown = () => {
	state.dropDownShow = false
	state.dropDownCreateShow = false
	state.dropDownSaveShow = false
	state.dropDownDataReplaceShow = false
	state.dropDownParamsManageShow = false
	state.dropDownXlsShow = false
	state.dropDownStatisticShow = false
	state.dropDownAlternatingCurrentShow = false
	state.dropDownBreakDownShow = false
}
const tabClick = (val) => {
	clearOpenDropDown()
	router.push(val)
	state.treeNode = routeTabs.value.find(item => item.key == val).treeNode
	state.isModalVisible = routeTabs.value.find(item => item.key == val).isModalVisible
	nextTick(() => {
		Mitt.emit('getTreeHeight', state.treeNode)
	})
}
const removeResultSessionStorage = (filePath) => {
	['scene2', 'partitionValue', 'startIndex', 'endIndex', 'pickerType', 'startZoom2', 'endZoom2', 'searchTime'].concat(['scene3', 'startZoom3', 'endZoom3', 'select_curve_list', 'select_type', 'type_options', 'select_category', 'search']).forEach(item => {
		if (sessionStorage.getItem(filePath + item)) sessionStorage.removeItem(filePath + item)
	})
}

const handleConfirmModal = () => {
	const tempQuery = route.query.type
	state.confirmVisible = false
	store.setTabs(state.targetKey, false)
	store.setSaveTabs(state.targetKey, true)
	if (tempQuery == 'isNewBuilt' || tempQuery == 'isResultBuilt') {
		if (navigator.userAgent.includes('Electron')) {
			Mitt.emit('saveInElectron', true)
		} else {
			Mitt.emit('handleActionBar', 'isNewBuilt')
		}
	} else {
		handleActionBar('saveAndClose')
	}
}
const handleCloseModal = () => {
	state.confirmVisible = false
	store.setTabs(state.targetKey, false)
	store.setSaveTabs(state.targetKey, true)
	setTimeout(() => {
		onEdit(state.targetKey)
	}, 300)
	if (state.isCloseElectron) {
		setTimeout(() => {
			closeElectron()
		}, 500)
	}
}
const handleCancelModal = () => {
	state.confirmVisible = false
	state.isRemoveTab = false
	state.isCloseElectron = false
}

const onAfterSaveClose = (targetKey) => {
	onEdit(state.targetKey)
	if (state.isCloseElectron) {
		setTimeout(() => {
			closeElectron()
		}, 500)
	}
}
Mitt.on('onAfterSaveClose', onAfterSaveClose)

const onEdit = (targetKey) => {
	const targetRouteTabs = routeTabs.value.filter(item => item.key == targetKey)
	if (targetRouteTabs[0].isSaved !== undefined && !targetRouteTabs[0].isSaved) {
		router.push(targetKey)
		state.caseName = targetRouteTabs[0].title.includes(t('编辑器') + '-') ? targetRouteTabs[0].title.replace(/编辑器-/g, '') : targetRouteTabs[0].title.replace(/结果查看-/g, '')
		state.targetKey = targetKey
		state.confirmVisible = true
		state.isRemoveTab = true
	} else {
		const select = routeTabs.value.find(item => item.key == targetKey)
		if (select.filePath && select.filePath.endsWith('.tc') && route.query.type !== 'isNewBuilt' && route.query.type !== 'isResultBuilt' && route.query.type !== 'gis') {
			getBaseDataApi({
				'import_string_func': 'teapcase:delete_tc_instance',
				'func_arg_dict': {
					'file_name': select.filePath
				}
			})
		}

		if (select.name == 'result') {
			setTimeout(() => {
				removeResultSessionStorage(select.filePath)
			}, 1000)
		}
		let count = 0
		let lastIndex
		routeTabs.value = routeTabs.value.filter((item, index) => {
			if (item.key == targetKey) {
				lastIndex = index
			} else {
				if (item.name == select.name) count++
				return item
			}
		})

		if (routeTabs.value.length == 0) {
			router.push('/')
			routeCache.value = []
			activeKey.value = ''
		} else {
			if (count == 0) {
				routeCache.value = routeCache.value.filter(item => item != select.name)
			}
			if (activeKey.value == targetKey) {
				if (lastIndex - 1 >= 0) {
					activeKey.value = routeTabs.value[lastIndex - 1].key
				} else {
					activeKey.value = routeTabs.value[0].key
				}
				router.push(activeKey.value)
			}
		}
		state.isRemoveTab = false
	}
	setTimeout(() => {
		if (activeKey.value) {
			state.treeNode = routeTabs.value.find(item => item.key == activeKey.value).treeNode
		}
		document.querySelector('.ant-tabs-nav-list').offsetWidth > document.querySelector('.ant-tabs-nav-wrap').offsetWidth ? paddingSize.value = '40px' : paddingSize.value = 0
	}, 200)
}

const setTreeNode = (tree) => {
	store.setTreeNode(activeKey.value, tree)
	state.treeNode = tree
}
Mitt.on('setTreeNode', setTreeNode)

window.addEventListener('keydown', function(e) {
	if (e.keyCode == 83 && (navigator.platform.match('Mac') ? e.metaKey : e.ctrlKey)) {
		e.preventDefault()
		handleActionBar('save')
	}
})

const screenScale = () => {
	if (document.querySelector('.home-body').style) document.querySelector('.home-body').style.zoom = window.innerWidth <= 1200 ? 1200 / 1920 : window.innerWidth / 1920
}

onMounted(() => {
	if (isChromeHigh.value) {
		screenScale()
		const debouncedScreenScale = debounce(screenScale, 200)
		window.addEventListener('resize', debouncedScreenScale)
	}
	if (activeKey.value) {
		state.treeNode = routeTabs.value.find(item => item.key == activeKey.value).treeNode
	}
	document.querySelector('.ant-tabs-nav-list').offsetWidth > document.querySelector('.ant-tabs-nav-wrap').offsetWidth ? paddingSize.value = '40px' : paddingSize.value = 0

	if (navigator.userAgent.includes('Electron')) {
		tabBar.value.addEventListener('mousedown', async(e) => {
			const { screenX, screenY } = e
			const [x, y] = await window.electronApi.waitToMain('getPosition')
			const offsetX = screenX - x
			const offsetY = screenY - y
			const width = window.outerWidth
			const height = window.outerHeight
			const onMouseMove = (e) => {
				const { screenX, screenY } = e
				const x = screenX - offsetX
				const y = screenY - offsetY
				window.electronApi.sendToMain('move-window', { x, y, width, height })
			}
			const onMouseUp = () => {
				window.removeEventListener('mousemove', onMouseMove)
				window.removeEventListener('mouseup', onMouseUp)
			}
			window.addEventListener('mousemove', onMouseMove)
			window.addEventListener('mouseup', onMouseUp)
		})
		window.electronApi.receiveFromMain('open_file_from_electron', (args) => {
			if (args) {
				const find = routeTabs.value.find(item => item.filePath == args)
				if (find) {
					router.push(find.key)
				} else {
					const formdata = new FormData()
					formdata.append('file_path', args)
					importApi(formdata, true).then(res => {
						if (res.code == 1) {
							state.fileName = res.file_name
							state.filePath = res.file_path
							if (res.file_name.includes('.tr')) {
								if (res.sim_mode) {
									addNewTab({
										name: t('结果查看') + `-${removeAfterDot(res.file_name)}-${state.sim_job_config[res.sim_mode].full_name}`,
										type: res.sim_mode,
										filePath: res.file_path
									})
								} else {
									const find = Object.keys(state.sim_job_config).map(item => {
										return {
											name: state.sim_job_config[item].full_name,
											job_id: state.sim_job_config[item].job_id,
											type: item
										}
									}).find(items => items.job_id == res.job_type_id)
									addNewTab({
										name: t('结果查看') + `-${removeAfterDot(res.file_name)}-${find.name}`,
										type: find.type,
										filePath: res.file_path
									})
								}
								storeLoading.hiddenModal()
							} else {
								addNewTab({
									name: t('编辑器') + `-${res.file_name}`,
									type: 'isEditor',
									filePath: res.file_path
								})
							}
						}
					}).catch(() => {
						storeLoading.hiddenModal()
					})
				}
			} else {
				if (!activeKey.value) {
					// logger.debug('activeKey.value', activeKey.value)
				} else {
					router.push(activeKey.value)
				}
			}
		})
	} else {
		if (!activeKey.value) {
			// logger.debug('activeKey.value', activeKey.value)
		} else {
			router.push(activeKey.value)
		}
	}

	const tabList = document.querySelector('.ant-tabs-nav-list')

	Sortable.create(tabList, {
		animation: 150,

		direction: 'horizontal',
		ghostClass: 'ghostClass',
		chosenClass: 'chosenClass',
		dragClass: 'dragClass',
		onEnd: function(evt) {
			const oldIndex = evt.oldIndex
			const newIndex = evt.newIndex
			const elementToMove = routeTabs.value.splice(oldIndex, 1)[0]
			if (!elementToMove) return
			routeTabs.value.splice(newIndex, 0, elementToMove)
			store.draggleSortTabs(routeTabs.value)
		}

	})
})
const beforeDestroy = () => {
	localStorage.setItem('activeKey', activeKey.value)
	localStorage.setItem('routeTabs', routeTabs.value ? JSON.stringify(routeTabs.value) : '')
	localStorage.setItem('routeCache', routeCache.value ? JSON.stringify(routeCache.value) : '')
}
onUnmounted(() => {
	window.removeEventListener('beforeunload', beforeDestroy)
})
window.addEventListener('beforeunload', beforeDestroy)

</script>
<style lang="scss" scoped>
::v-deep .ant-tabs-nav {
  margin: 0;
}
.table-enter-active,
  .table-leave-active {
    transition: all 0.5s linear;
  }
  .table-leave-to,.table-enter-from {
    transform: translateY(100%);
  }
.home-body {
  background-color: #f8f8f8;
  width: 100%;
  height: 100%;
  // background-color: #a7b9f1;
  box-sizing: border-box;
  overflow: hidden;
  .header {
    background: linear-gradient(180deg, var(--theme-header-color) 8%, var(--theme-header-color1) 132%);
    border: 1px solid #A2B5CC;
  }
  .menu_icon{
    font-size: 24px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    right:0;
    top: -1px;
    z-index: 9999;
    span{
      color: #000;
      padding: 5px;
      &:hover{
        cursor: pointer;
        background-color: #ccc;
      }
      &:active{
        background-color: #0564bd;
      }
    }
    >span:nth-child(2){
      margin: 0 5px;
    }
    >span:last-child{
      &:hover{
        background-color: red;
      }
      &:active{
        background-color: #0564bd;
      }
    }
  }
  .control_btn{
    position: fixed;
    right: 1px;
    bottom: 2px;
    height: 38px;
    width: 680px;
    // width: 675px;
    z-index: 99;
    border: 1px solid #A2B5CC;
    font-size: 12px;
    border-radius: 4px;

    // >span{
    //   font-size: 24px;
    //   color: #fff;
    // }
    &:hover{
      cursor: pointer;
    }
    .control_up_icon {
      width: 100%;
      height: 11px;
      background-color: #90a0bb;
      text-align: center;
      line-height: 5px;
      color: #fff;
    }
    .control_status_card {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      height: 24px;
      padding: 2px 20px;
      background-color: #f4f9fc;
      box-sizing: border-box;
      line-height: 20px;
      color: #6c6d6f;
      >div {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      >div:last-child {
        padding-left: 20px;
        border-left: 2px solid #A2B5CC;
      }
    }
  }
  .title {
    width: 100%;
    height: 35px;
    text-align: center;
    line-height: 35px;
    // background: #ECF0F2;
    box-sizing: border-box;
    // border: 1px solid #A2B5CC;
    position: relative;
    // font-size: 18px;
    @include add-size(18px, $size);
    font-weight: normal;
    font-family: 'SiYuan Medium',Serif;
    color: #1E3D59;
    img{
      height: 31px;
      position: absolute;
      left: 15px;
      top: 1px;
    }
  }
  .header_tabs {
    width: 100%;
    height: 40px;
    padding-left: 10px;
    padding-top: 2px;
    // padding: 0 15px 0 10px;
    // background: linear-gradient(to right, #02549F, #8099C0);
    // background: linear-gradient(90deg, #DBE5EF 2%, #D8E8F8 99%);
    box-sizing: border-box;
    // border: 1px solid #A2B5CC;
    display: flex;
    // font-size: 18px;
    @include add-size(18px, $size);
    font-family: 'SiYuan Medium',Serif;
    color: #1E3D59;
    font-weight: normal;
    // overflow: hidden;
    >img{
      height: 36px;
      width: 36px;
      margin-right: 10px;
    }
    div {
      width: 120px;
      height: 38px;
      margin-right: 2px;
      margin-top: 2px;
      cursor: pointer;
      border-radius: 4px 4px 0 0;
      text-align: center;
      line-height: 40px;
    }
    .activeTab {
      height: 37px;
      // margin-top: 2px;
      border-radius: 6px 6px 0 0;
      background-color: #FFFFFF;
      border: 1px solid #A2B5CC;
      border-bottom: 0;
    }
    // .defultTab {
      // background: linear-gradient(to bottom, #3470AC, #0A4C8A);
      // box-shadow: #7FA1D3 1px -1px 1px;
      // color: #fff;
    // }
  }
  .header_main {
    width: 100%;
    height: 120px;
    // padding: 5px 0;
    // background-color: #dbdee7;
    // background: linear-gradient(to bottom, #e2e2e2, #acacac);
    // background: linear-gradient(to bottom, #FAFAFA, #ECEDEA);
    background: linear-gradient(180deg, var(--theme-toolbar-color) 0%, var(--theme-toolbar-color) 101%);
    border-bottom: 1px solid #c3c6d4;
    display: flex;
    color: #1E3D59;
    font-family: 'SiYuan Medium',Serif;
    .header-type {
      border-right: 1px solid #A2B5CC;
      text-align: center;
      .header-btn {
        display: flex;
        height: 100px;
        // background: linear-gradient(180deg, #FFFFFF 1%, #EDF0F2 96%);
        padding: 0 6px 0  24px;
        .header-item {
          padding: 10px 6px 2px 6px;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 4px 0px;
          border-radius: 4px;
          position: relative;
          // background-color: #b0b3c3;
          p {
            // padding: 0 14px;
            line-height: 24px;
            // font-size: 16px;
            @include add-size(16px, $size);
            // color: #262729;
          }
          img {
            height: 48px;
            width: 42px;
            // padding-top: 8px;
            // box-sizing: content-box;
          }
          >div {
            width: 100px;
          }
          .autoLine {
            display: none;
          }
          .activeLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #BAC9DB;
          }
          .activeChoicveLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #F6E093;
          }
          .dropDownIcon {
            width: 100%;
            position: absolute;
            bottom: -5px;
          }
          .dropDownList {
            position: absolute;
            width: 183px;
            height: 53px;
            bottom: -55px;
            left: 0px;
            background: #fff;
            z-index: 101;
            border-radius: 2px;
            box-shadow: #8099C0  0 0 2px 0;
            &:deep(){
                .ant-upload-wrapper,.ant-upload{
                  display: block;
                  height: 100%;
                  width: 100%;
                  .upload_box {
                    display: flex;
                    line-height: 25px;
                    .dropIcon_box {
                      width: 37px;
                      height: 100%;
                      margin-right: 12px;
                      background-color: #EBEFF4;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      img {
                        width: 16px;
                        height: 16px;
                        margin-left: 10px;
                      }
                    }
                    // .dropIcon_box_auto {
                    //   background-color: #EBEFF4;
                    // }
                    .dropIcon_box_active {
                      background-color: #D5D5D5;
                    }
                  }

                }
                span.ant-upload{
                  display: flex;
                  align-items: left;
                  flex-direction: column;
                }
              }
            >div {
              width: 100%;
              height: 50%;
              // font-size: 15px;
              @include add-size(18px, $size);
              line-height: 25px;
              // color: #1B252B;
              display: flex;
              border-top: 1px solid #BAC9DB;
              .dropIcon_box {
                width: 37px;
                height: 100%;
                margin-right: 12px;
                background-color: #EBEFF4;
                display: flex;
                flex-direction: column;
                justify-content: center;
                img {
                  width: 16px;
                  height: 16px;
                  margin-left: 10px;
                }
              }
              .dropIcon_box_active {
                background-color: #D5D5D5;
              }
            }
            .dropDownActive {
              background-color: #D5D5D5;
            }
          }
        }
        .header-item-merge {
          padding: 10px 6px 2px 6px;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 4px 0px;
          border-radius: 4px;
          position: relative;
          // background-color: #b0b3c3;

          >div {
            width: 100px;
          }
          .autoLine {
            display: none;
          }
          .activeLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #BAC9DB;
          }
          .activeChoicveLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #F6E093;
          }
          :deep(.ant-upload-wrapper) {
            font-size: 16px;
            line-height: 0;
            color: #1E3D59;
            p {
              font-family: 'SiYuan Medium',Serif;
              // padding: 0 14px;
              font-weight: 500;
              font-size: 16px;
              line-height: 24px;
              // font-size: 16px;
              @include add-size(16px, $size);
              // color: #262729;
            }
            img {
              height: 48px;
              width: 42px;
              // padding-top: 8px;
              // box-sizing: content-box;
            }
          }

          // .dropDownIcon {
          //   width: 100%;
          //   position: absolute;
          //   bottom: -5px;
          // }

            // &:deep(){
            //     .ant-upload-wrapper,.ant-upload{
            //       display: block;
            //       height: 100%;
            //       width: 100%;
            //       .upload_box {
            //         display: flex;
            //         line-height: 25px;
            //         img {
            //           height: 48px;
            //           width: 42px;
            //           // padding-top: 8px;
            //           // box-sizing: content-box;
            //         }
            //         // .dropIcon_box_auto {
            //         //   background-color: #EBEFF4;
            //         // }
            //         .dropIcon_box_active {
            //           background-color: #D5D5D5;
            //         }
            //       }

            //     }
            //     span.ant-upload{
            //       display: flex;
            //       align-items: left;
            //       flex-direction: column;
            //     }
            //   }

        }
        .header-create-item {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          border-radius: 4px;
          // font-size: 16px;
          @include add-size(16px, $size);
          // color: #262729;
          margin: 4px 0;
          position: relative;
          .img_box {
            width: 100%;
            height: 60%;
            // background-color: #8099C0;
            padding: 10px 13px 0 13px;
            img {
              width: 42px;
              height: 48px;
            }
          }
          .text_box{
            width: 100%;
            height: 40%;
            padding: 4px 15px 0 15px;
            line-height: 24px;
            // font-size: 14px;
            @include add-size(16px, $size);
          }
          .autoLine {
            display: none;
          }
          .activeLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #BAC9DB;
          }
          .activeChoicveLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #F6E093;
          }
          .dropDownIcon {
            width: 100%;
            position: absolute;
            bottom: -5px;
            font-size: 14px;
            // color: #606166;
          }
          .dropDownList {
            position: absolute;
            width: 183px;
            // height: 53px;
            bottom: -50px;
            left: 0px;
            background: #fff;
            z-index: 101;
            border-radius: 2px;
            box-shadow: #8099C0  0 0 2px 0;
            >div {
              width: 100%;
              height: 25px;
              font-size: 15px;
              line-height: 25px;
              // color: #1B252B;
              display: flex;
              border-top: 1px solid #BAC9DB;
              .dropIcon_box {
                width: 37px;
                height: 100%;
                margin-right: 12px;
                background-color: #EBEFF4;
                display: flex;
                flex-direction: column;
                justify-content: center;
                img {
                  width: 16px;
                  height: 16px;
                  margin-left: 10px;
                }
              }
              .dropIcon_box_active {
                background-color: #D5D5D5;
              }
            }
            .dropDownActive {
              background-color: #D5D5D5;
            }
          }
        }
        .header-import-item {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          border-radius: 4px;
          // font-size: 16px;
          @include add-size(16px, $size);
          // color: #262729;
          margin: 4px 0;
          position: relative;

          .upload_all_box {
            width: 100%;
            height: 60%;
            // background-color: #8099C0;
            padding: 10px 13px 0 13px;
            // img {
            //   width: 42px;
            //   height: 48px;
            // }
            &:deep(){
            .ant-upload-wrapper,.ant-upload{
              display: block;
              height: 100%;
              width: 100%;
            }
            // span.ant-upload{
            //   display: flex;
            //   align-items: left;
            //   flex-direction: column;
            // }
          }
          }
          .text_box{
            width: 100%;
            height: 40%;
            padding: 4px 15px 0 15px;
            line-height: 24px;
            // font-size: 14px;
            @include add-size(16px, $size);
          }
          .autoLine {
            display: none;
          }
          .activeLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #BAC9DB;
          }
          .activeChoicveLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #F6E093;
          }
          .dropDownIcon {
            width: 100%;
            position: absolute;
            bottom: -5px;
            font-size: 14px;
            // color: #606166;
          }
          .dropDownList {
            position: absolute;
            width: 183px;
            height: 80px;
            bottom: -80px;
            left: 0px;
            background: #fff;
            z-index: 101;
            border-radius: 2px;
            box-shadow: #8099C0  0 0 2px 0;
            &:deep(){
                .ant-upload-wrapper,.ant-upload{
                  display: block;
                  height: 100%;
                  width: 100%;
                  .upload_box {
                    display: flex;
                    line-height: 25px;
                    .dropIcon_box {
                      width: 37px;
                      height: 100%;
                      margin-right: 12px;
                      background-color: #EBEFF4;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      img {
                        width: 16px;
                        height: 16px;
                        margin-left: 10px;
                      }
                    }
                    // .dropIcon_box_auto {
                    //   background-color: #EBEFF4;
                    // }
                    .dropIcon_box_active {
                      background-color: #D5D5D5;
                    }
                  }

                }
                span.ant-upload{
                  display: flex;
                  align-items: left;
                  flex-direction: column;
                }
              }
            >div {
              width: 100%;
              height: 33.33%;
              font-size: 15px;
              line-height: 25px;
              // color: #1B252B;
              display: flex;
              border-top: 1px solid #BAC9DB;
              .dropIcon_box {
                width: 37px;
                height: 100%;
                margin-right: 12px;
                background-color: #EBEFF4;
                display: flex;
                flex-direction: column;
                justify-content: center;
                img {
                  width: 16px;
                  height: 16px;
                  margin-left: 10px;
                }
              }
              .dropIcon_box_active {
                background-color: #D5D5D5;
              }
            }
            .dropDownActive {
              background-color: #D5D5D5;
            }
          }
          .dropDownList_gis{
            height: 75px;
            bottom: -75px;
            >div{
              height: 25px;
            }
            .upload_box {
              width: 100%;
              display: flex;
              line-height: 25px;
              .dropIcon_box {
                width: 37px;
                height: 100%;
                margin-right: 12px;
                background-color: #EBEFF4;
                display: flex;
                flex-direction: column;
                justify-content: center;
                img {
                  width: 16px;
                  height: 16px;
                  margin-left: 10px;
                }
              }
              // .dropIcon_box_auto {
              //   background-color: #EBEFF4;
              // }
              .dropIcon_box_active {
                background-color: #D5D5D5;
              }
            }
          }
        }
        .header-save-item {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          border-radius: 4px;
          // font-size: 16px;
          @include add-size(16px, $size);
          // color: #262729;
          margin: 4px 0;
          position: relative;
          .img_box {
            width: 100%;
            height: 60%;
            // background-color: #8099C0;
            padding: 10px 13px 0 13px;
            img {
              width: 42px;
              height: 48px;
              object-fit: contain;
            }
          }
          .text_box{
            width: 100%;
            height: 40%;
            padding: 4px 15px 0 15px;
            line-height: 24px;
            // font-size: 14px;
            @include add-size(16px, $size);
          }
          .autoLine {
            display: none;
          }
          .activeLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #BAC9DB;
          }
          .activeChoicveLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #F6E093;
          }
          .dropDownIcon {
            width: 100%;
            position: absolute;
            bottom: -5px;
            font-size: 14px;
            // color: #606166;
          }
          .dropDownList {
            position: absolute;
            width: 183px;
            // height: 53px;
            bottom: -75px;
            left: 0px;
            background: #fff;
            z-index: 101;
            border-radius: 2px;
            box-shadow: #8099C0  0 0 2px 0;
            >div {
              width: 100%;
              height: 25px;
              font-size: 15px;
              line-height: 25px;
              // color: #1B252B;
              display: flex;
              border-top: 1px solid #BAC9DB;
              .dropIcon_box {
                width: 37px;
                height: 100%;
                margin-right: 12px;
                background-color: #EBEFF4;
                display: flex;
                flex-direction: column;
                justify-content: center;
                img {
                  width: 16px;
                  height: 16px;
                  margin-left: 10px;
                }
              }
              .dropIcon_box_active {
                background-color: #D5D5D5;
              }
            }
            .dropDownActive {
              background-color: #D5D5D5;
            }
          }
          .dropDownList1 {
            position: absolute;
            width: 183px;
            // height: 53px;
            bottom: -100px;
            left: 0px;
            background: #fff;
            z-index: 101;
            border-radius: 2px;
            box-shadow: #8099C0  0 0 2px 0;
            >div {
              width: 100%;
              height: 25px;
              font-size: 15px;
              line-height: 25px;
              // color: #1B252B;
              display: flex;
              border-top: 1px solid #BAC9DB;
              .dropIcon_box {
                width: 37px;
                height: 100%;
                margin-right: 12px;
                background-color: #EBEFF4;
                display: flex;
                flex-direction: column;
                justify-content: center;
                img {
                  width: 16px;
                  height: 16px;
                  margin-left: 10px;
                }
              }
              .dropIcon_box_active {
                background-color: #D5D5D5;
              }
            }
            .dropDownActive {
              background-color: #D5D5D5;
            }
          }
          .dropDownList1_gis{
            bottom: -26px;
          }
          .dropDownList1_gis_s{
            bottom: -125px;
          }
          .dropDownList2_gis{
            bottom: -50px;
          }
        }
        .header-view-item {
          padding: 10px 6px 2px 6px;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin: 4px 0px;
          border-radius: 4px;
          position: relative;
          // background-color: #b0b3c3;
          p {
            // padding: 0 14px;
            line-height: 24px;
            // font-size: 16px;
            @include add-size(16px, $size);
            // color: #262729;
          }
          img {
            height: 48px;
            width: 42px;
            // padding-top: 8px;
            // box-sizing: content-box;
          }
          >div {
            width: 100px;
          }
          .autoLine {
            display: none;
          }
          .activeLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #BAC9DB;
          }
          .activeChoicveLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #F6E093;
          }
          .dropDownIcon {
            width: 100%;
            position: absolute;
            bottom: -5px;
          }
          .dropDownList {
            position: absolute;
            width: 183px;
            // height: 53px;
            bottom: -50px;
            left: 0px;
            background: #fff;
            z-index: 101;
            border-radius: 2px;
            box-shadow: #8099C0  0 0 2px 0;
            &:deep(){
                .ant-upload-wrapper,.ant-upload{
                  display: block;
                  height: 100%;
                  width: 100%;
                  .upload_box {
                    display: flex;
                    line-height: 25px;
                    .dropIcon_box {
                      width: 37px;
                      height: 100%;
                      margin-right: 12px;
                      background-color: #EBEFF4;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      img {
                        width: 16px;
                        height: 16px;
                        margin-left: 10px;
                      }
                    }
                    // .dropIcon_box_auto {
                    //   background-color: #EBEFF4;
                    // }
                    .dropIcon_box_active {
                      background-color: #D5D5D5;
                    }
                  }

                }
                span.ant-upload{
                  display: flex;
                  align-items: left;
                  flex-direction: column;
                }
              }
            >div {
              width: 100%;
              height: 25px;
              font-size: 15px;
              line-height: 25px;
              // color: #1B252B;
              display: flex;
              border-top: 1px solid #BAC9DB;
              .dropIcon_box {
                width: 37px;
                height: 100%;
                margin-right: 12px;
                background-color: #EBEFF4;
                display: flex;
                flex-direction: column;
                justify-content: center;
                img {
                  width: 16px;
                  height: 16px;
                  margin-left: 10px;
                }
              }
              .dropIcon_box_active {
                background-color: #D5D5D5;
              }
            }
            .dropDownActive {
              background-color: #D5D5D5;
            }
          }
        }
        .bgShadow {
          border-radius: 4px;
          background: linear-gradient(180deg, #C8D4E2 0%, #DFE3E9 63%, #F4F4F4 100%);
          box-shadow: 0px 0px 1px 0px #728BB1,inset 0px 0px 2px 0px #769FCB;
        }
        .choiceBgShadow {
          border-radius: 4px;
          background: linear-gradient(180deg, #3C96FF 0%, #93BFFF 63%, #C9DEFF 100%);
          box-shadow: inset 0px 0px 2px 0px #55A3FF;
        }
      }
      .header-tool {
        display: flex;
        height: 100px;
        padding: 4px 6px 4px 12px;
        // background: linear-gradient(180deg, #FFFFFF 1%, #EDF0F2 96%);
        color: #1E3D59;

        .header-item {
          display: flex;
          flex-direction: column;
          // align-items: center;
          justify-content: space-between;
          border-radius: 4px;
          padding: 6px 1px;

          p {
            // font-size: 16px;
            @include add-size(16px, $size);
            // color: #262729;
          }
          >div {
            position: relative;
            // width: 100px;
            padding: 0 7px;
            display: flex;
            // justify-content: space-between;
            .dropDownList {
              width: 183px;
              position: absolute;
              top: 23px;
              left: 0px;
              background: #fff;
              z-index: 101;
              border-radius: 2px;
              box-shadow: #8099C0  0 0 2px 0;
              >div {
                width: 100%;
                height: 25px;
                font-size: 15px;
                line-height: 25px;
                // color: #1B252B;
                display: flex;
                border-top: 1px solid #BAC9DB;
                .dropIcon_box {
                  width: 37px;
                  height: 100%;
                  margin-right: 12px;
                  background-color: #EBEFF4;
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  img {
                    width: 16px;
                    height: 16px;
                    margin-left: 10px;
                  }
                }
                .dropIcon_box_active {
                  background-color: #D5D5D5;
                }
              }
              .dropDownActive {
                background-color: #D5D5D5;
              }
            }

            >img {
              height: 16px;
              width: 16px;
              margin: 3px 4px 0 0;
            }

          }

        }
        .header-tool-item {
          padding: 10px 6px 2px 6px;
          display: flex;
          flex-direction: column;
          align-items: center;
          border-radius: 4px;
          position: relative;
          p {
            line-height: 24px;
            // font-size: 14px;
            @include add-size(16px, $size);
            // color: #1E3D59;
          }
          img {
            height: 48px;
            width: 42px;
          }
          .autoLine {
            display: none;
          }
          .dropDownIcon {
            width: 100%;
            position: absolute;
            bottom: -5px;
          }
          .dropDownList {
            position: absolute;
            width: 183px;
            // height: 53px;
            bottom: -50px;
            left: 0px;
            background: #fff;
            z-index: 101;
            border-radius: 2px;
            box-shadow: #8099C0  0 0 2px 0;
            &:deep(){
                .ant-upload-wrapper,.ant-upload{
                  display: block;
                  height: 100%;
                  width: 100%;
                  .upload_box {
                    display: flex;
                    line-height: 25px;
                    .dropIcon_box {
                      width: 37px;
                      height: 100%;
                      margin-right: 12px;
                      background-color: #EBEFF4;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      img {
                        width: 16px;
                        height: 16px;
                        margin-left: 10px;
                      }
                    }
                    // .dropIcon_box_auto {
                    //   background-color: #EBEFF4;
                    // }
                    .dropIcon_box_active {
                      background-color: #D5D5D5;
                    }
                  }

                }
                span.ant-upload{
                  display: flex;
                  align-items: left;
                  flex-direction: column;
                }
              }
            >div {
              width: 100%;
              height: 25px;
              font-size: 15px;
              line-height: 25px;
              // color: #1B252B;
              display: flex;
              border-top: 1px solid #BAC9DB;
              .dropIcon_box {
                width: 37px;
                height: 100%;
                margin-right: 12px;
                background-color: #EBEFF4;
                display: flex;
                flex-direction: column;
                justify-content: center;
                img {
                  width: 16px;
                  height: 16px;
                  margin-left: 10px;
                }
              }
              .dropIcon_box_active {
                background-color: #D5D5D5;
              }
            }
            .dropDownActive {
              background-color: #D5D5D5;
            }
          }
        }
        .header-tool-item-batch{
          padding: 10px 6px 2px 6px;
          display: flex;
          flex-direction: column;
          align-items: center;
          border-radius: 4px;
          position: relative;
          p {
            line-height: 24px;
            // font-size: 14px;
            @include add-size(16px, $size);
            // color: #1E3D59;
          }
          img {
            height: 48px;
            width: 42px;
          }
          .autoLine {
            display: none;
          }
          .dropDownIcon {
            width: 100%;
            position: absolute;
            bottom: -5px;
          }
          .dropDownList {
            position: absolute;
            width: 183px;
            // height: 53px;
            bottom: -75px;
            left: 0px;
            background: #fff;
            z-index: 101;
            border-radius: 2px;
            box-shadow: #8099C0  0 0 2px 0;
            &:deep(){
                .ant-upload-wrapper,.ant-upload{
                  display: block;
                  height: 100%;
                  width: 100%;
                  .upload_box {
                    display: flex;
                    line-height: 25px;
                    .dropIcon_box {
                      width: 37px;
                      height: 100%;
                      margin-right: 12px;
                      background-color: #EBEFF4;
                      display: flex;
                      flex-direction: column;
                      justify-content: center;
                      img {
                        width: 16px;
                        height: 16px;
                        margin-left: 10px;
                      }
                    }
                    // .dropIcon_box_auto {
                    //   background-color: #EBEFF4;
                    // }
                    .dropIcon_box_active {
                      background-color: #D5D5D5;
                    }
                  }

                }
                span.ant-upload{
                  display: flex;
                  align-items: left;
                  flex-direction: column;
                }
              }
            >div {
              width: 100%;
              height: 25px;
              font-size: 15px;
              line-height: 25px;
              // color: #1B252B;
              display: flex;
              border-top: 1px solid #BAC9DB;
              .dropIcon_box {
                width: 37px;
                height: 100%;
                margin-right: 12px;
                background-color: #EBEFF4;
                display: flex;
                flex-direction: column;
                justify-content: center;
                img {
                  width: 16px;
                  height: 16px;
                  margin-left: 10px;
                }
              }
              .dropIcon_box_active {
                background-color: #D5D5D5;
              }
            }
            .dropDownActive {
              background-color: #D5D5D5;
            }
          }
        }
        .header-tool-item-params {
          padding: 10px 6px 2px 6px;
          display: flex;
          flex-direction: column;
          align-items: center;
          border-radius: 4px;
          position: relative;

          .img_box {
            width: 100%;
            height: 60%;
            // padding: 10px 13px 0 13px;
            img {
              height: 48px;
              width: 42px;
            }
          }
          .text_box{
            width: 100%;
            height: 40%;
            // padding: 2px 15px 0 15px;
            line-height: 24px;
            // font-size: 14px;
            @include add-size(16px, $size);
            // color: #262729;
          }
          .autoLine {
            display: none;
          }
          .activeLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #BAC9DB;
          }
          .activeChoicveLine {
            position: absolute;
            top: 54px;
            width: 100%;
            height: 1px;
            // border: 1px solid #769FCB;
            background-color: #F6E093;
          }
          .dropDownIcon {
            width: 100%;
            position: absolute;
            bottom: -5px;
            font-size: 14px;
            // color: #606166;
          }
          .dropDownList {
            position: absolute;
            width: 183px;
            // height: 53px;
            bottom: -50px;
            left: 0px;
            background: #fff;
            z-index: 101;
            border-radius: 2px;
            box-shadow: #8099C0  0 0 2px 0;
            >div {
              width: 100%;
              height: 25px;
              font-size: 15px;
              line-height: 25px;
              // color: #1B252B;
              display: flex;
              border-top: 1px solid #BAC9DB;
              .dropIcon_box {
                width: 37px;
                height: 100%;
                margin-right: 12px;
                background-color: #EBEFF4;
                display: flex;
                flex-direction: column;
                justify-content: center;
                img {
                  width: 16px;
                  height: 16px;
                  margin-left: 10px;
                }
              }
              .dropIcon_box_active {
                background-color: #D5D5D5;
              }
            }
            .dropDownActive {
              background-color: #D5D5D5;
            }
          }
        }
        .bgShadow {
          border-radius: 4px;
          // background-color: #b4b6c5;
          background: linear-gradient(180deg, #C8D4E2 0%, #DFE3E9 63%, #F4F4F4 100%);
          box-shadow: 0px 0px 1px 0px #728BB1,inset 0px 0px 2px 0px #769FCB;
        }
        .choiceBgShadow {
          border-radius: 4px;
          background: linear-gradient(180deg, #3C96FF 0%, #93BFFF 63%, #C9DEFF 100%);
          box-shadow: inset 0px 0px 2px 0px #55A3FF;
        }

      }
      .header_typeName {
        height: 20px;
        line-height: 20px;
        // font-size: 12px;
        @include add-size(12px, $size);
        color: #888e93;
        // background-color: #d9d6d6;
        background: rgba(194, 212, 229, 0.29);
        box-shadow: inset 0px 1px 1px 0px rgba(194, 212, 229, 0.6745);
      }
    }
  }
  .main {
    width: 100%;
    height: calc(100% - 35px - 40px - 120px - 30px);
    // border: 1px solid #606166;
    background-color: var(--theme-bg-color);
    // background-size: cover;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    box-sizing: border-box;
    font-family: 'SiYuan Normal',Serif;
    .main-tabs {
      width: 100%;
      padding: 2px 0 0 10px;
      background: var(--theme-tabs-color);
      border: 1px solid #A2B5CC;
      border-top: 0;
      .dot {
        height: 10px;
        width: 10px;
        background-color: #d89226;
        border-radius: 50%;
        display:inline-block;
      }
      &:deep(){
        .ant-tabs-tab{
          padding:3px 15px;
          background: #BAD6EA;
        }
      }
      :deep(.ant-tabs-card >.ant-tabs-nav .ant-tabs-tab-active,
      .ant-tabs-card >div>.ant-tabs-nav .ant-tabs-tab-active) {
        color: #474747 !important;
        background: #F4F7FA!important;
        border-bottom-color: #F4F7FA!important;
      }
      :deep(.ant-tabs-nav)  {
        position: relative;
        padding:  0 10px 0 40px;
        padding-left: v-bind(paddingSize);
      }
      :deep(.ant-tabs-nav-operations)  {
        position: absolute;
        left: -10px;
        top: 0;
      }

    }
  }
  .main-bg-prsas {
    background-image: url('@/assets/bg-img/themeBG-prsas.webp');
  }
  .main-bg-js {
    background-image: url('@/assets/bg-img/themeBG-js.jpeg');
  }
  .main-bg {
    background-image: url('@/assets/bg-img/themeBG.webp');
  }
  .main_router{
    height: calc(100% - 33px);
  }
}
</style>
