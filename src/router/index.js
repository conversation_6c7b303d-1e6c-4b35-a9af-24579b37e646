import { createWebHashHistory, createRouter } from 'vue-router'
import { routeStore } from '@/store/routeStore'
import { storeToRefs } from 'pinia'

const routes = [
	{
		path: '/',
		name: 'layout',

		component: () => import('../views/layout/index.vue'),
		children: [

			{
				path: '/detail',
				name: 'detail',
				component: () => import('../views/layout/detail.vue')
			},
			{
				path: '/result',
				name: 'result',
				component: () => import('../views/layout/result.vue')
			},
			{
				path: '/resultGis',
				name: 'resultGis',
				component: () => import('../views/layout/resultGis.vue')
			},
			{
				path: '/resultTeap',
				name: 'resultTeap',
				component: () => import('../views/layout/resultTeap.vue')
			},
			{
				path: '/params',
				name: 'params',
				component: () => import('../views/layout/params.vue')
			},
			{
				path: '/gis',
				name: 'gis',
				component: () => import('../views/layout/gis.vue')
			}

		]
	},
	{
		path: '/errorPage',
		name: 'errorPage',
		component: () => import('../views/errorPage/404.vue')
	}

]

const router = createRouter({
	history: createWebHashHistory(),
	routes
})
router.beforeEach((to, from, next) => {
	const store = routeStore()
	const { routeCache, routeTabs } = storeToRefs(store)
	if (to.path == '/') {
		next()
		return
	}
	if (routeCache.value.length != 0 && routeCache.value.find(item => item == to.name)) {
		if (routeTabs.value.find(item => item.key == to.fullPath)) {
			store.setActive(to.fullPath)
			next()
		} else {
			store.addTabs(Object.assign({}, to.query, {
				key: to.fullPath,
				name: to.name,
				title: to.query.name,
				isUnsaved: to.query.type == 'isNewBuilt',
				isModalVisible: false,
				treeNode: 'bus'
			}))
			next()
		}
	} else {
		store.addTabs(Object.assign({}, to.query, {
			key: to.fullPath,
			name: to.name,
			title: to.query.name,
			isUnsaved: false,
			isModalVisible: false,
			treeNode: 'bus'
		}))
		next()
	}
})
export default router
