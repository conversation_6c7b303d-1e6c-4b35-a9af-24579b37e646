import { createApp } from 'vue'
import '@/styles/common.scss'
import App from './App.vue'
import i18n from './i18n'

import router from '@/router/index'

import AgGrid from '@/components/AG-Grid/index.vue'

import scrollLoadmore from './directive/el-scroll-loadmore/loadmore'
import pinia from '@/store/store'

import { RecycleScroller } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

import './SiYuanHeiTi/font.css'

const app = createApp(App)

app.directive('scrollLoadmore', scrollLoadmore)

app.component('AgGrid', AgGrid)
app.component('RecycleScroller', RecycleScroller)

app.use(router).use(i18n).use(pinia).mount('#app')
