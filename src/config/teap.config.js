const userAgent = navigator.userAgent
let baseURL, websocket_url, download_url
if (userAgent.includes('Electron')) {
	baseURL = sessionStorage.getItem('baseURL') ? sessionStorage.getItem('baseURL') : window.config.baseURL + '/'
	websocket_url = sessionStorage.getItem('websocket_url') ? sessionStorage.getItem('websocket_url') : window.config.websocket_url
	download_url = sessionStorage.getItem('baseURL') ? sessionStorage.getItem('baseURL') : window.config.baseURL
} else {
	baseURL = ''
	websocket_url = (document.domain + ':' + location.port)
	download_url = ''
}
const network = {

	baseURL: process.env.NODE_ENV == 'development' ? '/teap3' : baseURL,
	download_Prefix: process.env.NODE_ENV == 'development' ? 'http://192.168.50.116:20427' : download_url,
	websocket_Prefix: process.env.NODE_ENV == 'development' ? '192.168.50.116:20427' : websocket_url,

	contentType: 'application/json;charset=UTF-8',

	requestTimeout: 60000,

	successCode: [1, 2, -2],
	errorCode: [0],
	lostFileCode: 1001,

	invalidCode: 402,

	noPermissionCode: 401
}
export default network
