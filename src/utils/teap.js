import { saveAs } from 'file-saver'
import { checkMemory } from '@/api/index'
import { notification, Modal } from 'ant-design-vue'
import { createVNode } from 'vue'
import { utils, writeFile } from 'xlsx'
import { t } from '@/utils/common'
import dayjs from 'dayjs'
import network from '@/config/teap.config'
export const downloadHtml = (html, fileName, isComplete) => {
	if (isComplete) {
		saveAs(new Blob([html]), fileName)
	} else {
		saveAs(new Blob([`<!doctype html>
        <html>
            <head>
            <meta charset="utf-8">
            <title>${fileName.split('.')[0]}</title>
            </head>
            <body>
                ${html}
            </body>
        </html>`]), fileName)
	}
}
export const getMoreFields = (isResult, type, data, dataMore) => {
	if (type == 0 || type == 2) {
		return Object.assign({
			up_reserve_cof: data.up_reserve_cof,
			down_reserve_cof: data.down_reserve_cof,
			emergency_reserve_cof: data.emergency_reserve_cof,
			wind_reserve_cof: data.wind_reserve_cof,
			solar_reserve_cof: data.solar_reserve_cof,
			consider_reserve_flag: data.consider_reserve_flag,
			consider_start_shut_flag: data.consider_start_shut_flag,
			consider_ramp_flag: data.consider_ramp_flag,
			consider_branch_flag: data.consider_branch_flag,
			consider_interface_flag: data.consider_interface_flag,
			consider_transmission_losses: data.consider_transmission_losses
		}, isResult ? {
			wind_rate: dataMore[0],
			solar_rate: dataMore[1],
			energy_rate: dataMore[2],
			emission: dataMore[3],
			gen_use_hours: dataMore[5],
			wind_use_hours: dataMore[6],
			solar_use_hours: dataMore[7]
		} : {}
		)
	} else {
		return Object.assign({
			up_reserve_cof: data.up_reserve_cof,
			down_reserve_cof: data.down_reserve_cof,
			emergency_reserve_cof: data.emergency_reserve_cof,
			wind_reserve_cof: data.wind_reserve_cof,
			solar_reserve_cof: data.solar_reserve_cof,
			consider_reserve_flag: data.consider_reserve_flag,
			consider_start_shut_flag: data.consider_start_shut_flag,
			consider_ramp_flag: data.consider_ramp_flag,
			consider_branch_flag: false,
			consider_interface_flag: false,
			consider_transmission_losses: false
		}, isResult ? {
			wind_rate: dataMore[0],
			solar_rate: dataMore[1],
			energy_rate: dataMore[2],
			emission: dataMore[3],
			gen_use_hours: dataMore[4],
			wind_use_hours: dataMore[5],
			solar_use_hours: dataMore[6]
		} : {}
		)
	}
}
export const getSortId = (id) => {
	let sort_id
	switch (id) {
		case 1:
			sort_id = 0
			break
		case 2:
			sort_id = 1
			break
		case 0:
			sort_id = 2
			break
		case 3:
			sort_id = 3
			break
		default:
			sort_id = 4
			break
	}

	return {
		sort_id
	}
}
export const getFilterData = (search, allDataArr) => {
	const filterArr = allDataArr.filter((item, index) => {
		return Object.keys(search).reduce((acc, k) => {
			const v = search[k] == undefined ? '' : search[k]
			const j = item[k] || item[k] == 0 ? item[k] : ''
			if (Object.prototype.toString.call(v) === '[object Array]') {
				return (v.includes(j) && acc)
			} else if (k == 'searchText') {
				const s = item.case_file_name.toUpperCase()
				const b = item.case_file_note.toUpperCase()
				return ((s.includes(v.toUpperCase()) || b.includes(v.toUpperCase())) && acc)
			} else {
				return (v ? j.includes(v) && acc : acc)
			}
		}, true)
	})
	return filterArr
}
export const getNotify = () => {
	checkMemory().then(res => {
		if (res.code == 0) {
			notification.warning({
				message: t('警告'),
				description: res.message,
				placement: 'bottomRight',
				duration: 30
			})
		}
	})
}
export const openModal = (val) => {
	return new Promise((resolve, reject) => {
		Modal.confirm({
			title: t('警告'),

			centered: true,
			content: createVNode('div', {
				style: 'color:red;'
			}, val),
			onOk() {
				resolve(true)
			},
			onCancel() {
				reject('reject')
			},
			class: 'confirm-modal'
		})
	})
}
export const openModals = (title, icon, content, okText, cancelText) => {
	return new Promise((resolve, reject) => {
		Modal.confirm({
			title: title,
			icon: icon ? createVNode(icon) : undefined,
			centered: true,
			content: createVNode('div', {
				style: ''
			}, content),
			okText: okText || t('确定'),
			cancelText: cancelText || t('取消'),
			onOk() {
				resolve(true)
			},
			onCancel() {
				reject('reject')
			},
			class: 'confirm-modal'
		})
	})
}

export const openErrorModal = (msg, val) => {
	return new Promise((resolve, reject) => {
		Modal.confirm({
			title: t('警告'),
			centered: true,
			content: createVNode('div', {
				style: 'color:red;font-size:12px;'
			}, msg),
			class: 'error-modal',

			keyboard: false,
			okText: val ? t('重新校验软证书') : t('重试'),
			cancelText: val ? t('申请新的软证书') : t('激活软证书'),
			onOk() {
				resolve()
			},
			onCancel() {
				reject()
			}
		})
	})
}
export const getLogList = (data, error, Info, warning) => {
	const log_value = data.reduce((a, b) => {
		let str
		if ((b[0] == 'Info' || !b[0]) && Info) {
			if (b[3]) {
				str = `<font style="line-break:break-word;">${b[1].slice(0, 14)} [Info] ${b[1].slice(14, b[1].toString().length)}</font>`
			} else {
				str = `<font style="line-break:break-word;white-space:pre">${b[1]}</font>`
			}
		} else if (b[0] == 'Warning' && warning) {
			if (b[3]) {
				str = `<font>${b[1].slice(0, 14)}</font><font style='color:#007bff;line-break:break-word;'> [Warning] ${b[1].slice(14, b[1].toString().length)}</font>`
			} else {
				str = `<font style='color:#007bff;line-break:break-word;white-space:pre'>${b[1]}</font>`
			}
		} else if (b[0] == 'Error' && error) {
			if (!b[3]) {
				str = `<font style='color:red;line-break:break-word;white-space:pre'>${b[1]}</font>`
			} else if (b[1].indexOf(t('日志文件')) != -1) {
				str = `<font>${b[1].slice(0, 14)}</font><font style='color:red;line-break:break-word;'> [Error] ${b[1].slice(14, b[1].indexOf(t('日志文件'))) + `<a style="text-decoration: underline;" href="${network.download_Prefix + '/backend/teap_api/download_task_log/'}">${t('日志文件')}</a>` + b[1].slice(b[1].indexOf(t('日志文件')) + 4, b[1].toString().length)}</font>`
			} else {
				str = `<font>${b[1].slice(0, 14)}</font><font style='color:red;line-break:break-word;'> [Error] ${b[1].slice(14, b[1].toString().length)}</font>`
			}
		} else {
			str = ''
		}
		return a + str + '<br>'
	}, '')
	return log_value || ''
}
export const generateDateTimeArray = (year, isNeedYear, isNeedTime) => {
	const dateTimeArray = []
	for (let month = 1; month <= 12; month++) {
		const daysInMonth = new Date(year, month, 0).getDate()
		for (let day = 1; day <= daysInMonth; day++) {
			for (let hour = 0; hour < 24; hour++) {
				const monthStr = month < 10 ? `0${month}` : `${month}`
				const dayStr = day < 10 ? `0${day}` : `${day}`
				const hourStr = hour < 10 ? `0${hour}` : `${hour}`
				const dateTimeString = isNeedYear
					? isNeedTime ? `${year}-${monthStr}-${dayStr} ${hourStr}:00:00` : `${year}-${monthStr}-${dayStr}`
					: isNeedTime ? `${monthStr}-${dayStr} ${hourStr}:00:00` : `${monthStr}-${dayStr}`
				dateTimeArray.push(dateTimeString)
			}
		}
	}
	return dateTimeArray
}
export const getMonthData = (data, currentYear) => {
	const monthData = Array.from({ length: 12 }, () => 0)

	let cumulativeSum = 0
	data.forEach((item, index) => {
		cumulativeSum += item
		const date = new Date(currentYear, 0, index + 1)
		const monthIndex = date.getMonth()
		monthData[monthIndex] += item
	})
	return monthData
}
export const getLineOption = (type, data, year, MAX, AVG, MIN, data2, data3) => {
	const name = type == 'load' ? t('负荷') : type == 'solar' ? t('光伏') : type == 'wind' ? t('风电') : t('风光')
	const color = type == 'load' ? '#D56D39' : type == 'solar' ? '#FFC451' : '#99E897'
	const markPoint = [].concat(MAX ? [
		{
			type: 'max', name: 'Max', itemStyle: {
				color: 'rgb(22,196,175)'
			}, label: {

			}}
	] : []).concat(MIN ? [
		{ type: 'min', name: 'MIN', itemStyle: {
			color: 'rgb(22,196,175)'
		}, label: {

		}}
	] : [])
	const markLine = [].concat(AVG ? [
		{
			type: 'average', name: 'Avg', lineStyle: {
				color: 'rgb(22,196,175)'
			}}
	] : [])
	const timeArr = generateDateTimeArray(year, true, true)
	const option = {
		tooltip: {
			trigger: 'axis'
		},
		legend: {},
		grid: {
			left: 70,
			right: 70,
			bottom: 65,
			top: 90
		},
		xAxis: {
			type: 'category',
			data: timeArr
		},
		yAxis: {
			type: 'value',
			name: type == 'load' ? t('万千瓦') : 'μ',
			nameTextStyle: {
				fontSize: 16
			}
		},
		dataZoom: {
			height: 25,
			start: 0,
			end: 100,

			bottom: 10
		},
		color: type == 'load' ? ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'] : type == 'windSolar' ? ['#99E897', '#FFC451'] : color,

		series: type == 'windSolar' ? ['wind_pwr_mw', 'solar_pwr_mw'].map(item => {
			return {
				name: item == 'wind_pwr_mw' ? t('风电') : t('光伏'),
				type: 'line',
				showSymbol: false,
				data: data.map(val => val[item]),
				markPoint: {
					data: markPoint
				},
				markLine: {
					data: markLine
				}
			}
		}) : [
			{
				name,
				type: 'line',
				showSymbol: false,
				data: type != 'load' ? data.map(item => item.pw_mw_val) : data,
				markPoint: {
					data: markPoint
				},
				markLine: {
					data: markLine
				}
			}
		].concat(type == 'load' ? [
			{
				name: t('第二产业负荷'),
				type: 'line',
				showSymbol: false,
				data: data2,
				markPoint: {
					data: markPoint
				},
				markLine: {
					data: markLine
				}
			},
			{
				name: t('第三产业+居民用电负荷'),
				type: 'line',
				showSymbol: false,
				data: data3,
				markPoint: {
					data: markPoint
				},
				markLine: {
					data: markLine
				}
			}
		] : [])
	}
	return option
}
export const getLineOptionSole = ({ data, total_row, time }, name) => {
	const timeArr = generateDateTimeArray(total_row == 8760 ? 2023 : 2024, false, true)
	const option = {
		tooltip: {
			trigger: 'axis'
		},
		grid: {
			left: 50,
			right: 50,
			bottom: 65,
			top: 50
		},
		xAxis: {
			type: 'category',

			data: time || timeArr
		},
		yAxis: {
			type: 'value',
			name: 'μ',
			nameTextStyle: {
				fontSize: 16
			}
		},
		dataZoom: {
			height: 25,
			start: 0,
			end: 100,

			bottom: 10
		},
		series: [
			{
				name,
				type: 'line',
				showSymbol: false,
				data: data
			}
		]
	}
	return option
}
export const getBarOption = ({ daily_power, importance_group }, pred_year, type) => {
	const datas = getMonthData(daily_power, pred_year)
	const option1 = {
		tooltip: {
			trigger: 'axis'
		},
		legend: {},
		grid: {
			left: 70,
			right: 70,
			bottom: 65,
			top: 90
		},
		xAxis: {
			type: 'category',
			data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
		},
		yAxis: {
			type: 'value',
			name: t('万千瓦时'),
			nameTextStyle: {
				fontSize: 16
			}
		},
		series: [
			{
				name: pred_year + t('年每月用电量'),
				type: 'bar',
				data: datas
			}
		]
	}
	const option2 = {
		tooltip: {
			trigger: 'axis'
		},
		legend: {},
		grid: {
			left: 70,
			right: 70,
			bottom: 65,
			top: 90
		},
		color: ['rgb(155,255,191)', 'rgb(250,200,89)'],
		xAxis: {
			type: 'category',
			data: [t('节假日类型'), t('星期几'), t('是否节假日'), t('最高气温'), t('最低气温'), t('平均气温'), t('月份')]
		},
		yAxis: {
			type: 'value',
			name: '',
			nameTextStyle: {
				fontSize: 16
			}
		},
		series: [
			{
				name: t('第二产业'),
				type: 'bar',
				data: importance_group.trend[type]
			},
			{
				name: t('第三产业+居民用电'),
				type: 'bar',
				data: importance_group.seasonal[type]
			}
		]
	}
	return { option1, option2 }
}
export function get_canvas(watermark_text) {
	const waterMarkText = watermark_text
	const canvas = document.createElement('canvas')
	const ctx = canvas.getContext('2d')
	canvas.width = canvas.height = 100
	ctx.textAlign = 'center'
	ctx.textBaseline = 'middle'
	ctx.globalAlpha = 0.08
	ctx.font = '20px Microsoft Yahei'
	ctx.translate(50, 50)
	ctx.rotate(-Math.PI / 4)
	ctx.fillText(waterMarkText, 0, 0)
	return canvas
}
export const getMidLineSeries = (charts_data, allLineConfig, selected_obj, charts_obj, add_watermark, watermark_text, fuc, changeLegendSelected, startIndex, endIndex, start, end, unitValue, unitName, flag) => {
	const lineConfig = allLineConfig.filter(item => Object.keys(charts_data).includes(item.lineName))
	const legendData = lineConfig.map(item => item.name)
	const legendColor = lineConfig.map(item => item.color)
	const defultselect = {}
	lineConfig.forEach(item => {
		if (item.default_show || item.default_show == undefined) {
			defultselect[item.name] = true
		} else {
			defultselect[item.name] = false
		}
	})

	const timeData = charts_data['time'].slice(startIndex, endIndex + 1)
	const option = {
		backgroundColor: add_watermark ? {
			type: 'pattern',
			image: get_canvas(watermark_text),
			repeat: 'repeat'
		} : undefined,
		color: legendColor,
		tooltip: {
			trigger: 'axis',
			triggerOn: 'click',
			backgroundColor: 'rgba(255,255,255,0.8)',
			enterable: true,
			formatter: function(datas) {
				let res = (datas[0].name + ' (' + t('第') + (1 + startIndex + datas[0].dataIndex) + t('小时') + ')' + '<br/>')
				for (let i = 0, length = datas.length; i < length; i++) {
					let format_value = datas[i].data
					const tempMarker = lineConfig.find(item => item.name == datas[i].seriesName).type !== 'area' ? `<span style="display:inline-block;margin-right:4px;width:10px;height:2px;margin-bottom: 4px;background-color:${datas[i].color};"></span>` : datas[i].marker
					if (format_value !== 0) { format_value = format_value.toFixed(2) }
					res += tempMarker + datas[i].seriesName + ': ' + format_value + '<br/>'
				}
				return res + (flag ? `<button class='echarts-btn' onClick=downloadBPI(${start + datas[0].dataIndex})>${t('下载BPA')}</button><br/>` : '')
			},
			axisPointer: {
				type: 'cross',
				label: {
					backgroundColor: '#6a7985'
				}
			}
		},
		toolbox: {
			show: true,
			top: 40,
			right: 0,
			emphasis: {
				iconStyle: {
					textPosition: 'top'
				}
			},
			feature: {
				mark: { show: true },
				myDownload: {
					show: true,
					title: t('下载数据'),
					icon: 'image://' + new URL(`@/assets/download_table.png`, import.meta.url).href,
					onclick: () => {
						fuc(charts_obj.getOption().legend[0].selected, charts_obj.getOption().legend[0].data.filter(item => item != '\n'))
					}
				},
				mySelectDefault: {
					show: true,
					title: t('全选默认'),
					icon: 'image://' + new URL(`@/assets/select_default.png`, import.meta.url).href,
					onclick: () => {
						const opt = charts_obj.getOption()

						opt.legend[0].selected = defultselect
						charts_obj.setOption(opt)
						changeLegendSelected(charts_obj.getOption().legend[0].selected, charts_obj.getOption().legend[0].data.filter(item => item != '\n'))
					}
				},
				mySelectAll: {
					show: true,
					title: t('全选所有'),
					icon: 'image://' + new URL(`@/assets/select_all.png`, import.meta.url).href,
					onclick: () => {
						charts_obj.dispatchAction({
							type: 'legendAllSelect'
						})
						changeLegendSelected(charts_obj.getOption().legend[0].selected, charts_obj.getOption().legend[0].data.filter(item => item != '\n'))
					}
				},
				myClearSelect: {
					show: true,
					title: t('全不选'),
					icon: 'image://' + new URL(`@/assets/clear_select.png`, import.meta.url).href,
					onclick: () => {
						const opt = charts_obj.getOption()
						const obj = {}
						legendData.forEach(item => {
							obj[item] = false
						})
						opt.legend[0].selected = obj
						charts_obj.setOption(opt)
						changeLegendSelected(charts_obj.getOption().legend[0].selected, charts_obj.getOption().legend[0].data.filter(item => item != '\n'))
					}
				},
				myReverseSelection: {
					show: true,
					title: t('反选'),
					icon: 'image://' + new URL(`@/assets/reverse_selection.png`, import.meta.url).href,
					onclick: () => {
						charts_obj.dispatchAction({
							type: 'legendInverseSelect'
						})
						changeLegendSelected(charts_obj.getOption().legend[0].selected, charts_obj.getOption().legend[0].data.filter(item => item != '\n'))
					}
				},
				saveAsImage: {
					show: true,
					title: t('保存为图片'),
					icon: 'image://' + new URL(`@/assets/download.png`, import.meta.url).href,
					name: '电力电量平衡_' + dayjs(new Date()).format('YYYYMMDD_HHmmss')
				},
				myReload: {
					show: true,
					title: t('刷新'),
					icon: 'image://' + new URL(`@/assets/refresh.png`, import.meta.url).href,
					onclick: () => {
						const opt = charts_obj.getOption()
						charts_obj.clear()
						charts_obj.setOption(opt)
					}
				}
			}
		},
		legend: {
			width: '85%',
			height: '100%',
			inactiveColor: '#837F7B',
			data: legendData,

			itemGap: 8,
			bottom: 30,
			top: 3,
			selected: selected_obj

		},
		grid: {
			left: 60,
			right: 60,
			bottom: 65,
			top: 68
		},
		xAxis: {
			type: 'category',
			data: timeData,
			name: t('时间'),
			nameTextStyle: {
				fontSize: 16,
				padding: [0, 0, 0, -5]
			},
			axisLabel: {
				fontSize: 12
			}
		},
		yAxis: {
			type: 'value',
			name: t('功率') + `(${unitName})`,
			nameTextStyle: {
				fontSize: 16,
				padding: 10
			},
			axisLabel: {
				fontSize: 12
			}
		},
		dataZoom: {
			height: 25,
			start,
			end: end || (24 * 7 * 100) / timeData.length

		},
		series: lineConfig.map(item => {
			return Object.assign({ ...item }, {
				emphasis: {
					disabled: true
				},
				symbol: 'circle',
				type: 'line',

				step: 'middle',
				showSymbol: false,
				color: item.color,
				data: (charts_data[item.lineName] ? (charts_data[item.lineName].map(item => item * unitValue)).slice(startIndex, endIndex + 1) : undefined),
				stack: item.type == 'area' ? '总量' : item.name
			}, item.type == 'area' ? {
				areaStyle: {
					opacity: 1
				},
				lineStyle: {
					width: 0
				}
			} : {
				lineStyle: {

					type: item.type.split('_')[0]
				}
			})
		}).filter(item => item.data).sort((a, b) => a.sequence - b.sequence)
	}
	return option
}
export const exportExcelTable = (charts_data, legend, legendData, lineConfig, sheetName, fileName, unitRatio) => {
	const timeData = charts_data['time']
	const power_balance_line_chart_name = {}
	lineConfig.forEach(item => {
		power_balance_line_chart_name[item.name] = item.lineName
	})
	const tableData = timeData.map((item, index) => {
		return unitRatio ? [item].concat(legendData.map(item => charts_data[power_balance_line_chart_name[item]][index] * unitRatio)) : [item].concat(legendData.map(item => charts_data[power_balance_line_chart_name[item]][index]))
	})

	const title = [t('时间')].concat(legendData)
	const wb = utils.book_new()
	tableData.unshift(title)
	const ws = utils.aoa_to_sheet(tableData)
	autoWidthFun(ws, tableData)
	utils.book_append_sheet(wb, ws, sheetName)
	writeFile(wb, fileName)
}
function autoWidthFun(ws, data) {
	const colWidth = data.map(row =>
		row.map(val => {
			if (val == null) {
				return { wch: 10 }
			} else if (val.toString().charCodeAt(0) > 255) {
				return { wch: val.toString().length * 2 + 2 }
			} else {
				return { wch: val.toString().length + 2 }
			}
		})
	)
	const result = colWidth[0]
	for (let i = 1; i < colWidth.length; i++) {
		for (let j = 0; j < colWidth[i].length; j++) {
			if (result[j].wch < colWidth[i][j].wch) {
				result[j].wch = colWidth[i][j].wch
			}
		}
	}
	ws['!cols'] = result
}
export const getCurveLineOptions = (data, time, charts_obj, add_watermark, watermark_text, startIndex, endIndex, start, end, unitText) => {
	let option = {}
	let unit
	let text
	if (data.length != 0) {
		unit = data[0].unit
	} else {
		return option
	}
	const nameArr = data.map(item => item.name)
	let datas = []
	if (unit.type == 'bool') {
		text = unit.state.sort((a, b) => b.value - a.value).map(item => item.description)
		data.forEach((item, index) => {
			datas = datas.concat(item.data.slice(startIndex, endIndex + 1).map((items, indexs) => {
				return [
					indexs, index, items
				]
			}))
		})
	}
	const timeData = time.slice(startIndex, endIndex + 1)

	option = {
		backgroundColor: add_watermark ? {
			type: 'pattern',
			image: get_canvas(watermark_text),
			repeat: 'repeat'
		} : undefined,
		tooltip: unit.type == 'float' ? {
			trigger: 'axis',
			backgroundColor: 'rgba(255,255,255,0.8)',
			formatter: function(datas) {
				let res = datas[0].name + '<br/>'
				for (let i = 0, length = datas.length; i < length; i++) {
					let format_value = datas[i].data
					if (format_value !== 0) { format_value = format_value.toFixed(2) }
					res += datas[i].marker + datas[i].seriesName.replace(/\*(.*?)\*/g, '') + ': <b>' + format_value + `</b> ${unit.description}<br/>`
				}
				return res
			},
			axisPointer: {
				type: 'cross',
				label: {
					backgroundColor: '#6a7985'
				}
			}
		} : {
			trigger: 'item',
			position: 'bottom',
			formatter: function(e) {
				const t = e.data[2] ? text[0] : text[1]
				return '<b>' + nameArr[e.data[1]].replace(/\*(.*?)\*/g, '') + '<br/>' + e.name + ': ' + e.marker + t
			}
		},
		color: ['rgb(84, 112, 198)', 'rgb(145, 204, 117)', 'rgb(250, 200, 88)', 'rgb(238, 102, 102)', 'rgb(115, 192, 222)', 'rgb(59, 162, 114)', 'rgb(252, 132, 82)', 'rgb(154, 96, 180)', 'rgb(234, 124, 204)', 'gray'],
		grid: {
			left: 60,
			right: 60,
			bottom: 65,
			top: 55
		},
		toolbox: {
			show: true,
			top: 20,
			emphasis: {
				iconStyle: {
					textPosition: 'top'
				}
			},
			feature: {
				mark: { show: true },
				saveAsImage: {
					show: true,
					title: t('保存为图片'),
					icon: 'image://' + new URL(`@/assets/download.png`, import.meta.url).href,
					name: t('功率曲线') + '_' + dayjs(new Date()).format('YYYYMMDD_HHmmss')
				},
				myReload: {
					show: true,
					title: t('刷新'),
					icon: 'image://' + new URL(`@/assets/refresh.png`, import.meta.url).href,
					onclick: () => {
						const opt = charts_obj.getOption()
						charts_obj.clear()
						charts_obj.setOption(opt)
					}
				}
			}
		},
		visualMap: unit.type == 'bool' ? {
			type: 'piecewise',
			orient: 'horizontal',
			text,
			pieces: [
				{ value: 0, color: '#DD4F42', label: '' },
				{ value: 1, color: '#1CD66C', label: '' }
			],
			left: 'center',
			top: 0
		}
			: undefined,
		xAxis: unit.type == 'float' ? {
			type: 'category',
			boundaryGap: false,
			data: timeData,
			nameLocation: 'center',
			nameTextStyle: {
				fontSize: 12,
				padding: [5, 0, 0, 0]
			}
		} : {
			type: 'category',
			data: timeData,
			nameTextStyle: { padding: [10, 10, 10, -10] },
			splitArea: {
				show: true
			}
		},
		yAxis: unit.type == 'float'
			? {
				type: 'value',
				name: unitText,
				nameLocation: 'end',
				nameTextStyle: {
					fontSize: 14,
					align: 'left'
				}
			} : {
				type: 'category',
				name: unitText,
				data: nameArr,
				splitArea: {
					show: true
				},
				splitNumber: 5,
				axisLabel: {
					overflow: 'truncate',
					width: '60',
					align: 'right'
				}
			},
		series: unit.type == 'float' ? data.map((item, index) => {
			return Object.assign({ ...item }, {
				type: 'line',
				symbol: 'none',
				index,
				emphasis: {
					focus: 'self'
				},
				data: item.data.slice(startIndex, endIndex + 1)
			})
		}) : [
			{
				name: t('机组启停状态'),
				type: 'heatmap',
				data: datas,
				label: {
					show: false
				},
				markArea: {
					animation: false,
					animationDurationUpdate: 0
				},
				emphasis: { itemStyle: { shadowBlur: 10, shadowColor: '#000' }}
			}
		],
		dataZoom: unit.type == 'float' ? [
			{
				type: 'slider',
				xAxisIndex: 0,
				filterMode: 'none',
				start: start,
				end: end
			},
			{
				type: 'inside',
				xAxisIndex: 0,
				filterMode: 'none',
				start: start,
				end: end
			}
		] : [
			{
				type: 'slider',
				start: start,
				end: end
			},
			{
				type: 'inside',
				zoomOnMouseWheel: false,
				moveOnMouseMove: true,
				moveOnMouseWheel: true,
				start: start,
				end: end
			}
		]

	}
	return option
}

export const getCurveSeries = (charts_data, lineConfig, charts_obj, time) => {
	const legendData = lineConfig.map(item => item.name)

	const timeData = generateDateTimeArray(time.length == 8760 ? 2023 : 2024, false, true)
	const option = {

		toolbox: {
			show: true,
			top: 0,
			right: 0,
			emphasis: {
				iconStyle: {
					textPosition: 'top'
				}
			},
			feature: {
				mark: { show: true },

				mySelectAll: {
					show: true,
					title: t('全部选中'),
					icon: 'image://' + new URL(`@/assets/select_all.png`, import.meta.url).href,
					onclick: () => {
						charts_obj.dispatchAction({
							type: 'legendAllSelect'
						})
					}
				},
				myClearSelect: {
					show: true,
					title: t('全部取消选中'),
					icon: 'image://' + new URL(`@/assets/clear_select.png`, import.meta.url).href,
					onclick: () => {
						const opt = charts_obj.getOption()
						const obj = {}
						legendData.forEach(item => {
							obj[item] = false
						})
						opt.legend[0].selected = obj
						charts_obj.setOption(opt)
					}
				},
				myReverseSelection: {
					show: true,
					title: t('反选'),
					icon: 'image://' + new URL(`@/assets/reverse_selection.png`, import.meta.url).href,
					onclick: () => {
						charts_obj.dispatchAction({
							type: 'legendInverseSelect'
						})
					}
				},
				saveAsImage: {
					show: true,
					title: t('保存为图片'),
					icon: 'image://' + new URL(`@/assets/download.png`, import.meta.url).href,
					name: '曲线预览_' + dayjs(new Date()).format('YYYYMMDD_HHmmss')
				},
				myReload: {
					show: true,
					title: t('刷新'),
					icon: 'image://' + new URL(`@/assets/refresh.png`, import.meta.url).href,
					onclick: () => {
						const opt = charts_obj.getOption()
						charts_obj.clear()
						charts_obj.setOption(opt)
					}
				}
			}
		},
		legend: {
			width: '85%',
			height: '100%',
			inactiveColor: '#837F7B',
			data: legendData,
			top: 8
		},
		grid: {
			left: 60,
			right: 60,
			bottom: 65,
			top: 58
		},
		xAxis: {
			type: 'category',

			data: time || timeData
		},
		yAxis: {
			type: 'value',
			name: 'μ',
			nameTextStyle: {
				fontSize: 16
			}
		},
		dataZoom: {
			height: 25,
			start: 0,
			end: 100,

			bottom: 10
		},
		series: lineConfig.map(item => {
			return Object.assign({ ...item }, {
				emphasis: {
					disabled: true
				},
				symbol: 'circle',
				type: 'line',

				step: 'middle',
				showSymbol: false,

				data: charts_data[item.lineName],
				stack: item.name,
				lineStyle: {
					width: 1
				}
			})
		})
	}
	return option
}

export const getConfidenceSeries = (data) => {
	const { bin_edges_arr, cdf_arr } = data
	const option = {
		tooltip: {
			trigger: 'axis'
		},
		grid: {
			left: 50,
			right: 50,
			bottom: 65,
			top: 50
		},
		xAxis: bin_edges_arr.map(item => {
			return {
				type: 'category',
				data: item
			}
		}),
		yAxis: {
			type: 'value',

			nameTextStyle: {
				fontSize: 16
			}
		},
		dataZoom: {
			height: 25,
			start: 0,
			end: 100,

			bottom: 10
		},
		series: cdf_arr.map(item => {
			return Object.assign({ ...item }, {
				emphasis: {
					disabled: true
				},
				symbol: 'none',
				type: 'line',
				smooth: true,

				showSymbol: false,

				data: item,

				lineStyle: {
					width: 1
				}
			})
		})

	}
	return option
}

export const getStatisticSeries = (statisticDta, columns) => {
	const xArr = statisticDta.year
	const tempArr = Object.keys(statisticDta).filter(item => item !== 'year')

	const columnsObj = columns.reduce((acc, cur) => {
		acc[cur.field] = cur.headerName
		return acc
	}, {})

	const option = {
		tooltip: {
			trigger: 'axis'
		},
		grid: {
			left: 50,
			right: 50,
			bottom: 65,
			top: 50
		},
		xAxis: {
			type: 'category',
			data: xArr
		},
		yAxis: {
			type: 'value',

			nameTextStyle: {
				fontSize: 16
			}
		},
		dataZoom: {
			height: 25,
			start: 0,
			end: 100,

			bottom: 10
		},
		legend: {

			orient: 'horizontal',
			right: '50px',
			top: 8,

			data: columns.map(item => item.headerName)
		},
		series: tempArr.map(item => {
			return Object.assign(
				{
					'name': columnsObj[item]
				},
				{
					emphasis: {
						disabled: true
					},
					symbol: 'none',
					type: 'line',
					smooth: true,

					showSymbol: false,

					data: statisticDta[item],

					lineStyle: {
						width: 1
					}
				})
		})
	}
	return option
}

export const getExampleSeries = (charts_data, allConfig, selectedRowKeys, add_watermark, watermark_text, startIndex, start, end) => {
	const selectedConfig = allConfig.filter(item => selectedRowKeys.includes(item.lineName))

	const legendColor = selectedConfig.map(item => item.color)

	const timeData = charts_data['Time']
	const option = {
		backgroundColor: add_watermark ? {
			type: 'pattern',
			image: get_canvas(watermark_text),
			repeat: 'repeat'
		} : undefined,
		color: legendColor,
		tooltip: {
			trigger: 'axis',
			backgroundColor: 'rgba(255,255,255,0.8)',
			formatter: function(datas) {
				let res = (datas[0].name + ' (' + t('第') + (1 + startIndex + datas[0].dataIndex) + t('小时') + ')' + '<br/>')
				for (let i = 0, length = datas.length; i < length; i++) {
					let format_value = datas[i].data
					const tempMarker = selectedConfig.find(item => item.name == datas[i].seriesName).type !== 'area' ? `<span style="display:inline-block;margin-right:4px;width:10px;height:2px;margin-bottom: 4px;background-color:${datas[i].color};"></span>` : datas[i].marker
					if (format_value !== 0) { format_value = format_value.toFixed(2) }
					res += tempMarker + datas[i].seriesName + ': ' + format_value + '<br/>'
				}
				return res
			},
			axisPointer: {
				type: 'cross',
				label: {
					backgroundColor: '#6a7985'
				}
			}
		},

		legend: {
			width: '85%',
			height: '100%',
			inactiveColor: '#837F7B',

			itemGap: 8,
			bottom: 30,
			top: 3
		},
		grid: {
			left: 60,
			right: 60,
			bottom: 65,
			top: 68
		},
		xAxis: {
			type: 'category',
			data: timeData,
			name: t('时间'),
			nameTextStyle: {
				fontSize: 16,
				padding: [0, 0, 0, -5]
			},
			axisLabel: {
				fontSize: 12
			}
		},
		yAxis: {
			type: 'value',
			name: t(`功率(万千瓦)`),
			nameTextStyle: {
				fontSize: 16,
				padding: 10
			},
			axisLabel: {
				fontSize: 12
			}
		},

		dataZoom: {
			height: 25,
			start,
			end: end || (24 * 7 * 100) / timeData.length
		},
		series: selectedConfig.map(item => {
			return Object.assign({ ...item }, {
				emphasis: {
					disabled: true
				},
				symbol: 'circle',
				type: 'line',

				step: 'middle',
				showSymbol: false,
				color: item.color,
				data: charts_data[item.lineName] ? charts_data[item.lineName] : undefined,
				stack: item.type == 'area' ? '总量' : item.name
			}, item.type == 'area' ? {
				areaStyle: {
					opacity: 1
				},
				lineStyle: {
					width: 0
				}
			} : {
				lineStyle: {

					type: item.type.split('_')[0]
				}
			})
		}).filter(item => item.data).sort((a, b) => a.sequence - b.sequence)
	}
	return option
}
