import { Modal } from 'ant-design-vue'
import { WarningTwoTone } from '@ant-design/icons-vue'
import { createVNode } from 'vue'
import { utils, writeFile } from 'xlsx'
import { t } from '@/utils/common'
export function getTextWidth(text, fontStyle) {
	const span = document.createElement('span')
	span.style.visibility = 'hidden'
	span.style.position = 'absolute'
	span.style.whiteSpace = 'nowrap'
	span.style.font = fontStyle || '14px Arial'
	span.textContent = text
	document.body.appendChild(span)
	const width = span.offsetWidth
	document.body.removeChild(span)
	return width
}

export function checkFileExtension(filename, ext, caseSensitive = false) {
	const lastDotIndex = filename.lastIndexOf('.')
	if (lastDotIndex === -1) return false
	const fileExt = filename.slice(lastDotIndex + 1)
	const compareExt = caseSensitive ? fileExt : fileExt.toLowerCase()
	const targetExts = Array.isArray(ext)
		? (caseSensitive ? ext : ext.map(e => e.toLowerCase()))
		: [caseSensitive ? ext : ext.toLowerCase()]
	return targetExts.includes(compareExt)
}
export function debounce(func, delay) {
	let timer
	return function(...args) {
		const context = this
		clearTimeout(timer)
		timer = setTimeout(() => {
			func.apply(context, args)
		}, delay)
	}
}

export function throttle(fn, delay, leading = true) {
	let timer
	return function(...args) {
		if (!timer) {
			if (leading) {
				fn.apply(this, args)
			}
			timer = setTimeout(() => {
				if (!leading) {
					fn.apply(this, args)
				}
				timer = null
			}, delay)
		}
	}
}
export const exportExcel = (data = [], titleName, sheetName, fileName) => {
	const tableData = data.map(item => Object.values(item))
	const title = data.length > 0 ? Object.keys(data[0]).map(item => titleName[item]) : Object.values(titleName)
	const wb = utils.book_new()
	tableData.unshift(title)
	const ws = utils.aoa_to_sheet(tableData)
	autoWidthFun(ws, tableData)
	utils.book_append_sheet(wb, ws, sheetName)
	writeFile(wb, fileName)
}
export const exportExcels = (data, fileName) => {
	const wb = utils.book_new()
	data.forEach(item => {
		const tableData = item.data
		tableData.unshift(item.title)
		const ws = utils.aoa_to_sheet(tableData)
		autoWidthFun(ws, tableData)
		utils.book_append_sheet(wb, ws, item.sheetName)
	})
	writeFile(wb, fileName)
}
function autoWidthFun(ws, data) {
	const colWidth = data.map(row =>
		row.map(val => {
			if (val == null || val == undefined || val == '') {
				return { wch: 10 }
			} else if (val.toString().charCodeAt(0) >= 0x4E00 && val.toString().charCodeAt(0) <= 0x9FA5) {
				return { wch: val.toString().length * 2 + 2 }
			} else {
				return { wch: val.toString().length + 2 }
			}
		})
	)
	const result = colWidth[0]
	for (let i = 1; i < colWidth.length; i++) {
		for (let j = 0; j < colWidth[i].length; j++) {
			if (result[j].wch < colWidth[i][j].wch) {
				result[j].wch = colWidth[i][j].wch
			}
		}
	}
	ws['!cols'] = result
}
function adjustLongitude(max_lon, min_lon, max_lat, min_lat) {
	const currentRatio = (max_lon - min_lon) / (max_lat - min_lat)

	if (currentRatio <= 1.25) {
		const targetRatio = 1.25
		const required_lon_diff = (max_lat - min_lat) * targetRatio

		const current_lon_diff = max_lon - min_lon

		const adjustment = (required_lon_diff - current_lon_diff) / 2

		max_lon += adjustment
		min_lon -= adjustment
		if (max_lon > 180) max_lon = 180
		if (min_lon < -180) min_lon = -180

		return { max_lon, min_lon, max_lat, min_lat }
	}

	return { max_lon, min_lon, max_lat, min_lat }
}
export const getGeoJson = (data) => {
	const max_lon1 = data.max_lon + 2 > 180 ? 180 : data.max_lon + 2
	const min_lon1 = data.min_lon - 2 < -180 ? -180 : data.min_lon - 2
	const max_lat1 = data.max_lat + 1 > 90 ? 90 : data.max_lat + 1
	const min_lat1 = data.min_lat - 1 < -90 ? -90 : data.min_lat - 1
	const { max_lon, min_lon, max_lat, min_lat } = adjustLongitude(max_lon1, min_lon1, max_lat1, min_lat1)
	return {
		'type': 'FeatureCollection',
		'features': [
			{
				'type': 'Feature',
				'properties': {
					'name': '',
					'center': [
						0, 0
					],
					'centroid': [
						0, 0
					]
				},
				'bbox': [min_lon, min_lat, max_lon, max_lat],
				'geometry': {
					'type': 'Polygon',
					'coordinates': [
						[
							[
								max_lon, min_lat
							],
							[
								max_lon, max_lat
							],
							[
								min_lon, max_lat
							],
							[
								min_lon, min_lat
							]
						]
					]
				}
			}
		]
	}
}
export const checkGeoJson = (geoJson) => {
	if (typeof geoJson !== 'object' || Array.isArray(geoJson)) {
		throw new Error('当前JSON文件不是地理json')
	}

	const type = geoJson.type
	if (!type || typeof type !== 'string') {
		throw new Error('当前JSON文件不是地理json')
	}

	const validTypes = new Set(['Point', 'LineString', 'Polygon',
		'MultiPoint', 'MultiLineString', 'MultiPolygon',
		'Feature', 'FeatureCollection'])

	if (!validTypes.has(type)) {
		throw new Error('当前JSON文件不是地理json')
	}
	return true
}
export const pointOnLine = (start, end, percent) => {
	const dx = end[0] - start[0]
	const dy = end[1] - start[1]
	const distance = Math.sqrt(dx * dx + dy * dy)
	const targetDistance = distance * percent
	const targetX = start[0] + (dx / distance) * targetDistance
	const targetY = start[1] + (dy / distance) * targetDistance
	return [targetX, targetY]
}
export const getRotate = (start, end, aspectScale = 0.75) => {
	const angle = Math.atan2((end[1] - start[1]) / aspectScale, (end[0] - start[0])) * (180 / Math.PI)
	return angle
}
export function generateHourlyTimestamps(startTime, endTime) {
	const start = new Date(startTime)
	const end = new Date(endTime)
	const timestamps = []

	let current = new Date(start)
	while (current <= end) {
		const formattedTime = current.toISOString().slice(0, 19).replace('T', ' ')
		timestamps.push(formattedTime)

		current = new Date(current.setHours(current.getHours() + 1))
	}
	return timestamps
}
export const openModal = (title, content) => {
	return new Promise((resolve, reject) => {
		Modal.confirm({
			title,
			icon: createVNode(WarningTwoTone),
			centered: true,
			content: createVNode('div', {

			}, content),
			onOk() {
				resolve(true)
			},
			onCancel() {
				reject('reject')
			},
			class: 'gis-delete-modal'
		})
	})
}
export const fixInteger = (data, unit = 2) => {
	return data ? Number.isInteger(data) ? data : data.toFixed(unit) : 0
}
export function parseFilePath(path) {
	const str = path.includes('/') ? '/' : '\\'
	const lastSlashIndex = path.lastIndexOf(str)
	const file_name = path.substring(lastSlashIndex + 1)
	const folder_name = path.substring(0, lastSlashIndex + 1)
	return { file_name, folder_name }
}
export const getUnit = (val) => {
	if (val.includes(t('功率'))) {
		return 'MW'
	} else if (val.match(/\((.*?)\)/g)) {
		return val.match(/\((.*?)\)/g)[0]
	} else if (val.match(/（(.+?)）/)) {
		return val.match(/（(.+?)）/)[0]
	}
}
export const getGisColor = (value, range = [0.3, 0.5, 0.8, 1], color = ['#11cbd7', '#55e9bc', '#fff5a5', '#ffaa64', '#ff6464']) => {
	if (value < range[0]) {
		return color[0]
	}
	for (let index = range.length - 1; index >= 0; index--) {
		const element = range[index]
		if (value > element) {
			return color[index + 1]
		}
	}
}
export const getGisStyle = ({ vn_kv, line_types = 'solid', color }, zoom = 1) => {
	let width
	if (vn_kv >= 1000) {
		color = color || '#000000'
		width = 5
	} else if (vn_kv >= 500) {
		color = color || '#ff0000'
		width = 3
	} else if (vn_kv >= 200) {
		color = color || '#000000'
		width = 2
	} else {
		width = 1
		if (vn_kv >= 110) {
			color = color || '#0000ff'
		} else if (vn_kv >= 35) {
			color = color || '#00ff00'
		} else {
			color = color || '#408080'
		}
	}
	return {
		color,
		width: echartsResize(width * zoom),
		type: line_types
	}
}
export const stationList = {
	'pump': '抽蓄',
	'storage': '储能',
	'wind': '风电',
	'solar': '光伏',
	'nuclear': '核电',
	'converter_station': '换流站',
	'switching_station': '开关站',
	'coal': '煤电',
	'gas': '气电',
	'hydropower': '水电',
	'station': '变电站',
	'feedin': '电力流'
}
export const menuList = [
	{
		name: '变电站',
		type: 0,
		path: ` 
			M 0,25                        
			A 25,25 0 1,1 50,25             
			A 25,25 0 1,1 0,25  
			M 5,25                        
			A 20,20 0 1,1 45,25              
			A 20,20 0 1,1 5,25
			M 10,25                         
			A 15,15 0 1,1 40,25              
			A 15,15 0 1,1 10,25
		`
	},
	{
		name: '电力流',
		type: 7,
		path: ` 
			M 0,25                        
			A 25,25 0 1,1 50,25             
			A 25,25 0 1,1 0,25
			Z
			M 0,25
			L 25,25
			Z
			M 25,0
			L 25,50
			Z
		`
	},
	{
		name: '火电',
		type: 1,
		path: ` 
			M 0, 0
			L 0,50
			L 50,50
			L 50,0
			Z
			M 0,25
			L 50,25
		`
	},
	{
		name: '水电',
		type: 2,
		path: `
			M 0, 0
			L 0,50
			L 50,50
			L 50,0
			Z
			M 0,50
			L 50,0
		`
	},
	{
		name: '核电',
		type: 3,
		path: `
			M 0, 0
			L 0,50
			L 50,50
			L 50,0
			Z
			M 12.5,25                   
			A 12.5,12.5 0 1,1 37.5,25              
			A 12.5,12.5 0 1,1 12.5,25
			M 12.5,25                   
			A 12.5,12.5 0 1,1 37.5,25              
			A 12.5,12.5 0 1,1 12.5,25
		`
	},
	{
		name: '风电',
		type: 4,
		path: `
			M 0, 0
			L 0,50
			L 50,50
			L 50,0
			Z
			M 0,25
			H 37.5
			M 25,0
			L 50,50
			M 50,0
			L 25,50
		`
	},
	{
		name: '光伏',
		type: 5,
		path: `
			M 0, 0
			L 0,50
			L 50,50
			L 50,0
			Z
			M 0,25
			H 50
			M 0,0
			L 25,25
			M 25,0
			L 50,25
		`
	},
	{
		name: '储能',
		type: 6,
		path: `
			M 0, 0
			L 0,50
			L 50,50
			L 50,0
			Z
			M 0,16.5
			L 25,16.5
			M 25,16.5
			L 25,50
			M 25,33.5
			L 50,33.5
			M 25,33.5
			L 37.5,50
			M 37.5,33.5
			L 50,50
		`
	}

]
export const getSymbolPath = ({ station_type, vn_kv, expandable, type }) => {
	let path
	if (type == 'switch') {
		path = `
			M 40,0
			L 40,100
			L 60,100
			L 60,0
			Z
		`
	} else if (expandable) {
		path = `
			M 0,50                         
			A 50,50 0 1,1 100,50              
			A 50,50 0 1,1 0,50   
			M 25,25
			L 75,75
			M 75,25
			L 25,75
			`
	} else if (station_type == 'station') {
		if (vn_kv > 330) {
			path = ` 
			M 0,50                         
			A 50,50 0 1,1 100,50              
			A 50,50 0 1,1 0,50   
			M 10,50                         
			A 40,40 0 1,1 90,50              
			A 40,40 0 1,1 10,50
			M 20,50                         
			A 30,30 0 1,1 80,50              
			A 30,30 0 1,1 20,50
			`
		} else {
			path = `
			M 0,50                         
			A 50,50 0 1,1 100,50              
			A 50,50 0 1,1 0,50   
			M 15,50                         
			A 35,35 0 1,1 85,50              
			A 35,35 0 1,1 15,50    
			`
		}
	} else if (station_type == 'pump') {
		path = `
			M 0, 0
			L 0,100
			L 100,100
			L 100,0
			Z
			M 0,33
			L 50,100
			M 0,33
			L 50,33
			M 50,33
			L 50,100
			M 50,67
			L 100,67
			`
	} else if (station_type == 'storage') {
		path = `
			M 0, 0
			L 0,100
			L 100,100
			L 100,0
			Z
			M 0,33
			L 50,33
			M 50,33
			L 50,100
			M 50,67
			L 100,67
			M 50,67
			L 75,100
			M 75,67
			L 100,100
		`
	} else if (station_type == 'wind') {
		path = `
			M 0, 0
			L 0,100
			L 100,100
			L 100,0
			Z
			M 0,50
			H 75
			M 50,0
			L 100,100
			M 100,0
			L 50,100
		`
	} else if (station_type == 'solar') {
		path = `
			M 0, 0
			L 0,100
			L 100,100
			L 100,0
			Z
			M 0,50
			H 100
			M 0,0
			L 50,50
			M 50,0
			L 100,50
		`
	} else if (station_type == 'nuclear') {
		path = `
			M 0, 0
			L 0,100
			L 100,100
			L 100,0
			Z
			M 25,50                   
			A 25,25 0 1,1 75,50              
			A 25,25 0 1,1 25,50
			M 25,50                   
			A 25,25 0 1,1 75,50              
			A 25,25 0 1,1 25,50
		`
	} else if (station_type == 'converter_station') {
		path = `
			M 0,50                         
			A 50,50 0 1,1 100,50              
			A 50,50 0 1,1 0,50   
			M 14.644660940672622,85.35533905932738
			L 85.35533905932738,14.644660940672622
			M 20,30
			L 45,30
			M 55,60                         
			A 10,10 0 1,1 75,60   
			M 75,60
			A 10,10 0 1,0 95,60   
			M 75,60
			A 10,10 0 1,0 95,60   
		`
	} else if (station_type == 'switching_station') {
		path = `
			M 0,50                         
			A 50,50 0 1,1 100,50              
			A 50,50 0 1,1 0,50   
			M 14.644660940672622,85.35533905932738
			L 85.35533905932738,14.644660940672622
		`
	} else if (station_type == 'coal') {
		path = `
			M 0, 0
			L 0,200
			L 200,200
			L 200,0
			Z
			M 0,100
			H 200
			M 55,25
			L 85,25
			M 65,17
			L 65,40
			L 75,40
			L 75,17
			M 65,32
			L 75,32
			M 55,45
			L 85,45
			M 70,40
			L 70,60
			M 70,45
			Q 72,52 85,55
			M 70,45
			Q 68,52 55,55
			M 47,17
			Q 49,55 40,60
			M 46,47
			L 50,55
			M 53,27
			L 50,35
			M 45,27
			Q 45,35 42,37
		`
	} else if (station_type == 'gas') {
		path = `
			 M 0, 0
			L 0,200
			L 200,200
			L 200,0
			Z
			M 0,100
			H 200
			M 30,17
			Q 27,26 20,27
			M 28,22
			L 57,22
			M 28,27
			L 52,27
			M 25,32
			L 48,32
			M 48,32
			Q 53,60 57,45
		`
	} else if (station_type == 'hydropower') {
		path = `
			M 0, 0
			L 0,100
			L 100,100
			L 100,0
			Z
			M 0,100
			L 100,0
		`
	} else if (station_type == 'feedin') {
		path = ` 
		M 0,25                        
		A 25,25 0 1,1 50,25             
		A 25,25 0 1,1 0,25
		Z
		M 0,25
		L 25,25
		Z
		M 25,0
		L 25,50
		Z
	`
	} else {
		path = ` 
			M 0,50                         
			A 50,50 0 1,1 100,50              
			A 50,50 0 1,1 0,50   
		`
	}
	return path
}
export const getMapServies = ({ gisState: { iconZoom, partition, isBlank = false, showStationName = true }, mapData: { point_data, line_data }, resultType, markLineData = [], showMore = false, resultColor = ['#ff6464', '#ffaa64', '#fff5a5', '#55e9bc', '#11cbd7'] }) => {
	const option = {
		geo: Object.assign({
			map: partition,
			zoom: 5,
			roam: true,
			top: 0,
			bottom: 0,
			show: false
		}, {}),
		animation: false,
		tooltip: {
			formatter: (params) => {
				if (params.componentSubType == 'lines' && params.data.type == 'switch') {
					return params.name
				} else if ((resultType == 'n_1' || resultType == 'n_2') && params.componentSubType == 'lines' && params.data.load_ratio_max && params.data.load_ratio_count) {
					return `
					<div class="gis-result-tooltip-one"> 
						<p>${params.name}</p>
						<div>
							<p>${t('最大负载率')}：<span>${fixInteger(params.data.load_ratio_max, 2)}%</span></p>
							<p>${t('过载次数')}：<span>${params.data.load_ratio_count}${t('次')}</span></p>
						</div>
					</div>`
				} else if (resultType == 'short_circuit' && params.componentSubType == 'scatter' && params.data.gisResult.length > 0) {
					const str = params.data.gisResult.reduce((a, b) => {
						return a + `<div><p>${b.name}</p><p>${b.vn_kv}kV</p><p>${b.ik_ka}kA</p><p>${b.imax_ka}kA</p><p>${b.zone}</p><p>${b.type == '1-PH' ? t('单相短路') : t('三相短路')}</p></div>`
					}, '')
					return `
					<div class="gis-result-tooltip-two"> 
						<p>${params.data.station_name}</p>
						<div>
							<p>${t('母线名')}</p>
							<p>${t('电压等级')}</p>
							<p>${t('电流')}</p>
							<p>Imax</p>
							<p>${t('分区')}</p>
							<p>${t('故障类型')}</p>
						</div>
						${str}
					</div>`
				} else if (resultType == 'ac_power_flow') {
					if (params.componentSubType == 'scatter' && params.componentIndex == 2) {
						return `
						<div class="gis-result-tooltip-three"> 
							<p>${params.data.station_name}</p>
							<div>
								<p>${t('负荷有功功率')}<span>${fixInteger(params.data.res_load_sum_p_mw, 2)}MW</span></p>
								<p>${t('负荷无功功率')}<span>${fixInteger(params.data.res_load_sum_q_mvar, 2)}MVAR</span></p>
								<p>${t('机组有功功率')}<span>${fixInteger(params.data.res_gen_sum_p_mw, 2)}MW</span></p>
								<p>${t('机组无功功率')}<span>${fixInteger(params.data.res_gen_sum_q_mvar, 2)}MVAR</span></p>
								<p>${t('高压侧有功功率')}<span>${fixInteger(params.data.res_trafo_sum_p_hv_mw, 2)}MW</span></p>
								${params.data.station_type == 'feedin' ? `<p>${t('区外来电')}<span>${-1 * fixInteger(params.data.res_feedin_sum_p_mw, 2)}MW</span></p>` : ''}
								<p>${t('负载率')}{<span>${fixInteger(params.data.res_trafo_max_loading_percent, 2)}%</span></p>
								<p>${t('电压')}<span>${fixInteger(params.data.vn_kv, 2)}kV</span></p>
							</div>
						</div>`
					} else if (params.componentSubType == 'lines' && params.componentIndex == 1) {
						return `
						<div class="gis-result-tooltip-three"> 
							<p>${params.name}</p>
							<div>
								<p>${t('负载率')}<span>${fixInteger(params.data.loading_percent, 2)}%</span></p>
								<p>${t('有功功率')}<span>${fixInteger(params.data.p_from_mw, 2)}MW</span></p>
								<p>${t('无功功率')}<span>${fixInteger(params.data.q_from_mvar, 2)}MVAR</span></p>
							</div>
						</div>`
					}
				} else if (params.componentSubType == 'lines') {
					return params.name
				} else if (params.componentSubType == 'scatter' && (resultType)) {
					return params.data.station_name
				} else if (params.componentSubType == 'scatter' && params.componentIndex == 6) {
					return params.data.name
				}
			}
		},
		// ['ac_power_flow', 'n_1', 'n_2'].includes(resultType)
		visualMap: resultType ? [{
			seriesIndex: resultType == 'short_circuit' ? [2] : [1, 3],
			realtime: false,
			type: 'piecewise',
			align: 'left',
			show: true,
			selectedMode: false,
			left: echartsResize(0),
			bottom: echartsResize(35),
			itemGap: echartsResize(5),
			itemHeight: echartsResize(14),
			itemWidth: echartsResize(20),
			textGap: echartsResize(10),
			textStyle: {
				color: '#000',
				fontSize: echartsResize(10)
			},
			pieces: resultType == 'n_1' || resultType == 'n_2' ? [
				{ gt: 100, label: '100' + t('次') + t('以上'), color: resultColor[0] },
				{ gt: 50, lte: 100, label: '50-100' + t('次'), color: resultColor[1] },
				{ gt: 10, lte: 50, label: '10-50' + t('次'), color: resultColor[2] },
				{ gt: 0, lte: 10, label: '0-10' + t('次'), color: resultColor[3] },
				{ lte: 0, label: '0次', color: resultColor[4] }
			] : resultType == 'ac_power_flow' ? [
				{ gt: 1, label: '100%' + t('以上'), color: resultColor[0] },
				{ gt: 0.8, lte: 1, label: '80-100%', color: resultColor[1] },
				{ gt: 0.5, lte: 0.8, label: '50-80%', color: resultColor[2] },
				{ gt: 0.3, lte: 0.5, label: '30-50%', color: resultColor[3] },
				{ lte: 0.3, label: '0-30%', color: resultColor[4] }
			] : [
				{ gt: 15, label: '15' + t('以上'), color: resultColor[0] },
				{ gt: 10, lte: 15, label: '10-15', color: resultColor[1] },
				{ gt: 5, lte: 10, label: '5-10', color: resultColor[2] },
				{ gt: 0, lte: 5, label: '0-5', color: resultColor[3] },
				{ lte: 0, label: ' ', color: resultColor[4] }
			],
			outOfRange: {
				// color: '#000'
			}
		}] : [],
		series: [
			// 地图 0
			Object.assign({
				type: 'map',
				zlevel: 1,
				map: partition,
				zoom: 5,
				roam: 'move',
				label: {
					show: false
				},
				top: 0,
				bottom: 0,
				selectedMode: false,
				itemStyle: !isBlank ? {
					borderColor: 'rgba(0,0,0,0.2)',
					borderWidth: 1,
					areaColor: 'rgba(44,82,118,0)'
				} : {
					borderColor: 'transparent',
					borderWidth: 0,
					areaColor: 'transparent'
				},
				data: [],
				emphasis: {
					disabled: true
				}
			}, isBlank ? {
				left: 0,
				right: 0
			} : {

			}),
			// 线路 1
			{
				type: 'lines',
				zlevel: 2,
				animation: false,
				emphasis: {
					disabled: true
				},
				lineStyle: {
					opacity: 1
				},
				label: {
					show: true,
					position: 'middle',
					formatter: params => {
						if (resultType == 'n_1' || resultType == 'n_2') {
							return params.data.load_ratio_max ? fixInteger(params.data.load_ratio_max, 2) + '%' : ''
						} else {
							return ''
						}
					},
					rich: {
						load: {

						},
						power: {

						},
						vnkV: {

						}
					},
					fontSize: 12,
					color: '#000'
				},
				data: line_data
			},
			// 场站 2
			{
				animation: false,
				zlevel: 3,
				name: '起始涟漪城市',
				type: 'scatter',
				coordinateSystem: 'geo',
				symbolSize: (value, params) => {
					return getGisStyle(params.data, iconZoom).width * 2 * option.geo.zoom
				},
				symbol: (value, params) => {
					return 'path://' + getSymbolPath(params.data)
				},
				itemStyle: {
					opacity: 1,
					color: '#fff',
					borderWidth: 1
				},
				emphasis: {
					disabled: true
				},
				labelLayout: {
					hideOverlap: true
				},
				label: {
					show: showStationName,
					formatter: params => {
						if (resultType) {
							if (resultType === 'ac_power_flow') {
								const loadValue = params.data.res_load_sum_p_mw
								return params.data.station_name + '\n' +
									(!showMore ? (`{load|L:${fixInteger(loadValue)}}\n{power|G:${fixInteger(params.data.res_gen_sum_p_mw)}}\n{vnkV|V:${fixInteger(params.data.vn_kv)}kV}`)
										: (`{load|L:${fixInteger(loadValue)}+j${fixInteger(params.data.res_load_sum_q_mvar)}}\n{power|G:${fixInteger(params.data.res_gen_sum_p_mw)}+j${fixInteger(params.data.res_gen_sum_q_mvar)}}\n{vnkV|V:${fixInteger(params.data.vn_kv)}kV}`))
							} else if (resultType === 'n_1' || resultType === 'n_2') {
								return params.data.station_name
							} else if (resultType === 'short_circuit') {
								return params.data.station_name
							}
						} else {
							return params.data.station_name
						}
					},
					rich: {
						load: {

						},
						power: {

						},
						vnkV: {

						}
					},
					fontSize: 12,
					position: 'bottom',
					color: '#000'
				},
				data: resultType === 'short_circuit' ? point_data.map(item => {
					return Object.assign({}, item, {
						value: [item.value[0], item.value[1], showMore ? item.ik_sub_imax_3_ph || 0 : item.ik_sub_imax_1_ph || 0]
					})
				}) : point_data
			},

			{
				animation: false,
				zlevel: 1,
				name: '起始涟漪城市',
				type: 'scatter',
				coordinateSystem: 'geo',
				symbol: 'triangle',
				symbolSize: (value, params) => {
					return 1.5 * option.geo.zoom * iconZoom
				},
				itemStyle: {
					opacity: 1
				},
				labelLayout: {
					hideOverlap: true
				},
				emphasis: {
					disabled: true
				},
				data: resultType == 'ac_power_flow' ? line_data.filter(items => items.type != 'switch').map(item => {
					return {
						name: item.name,
						value: pointOnLine(item.coords[0], item.coords[1], 0.5).concat(item.value),
						symbolRotate: ((item.isSequence && item.p_from_mw >= 0) || (!item.isSequence && item.p_from_mw < 0) ? (getRotate(item.coords[0], item.coords[1]) - 90) : (getRotate(item.coords[0], item.coords[1]) + 90))
					}
				}) : []
			},

			{
				type: 'lines',
				zlevel: 3,
				animation: false,
				label: {
					show: true,
					position: 'middle',
					color: '#000',
					lineHeight: 0,
					formatter: params => {
						if (params.value) {
							if (showMore) {
								return 'P:' + Math.abs(fixInteger(params.value)) + '+j' + Math.abs(fixInteger(params.data.values))
							} else {
								return Math.abs(fixInteger(params.value))
							}
						} else {
							return ''
						}
					}
				},
				emphasis: {
					disabled: true
				},
				lineStyle: {
					opacity: 1,
					width: 0
				},
				data: resultType === 'ac_power_flow' ? markLineData : []
			},

			{
				animation: false,
				zlevel: 2,
				name: '起始涟漪城市',
				type: 'scatter',
				coordinateSystem: 'geo',
				symbolSize: (value, params) => {
					return getGisStyle(params.data, iconZoom).width * 2 * option.geo.zoom
				},
				symbol: (value, params) => {
					return 'path://' + getSymbolPath(params.data)
				},
				itemStyle: {
					opacity: 1,
					color: 'transparent',
					borderColor: 'transparent',
					borderWidth: 1
				},
				emphasis: {
					disabled: true
				},
				label: {
					show: true,
					formatter: params => {
						return params.data.trafo_id_list.length == 0 ? '' : (Math.abs(fixInteger(params.data.res_trafo_sum_p_hv_mw))) + (params.data.res_trafo_sum_p_hv_mw > 0 ? ' ↓' : ' ↑')
					},
					fontSize: 12,
					position: 'right',
					color: '#000'
				},
				data: resultType === 'ac_power_flow' ? point_data : []
			},

			{
				animation: false,
				zlevel: 2.5,
				name: '起始涟漪城市',
				type: 'scatter',
				coordinateSystem: 'geo',
				symbolSize: (value, params) => {
					return [getGisStyle(params.data, iconZoom).width * 2 * option.geo.zoom * 2 * 0.4, getGisStyle(params.data, iconZoom).width * 2 * option.geo.zoom * 0.4]
				},
				symbol: (value, params) => {
					return 'path://' + getSymbolPath(params.data)
				},
				emphasis: {
					disabled: true
				},
				itemStyle: {
					opacity: 1,
					borderWidth: 1,
					borderColor: '#000'
				},
				label: {
					show: false
				},
				data: line_data.filter(item => item.type == 'switch').map(item1 => {
					return Object.assign({ ...item1 }, {
						symbolRotate: getRotate(item1.coords[0], item1.coords[1]),
						value: centerOnLine(item1.coords[0], item1.coords[1]),
						itemStyle: {
							color: item1.closed ? '#000' : '#fff'
						}
					})
				})
			}
		]
	}
	return option
}
export const echartsResize = (val, initWidth = 1920) => {
	return val * (document.documentElement.clientWidth / initWidth)
}
export const getTangentialPoint = (startX, startY, r1, endX, endY, r2) => {
	const dx = endX - startX
	const dy = endY - startY
	const d = Math.sqrt(dx * dx + dy * dy)
	const theta = Math.atan2(dy, dx)
	const alpha = Math.acos((r1 - r2) / d)
	const dir1 = {
		x: Math.cos(theta + alpha),
		y: Math.sin(theta + alpha)
	}
	const dir2 = {
		x: Math.cos(theta - alpha),
		y: Math.sin(theta - alpha)
	}
	const p1 = [startX + r1 * dir1.x / 0.75, startY + r1 * dir1.y]
	const p2 = [endX + r2 * dir1.x / 0.75, endY + r2 * dir1.y]
	const p3 = [startX + r1 * dir2.x / 0.75, startY + r1 * dir2.y]
	const p4 = [endX + r2 * dir2.x / 0.75, endY + r2 * dir2.y]
	return [[p1, p2], [p3, p4]]
}
export function deepClone(object) {
	let result
	if (Object.prototype.toString.call(object) == '[object Object]') {
		result = {}
	} else if (Object.prototype.toString.call(object) == '[object Array]') {
		result = []
	} else {
		return ''
	}

	for (const key in object) {
		if (typeof object[key] == 'object') {
			result[key] = deepClone(object[key])
		} else {
			result[key] = object[key]
		}
	}
	return result
}
export const getMapScaleByBbox = (
	geoData, width = 500, height = 500, aspectScale = 0.75
) => {
	let bbox
	if (geoData.features.every(item => item.bbox)) {
		geoData.features.forEach((item, index) => {
			if (index == 0) {
				bbox = item.bbox
			} else {
				if (bbox[0] > item.bbox[0]) {
					bbox[0] = item.bbox[0]
				}
				if (bbox[1] > item.bbox[1]) {
					bbox[1] = item.bbox[1]
				}
				if (bbox[2] < item.bbox[2]) {
					bbox[2] = item.bbox[2]
				}
				if (bbox[3] < item.bbox[3]) {
					bbox[3] = item.bbox[3]
				}
			}
		})
	} else {
		let N = -90; let S = 90; let W = 180; let E = -180
		geoData.features.forEach(item => {
			item.geometry.coordinates.forEach(area => {
				(item.geometry.type === 'Polygon' ? area : area[0]).forEach(elem => {
					if (elem[0] < W) {
						W = elem[0]
					}
					if (elem[0] > E) {
						E = elem[0]
					}
					if (elem[1] > N) {
						N = elem[1]
					}
					if (elem[1] < S) {
						S = elem[1]
					}
				})
			})
		})
		bbox = [W, S, E, N]
	}
	return {
		scale: Math.min(width / (bbox[2] - bbox[0]), height / (bbox[3] - bbox[1]) * aspectScale),
		center: [
			(bbox[2] + bbox[0]) / 2,
			(bbox[3] + bbox[1]) / 2
		]
	}
}
export const initLineData = ({ type, channel, apiZoom, geoScale, resultType }) => {
	let lineList = []
	const symbolR = getGisStyle({ vn_kv: channel.min_vn_kv }, apiZoom).width * echartsResize(5)
	if (channel.switch_data && channel.switch_data.length > 0) {
		const line = Object.assign({ ...channel.switch_data[0] }, {
			line_type: 'middle',
			type: 'switch',
			coords: channel.coords,
			coord: channel.coords,
			min_vn_kv: channel.min_vn_kv,
			from_station: channel.from_station,
			to_station: channel.to_station,
			lineStyle: Object.assign({}, getGisStyle(channel.switch_data[0], apiZoom), {
				type: 'dashed'
			}),
			channel_name: channel.channel_name,
			line_number: 1
		})

		return line
	} else if (channel.line_data.length == 1) {
		const line = Object.assign({ ...channel.line_data[0] }, {
			line_type: 'middle',
			coords: channel.coords
		})
		lineList.push(line)
	} else if ([2, 3, 4, 5, 6].includes(channel.line_data.length)) {
		const coords = getTangentialPoint(channel.coords[0][0], channel.coords[0][1], (symbolR - getGisStyle({ vn_kv: channel.min_vn_kv }, apiZoom).width / 2) * geoScale, channel.coords[1][0], channel.coords[1][1], (symbolR - getGisStyle({ vn_kv: channel.min_vn_kv }, apiZoom).width / 2) * geoScale)
		const line1 = Object.assign({ ...channel.line_data[0] }, {
			line_type: 'boundary1',
			coords: coords[0]
		})
		const line2 = Object.assign({ ...channel.line_data[channel.line_data.length - 1] }, {
			line_type: 'boundary2',
			coords: coords[1]
		})
		lineList = lineList.concat([line1, line2])
		if (channel.line_data.length == 3 || channel.line_data.length == 5) {
			const line = Object.assign(channel.line_data.length == 3 ? { ...channel.line_data[1] } : { ...channel.line_data[2] }, {
				line_type: 'middle',
				coords: channel.coords
			})
			lineList.push(line)
			if (channel.line_data.length == 5) {
				const coords = getTangentialPoint(channel.coords[0][0], channel.coords[0][1], symbolR / 2 * geoScale, channel.coords[1][0], channel.coords[1][1], symbolR / 2 * geoScale)
				const line1 = Object.assign({ ...channel.line_data[1] }, {
					line_type: 'five1',
					coords: coords[0]
				})
				const line2 = Object.assign({ ...channel.line_data[3] }, {
					line_type: 'five2',
					coords: coords[1]
				})
				lineList = lineList.concat([line1, line2])
			}
		} else if (channel.line_data.length == 4) {
			const coords = getTangentialPoint(channel.coords[0][0], channel.coords[0][1], symbolR / 3 * geoScale, channel.coords[1][0], channel.coords[1][1], symbolR / 3 * geoScale)
			const line1 = Object.assign({ ...channel.line_data[1] }, {
				line_type: 'four1',
				coords: coords[0]
			})
			const line2 = Object.assign({ ...channel.line_data[2] }, {
				line_type: 'four2',
				coords: coords[1]
			})
			lineList = lineList.concat([line1, line2])
		} else if (channel.line_data.length == 6) {
			const coords1 = getTangentialPoint(channel.coords[0][0], channel.coords[0][1], symbolR / 5 * 3 * geoScale, channel.coords[1][0], channel.coords[1][1], symbolR / 5 * 3 * geoScale)
			const coords2 = getTangentialPoint(channel.coords[0][0], channel.coords[0][1], symbolR / 5 * geoScale, channel.coords[1][0], channel.coords[1][1], symbolR / 5 * geoScale)
			const line1 = Object.assign({ ...channel.line_data[1] }, {
				line_type: 'six1',
				coords: coords1[0]
			})
			const line2 = Object.assign({ ...channel.line_data[4] }, {
				line_type: 'six4',
				coords: coords1[1]
			})
			const line3 = Object.assign({ ...channel.line_data[2] }, {
				line_type: 'six2',
				coords: coords2[0]
			})
			const line4 = Object.assign({ ...channel.line_data[3] }, {
				line_type: 'six3',
				coords: coords2[1]
			})
			lineList = lineList.concat([line1, line2, line3, line4])
		}
	} else if (channel.line_data.length == 0) {
		return []
	} else {
		const line = Object.assign({}, {
			line_type: 'bolder',
			coords: channel.coords,
			line_data: channel.line_data,
			color: channel.color || '#000',
			line_types: channel.line_types || 'solid'
		})
		lineList.push(line)
	}
	return lineList.map(item => Object.assign({ ...item }, {
		coord: channel.coords,
		min_vn_kv: channel.min_vn_kv,
		from_station: channel.from_station,
		to_station: channel.to_station,
		lineStyle: type == 'result' ? {
			width: item.line_type == 'bolder' ? symbolR : getGisStyle(item, apiZoom).width,
			color: resultType == 'short_circuit' ? getGisStyle({ vn_kv: item.vn_kv }).color : undefined
		} : item.line_type == 'bolder' ? {
			color: type == 'draw' ? (channel.color || '#000') : '#000',
			width: symbolR,
			type: type == 'draw' ? (channel.line_types || 'solid') : 'solid'
		} : getGisStyle(type == 'edit' ? { vn_kv: item.vn_kv } : item, apiZoom),
		line_number: lineList.length
	},
	type == 'create' ? {
		line_types: 'solid',
		color: getGisStyle(item).color,
		channel_name: channel.from_station_name + '-' + channel.to_station_name
	} : {
		channel_name: channel.channel_name
	}, type == 'result' ? {
		value: (resultType == 'n_1' || resultType == 'n_2') ? (item.load_ratio_count) || '' : resultType == 'ac_power_flow' ? ((item.loading_percent || 0) / 100) || '' : undefined,
		itemStyle: {
			color: getGisStyle({ vn_kv: item.vn_kv }).color
		}
	} : {

	}
	))
}
export function handleChannel(data) {
	const result = []
	data.forEach(item => {
		const existing = result.find(item1 => item1.from_station === item.from_station && item1.to_station === item.to_station)
		if (existing) {
			existing.line_data.push({
				'index': item.index,
				'type': item.type,
				'color': item.color,
				'vn_kv': item.vn_kv,
				'line_types': item.line_types,
				'isSequence': item.isSequence
			})
		} else {
			if (item.line_data) {
				result.push({
					'channel_name': item.channel_name,
					'from_station': item.from_station,
					'to_station': item.to_station,
					'color': item.color,
					'line_types': item.line_types,
					'isSequence': item.isSequence,
					'line_data': item.line_data.map(item1 => {
						return {
							'index': item1.index,
							'type': item1.type,
							'vn_kv': item1.vn_kv
						}
					})
				})
			} else {
				result.push({
					'channel_name': item.channel_name,
					'from_station': item.from_station,
					'to_station': item.to_station,
					'line_data': [{
						'index': item.index,
						'type': item.type,
						'color': item.color,
						'vn_kv': item.vn_kv,
						'line_types': item.line_types,
						'isSequence': item.isSequence
					}]
				})
			}
		}
	})
	return result
}
export const centerOnLine = (start, end) => {
	var targetPoint = [(start[0] + end[0]) / 2, (start[1] + end[1]) / 2]
	return targetPoint
}
