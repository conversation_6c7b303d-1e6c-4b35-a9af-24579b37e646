import { saveAs } from "file-saver";
import message from "@/utils/message";
import i18n from "@/i18n";
export function t(name) {
  return i18n.global.t(name);
}
export function removePrefixSuffix(filename) {
  return filename.replace(/^Result_/, "").replace(/\..*$/, "");
}
export const decodeURIComponentInit = (str) => {
  const tempStr = str.replace(/%(?![0-9a-fA-F]{2})/g, "%25");
  return decodeURIComponent(tempStr);
};
export const downloadApiFile = ({ data, headers }) => {
  if (!headers["content-disposition"]) {
    const reader = new FileReader();

    reader.onload = function (event) {
      const jsonString = event.target.result;
      const jsonData = JSON.parse(jsonString);
      message.error(jsonData.message);
    };
    reader.readAsText(data);
    return;
  }
  const fileName = decodeURIComponentInit(
    headers["content-disposition"].split(";")[1].split("=")[1]
  );
  saveAs(data, fileName);
};

export const fileBlobFun = (data, fileName) => {
  const blob = new Blob([data], {
    type: "application/vnd.ms-excel",
  });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.style.display = "none";
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

export const transformColor = (hex) => {
  hex = hex.replace(/^#/, "");

  const redHex = hex.slice(0, 2);
  const greenHex = hex.slice(2, 4);
  const blueHex = hex.slice(4, 6);

  const redDecimal = parseInt(redHex, 16);
  const greenDecimal = parseInt(greenHex, 16);
  const blueDecimal = parseInt(blueHex, 16);

  const rgbColor = `${redDecimal},${greenDecimal},${blueDecimal}`;
  return rgbColor;
};

export const randomColor = () => {
  const r = Math.floor(Math.random() * 256);
  const g = Math.floor(Math.random() * 256);
  const b = Math.floor(Math.random() * 256);
  const color = "#" + r.toString(16) + g.toString(16) + b.toString(16);
  return color;
};

export const randomRgbaColor = () => {
  var r = Math.floor(Math.random() * 256);
  var g = Math.floor(Math.random() * 256);
  var b = Math.floor(Math.random() * 256);
  var alpha = Math.random();
  return `rgb(${r},${g},${b},${alpha})`;
};
export const getDayOption = () => {
  const monthData = Array.from({ length: 12 }, (_, monthIndex) => {
    const month = (monthIndex + 1).toString().padStart(2, "0");
    const daysCount = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][
      monthIndex
    ];
    return {
      value: month,
      label: `${month}` + t("Month"),
      children: Array.from({ length: daysCount }, (_, dayIndex) => {
        const day = (dayIndex + 1).toString().padStart(2, "0");
        return {
          label: `${day}` + t("Day"),
          value: `${month}-${day}`,
        };
      }),
    };
  });
  return monthData;
};
